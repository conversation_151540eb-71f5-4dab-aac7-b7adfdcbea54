package indigo;

import java.util.Map;

import Message.S2CBackpackMsg.PropInfo;
import Message.S2CIndigoMsg.IndigoStage;
import Message.S2CIndigoMsg.RaceStatus;
import Message.S2CPlayerMsg.PromptType;
import Message.Inner.GRCrossIndigo.GRRewardRaceReq;
import Message.Inner.GRCrossIndigo.GRRewardRaceRsp;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SingleRace;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;

/**
 * 领取赛场奖励
 * <AUTHOR>
 *
 */
public class RewardRaceScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		GRRewardRaceReq req = (GRRewardRaceReq) args.get(Key.ARG2);
		rewardRace(serverId, req);
		return null;
	}

	/**
	 * 领取赛场奖励
	 * 
	 * @param player
	 * @param raceId
	 */
	public void rewardRace(int serverId, GRRewardRaceReq req) {
		IndigoService indigoService = IndigoService.getInstance();
		Participant participant = indigoService.getParticipantByPlayerId(req.getPlayerId());
		if (participant == null) {
			 GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 296);// 未报名参赛
			return;
		}
		if (indigoService.getIndigoStage() == IndigoStage.INDIGO_APPLAY_VALUE
				|| indigoService.getIndigoStage() == IndigoStage.INDIGO_READY_VALUE) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 295);// 不是比赛阶段无法领取奖励
			return;
		}
		if (indigoService.getIndigoStage() == IndigoStage.INDIGO_IDLE_VALUE) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 297);// 未领取奖励在比赛结束已通过邮件发送
			return;
		}
		if (!participant.getMyRaceList().contains(req.getRaceId())) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 298);// 自己不存在该场比赛
			return;
		}
		int index = 0;
		for (String rId : participant.getMyRaceList()) {
			if (rId.equals(req.getRaceId())) {
				break;
			}
			index++;
		}
		int session = index + 1;
		Integer groupId = ServerService.getInstance().getGroupIdByServer(participant.getServerId());
		SingleRace singleRace = indigoService.findCurRaceById(req.getRaceId(), session, groupId);
		if (singleRace.getStatus() != RaceStatus.RACE_END_VALUE) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 291);// 比赛未结束，不能领取奖励
			return;
		}
		boolean canReward = false;
		if (req.getPlayerId() == singleRace.getRedId()) {
			canReward = singleRace.isRedReward();
		} else if (req.getPlayerId() == singleRace.getBlueId()) {
			canReward = singleRace.isBlueReward();
		}
		if (!canReward) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 299);// 已领取
			return;
		}
		// 发送奖励
		GRRewardRaceRsp.Builder builder = GRRewardRaceRsp.newBuilder();
		// 奖励列表
		Map<Integer, Integer> raceAward = indigoService.getCrossIndigoSingleRaceAward(singleRace.getWinner() == req.getPlayerId());
		PropInfo.Builder prop;
		for (Integer key : raceAward.keySet()) {
			prop = PropInfo.newBuilder();
			prop.setId(key);
			prop.setNum(raceAward.get(key));
			builder.addItems(prop);
		} 
		builder.setPlayerId(req.getPlayerId());
		// 设置已领取标识
		if (req.getPlayerId() == singleRace.getRedId()) {
			singleRace.setRedReward(false);
		} else if (req.getPlayerId() == singleRace.getBlueId()) {
			singleRace.setBlueReward(false);
		}
		builder.setRaceId(req.getRaceId());
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(req.getPlayerId(), GRRewardRaceRsp.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}

}
