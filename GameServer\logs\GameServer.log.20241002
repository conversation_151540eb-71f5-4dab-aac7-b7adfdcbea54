[2024-10-02 00:00:01:192 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:200 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:207 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:215 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:222 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:231 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:238 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:245 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:253 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:261 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:282 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:290 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:298 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:305 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:314 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:334 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:342 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:350 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:359 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:369 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:376 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:388 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:396 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:403 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:411 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:419 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:427 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:434 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:442 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:464 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:472 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:490 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:499 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:508 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:519 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:530 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:539 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:549 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:558 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:566 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:576 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:591 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:600 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:615 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:625 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:636 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:649 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:658 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:669 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:679 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:701 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:711 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:719 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:729 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:739 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:748 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:758 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:770 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:784 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:795 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:804 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:813 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:823 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:850 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:861 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:871 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:885 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:896 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:909 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:918 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:929 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:946 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:960 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:974 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:986 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:01:998 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:015 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:042 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:069 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:083 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:090 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:098 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:118 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:125 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:134 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:141 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:157 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:177 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:184 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:192 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:199 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:209 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:218 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:225 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:233 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:242 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:249 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:257 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:265 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:274 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:281 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:289 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:297 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:314 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:344 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:352 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:360 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:370 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:378 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:386 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:393 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:401 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:418 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:432 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:441 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:450 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:461 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:487 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:498 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:508 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:518 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:542 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:549 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:561 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:578 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:585 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:600 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:612 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:621 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:629 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:644 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:660 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:676 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:683 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:690 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:697 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:704 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:713 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:740 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:748 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:755 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:762 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:778 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:785 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:808 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:816 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:823 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:831 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:838 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:846 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:853 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:860 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:868 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:876 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:883 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:891 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:898 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:907 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:914 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:921 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:929 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:937 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:955 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:972 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:980 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:987 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:02:995 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:002 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:009 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:017 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:024 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:040 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:047 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:054 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:063 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:070 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:077 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:084 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:091 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:098 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:106 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:126 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:134 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:142 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:149 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:156 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:164 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:171 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:178 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:185 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:192 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:199 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:217 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:224 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:234 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:241 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:248 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:256 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:271 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:289 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:297 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:304 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:311 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:319 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:333 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:341 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:349 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:360 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:377 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:393 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:400 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:408 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:425 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:433 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:440 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:447 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:457 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:466 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:475 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:483 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:490 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:498 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:506 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:515 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:522 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:530 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:548 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:567 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:574 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:581 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:604 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:612 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:623 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:630 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:656 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:663 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:678 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:685 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:693 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:702 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:720 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:728 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:735 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:745 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:761 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:769 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:776 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:783 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:791 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:805 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:813 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:821 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:842 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:850 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:857 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:03:865 INFO](RechargeActivityOpenInfo.java:138)充值活动错误
[2024-10-02 00:00:04:854 INFO](SDKHttpClient.java:435)request remote http url: http://111.229.36.212:8501/server/getRebatelist
[2024-10-02 00:02:45:911 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3581MB usedMemory:515MB
[2024-10-02 00:07:45:911 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3537MB usedMemory:559MB
[2024-10-02 00:11:23:473 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： dc6c49e6> ]
[2024-10-02 00:11:23:473 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============dc6c49e6> ]
[2024-10-02 00:11:23:474 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====dc6c49e6> ]
[2024-10-02 00:11:27:109 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============dc6c49e6> ]
[2024-10-02 00:11:27:718 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： be3bf8dc> ]
[2024-10-02 00:11:28:066 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887599
[2024-10-02 00:11:28:089 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887599, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 00:11:28:090 ERROR](RechargeFinishScript.java:269)[ 玩家 ********* 的充值商品:1305 订单号： 536887599 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 00:12:45:911 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3733MB usedMemory:363MB
[2024-10-02 00:17:38:412 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： be3bf8dc> ]
[2024-10-02 00:17:38:412 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============be3bf8dc> ]
[2024-10-02 00:17:38:412 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====be3bf8dc> ]
[2024-10-02 00:17:45:912 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3667MB usedMemory:429MB
[2024-10-02 00:17:48:414 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============be3bf8dc> ]
[2024-10-02 00:22:45:912 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3639MB usedMemory:457MB
[2024-10-02 00:27:45:913 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3611MB usedMemory:485MB
[2024-10-02 00:32:27:468 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 06db52c2> ]
[2024-10-02 00:32:45:926 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3465MB usedMemory:631MB
[2024-10-02 00:34:21:591 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 00:35:01:111 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12101_30000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:36:21:591 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 21 , 耗时： 2
[2024-10-02 00:37:45:926 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3402MB usedMemory:694MB
[2024-10-02 00:38:21:590 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 1
[2024-10-02 00:38:38:917 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[8],开始丢给RechargeProcessor处理
[2024-10-02 00:38:38:917 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 00:38:38:917 ERROR](RechargeFinishScript.java:81)收到充值商品:8 订单信息:[ 支付类型:1 , 订单号: 536887632, 充值金额: 2000.0充值个数:1 ]
[2024-10-02 00:38:38:925 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:8 ] 充值完成，订单状态修改为 4！
[2024-10-02 00:40:21:592 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 12 , 耗时： 3
[2024-10-02 00:42:21:591 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 2
[2024-10-02 00:42:45:926 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3746MB usedMemory:350MB
[2024-10-02 00:43:59:763 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 06db52c2> ]
[2024-10-02 00:43:59:764 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============06db52c2> ]
[2024-10-02 00:43:59:764 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====06db52c2> ]
[2024-10-02 00:43:59:835 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============06db52c2> ]
[2024-10-02 00:44:05:085 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14061_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:44:05:085 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 00:44:17:987 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14062_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:44:17:988 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 00:44:21:592 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 3
[2024-10-02 00:44:27:490 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14063_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:44:27:490 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 00:44:35:306 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14064_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:44:35:306 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 00:44:50:271 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： e6a6002a> ]
[2024-10-02 00:46:21:593 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 16 , 耗时： 4
[2024-10-02 00:46:29:914 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1001],开始丢给RechargeProcessor处理
[2024-10-02 00:46:29:914 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 00:46:29:914 ERROR](RechargeFinishScript.java:81)收到充值商品:1001 订单信息:[ 支付类型:1 , 订单号: 536887633, 充值金额: 88.0充值个数:1 ]
[2024-10-02 00:46:29:926 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1001 ] 充值完成，订单状态修改为 4！
[2024-10-02 00:46:29:926 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 00:47:32:697 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:11
[2024-10-02 00:47:45:927 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3497MB usedMemory:599MB
[2024-10-02 00:48:21:591 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 2
[2024-10-02 00:49:26:475 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:12
[2024-10-02 00:49:55:072 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:12
[2024-10-02 00:50:13:560 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:12
[2024-10-02 00:52:45:927 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3427MB usedMemory:669MB
[2024-10-02 00:53:47:767 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:11
[2024-10-02 00:55:15:744 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:17
[2024-10-02 00:56:04:547 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[401],开始丢给RechargeProcessor处理
[2024-10-02 00:56:04:547 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 00:56:04:547 ERROR](RechargeFinishScript.java:81)收到充值商品:401 订单信息:[ 支付类型:1 , 订单号: 536887634, 充值金额: 6.0充值个数:1 ]
[2024-10-02 00:56:04:560 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:401 ] 充值完成，订单状态修改为 4！
[2024-10-02 00:56:04:560 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 00:56:21:592 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 6 , 耗时： 3
[2024-10-02 00:56:46:087 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14257_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:57:08:108 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:87
[2024-10-02 00:57:08:184 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 251 , 耗时： 81
[2024-10-02 00:57:08:202 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 251 , 耗时： 92
[2024-10-02 00:57:08:213 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 251 , 耗时： 103
[2024-10-02 00:57:45:928 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3725MB usedMemory:371MB
[2024-10-02 00:57:56:564 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:3990
[2024-10-02 00:58:21:647 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 249 , 耗时： 57
[2024-10-02 00:59:31:494 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10088_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 00:59:56:615 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:24
[2024-10-02 01:00:32:698 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536886164","productid":431,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:00:32:698 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[431],开始丢给RechargeProcessor处理
[2024-10-02 01:00:32:717 ERROR](RechargeFinishScript.java:81)收到充值商品:431 订单信息:[ 支付类型:3 , 订单号: 536887635, 充值金额: 98.0充值个数:1 ]
[2024-10-02 01:00:32:727 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:431 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:00:32:727 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887635 ， 玩家 : 536886164 ]
[2024-10-02 01:00:32:727 ERROR](RechargeService.java:442)订单[536887635] 脚本支付流程！
[2024-10-02 01:00:32:738 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 01:02:02:323 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14257_500"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:02:21:597 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 2
[2024-10-02 01:02:45:928 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3814MB usedMemory:282MB
[2024-10-02 01:02:57:138 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10088_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:03:13:478 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:2440
[2024-10-02 01:03:46:069 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10090_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:04:09:224 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:2448
[2024-10-02 01:04:21:597 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 01:04:55:694 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： e6a6002a> ]
[2024-10-02 01:04:55:695 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e6a6002a> ]
[2024-10-02 01:04:55:696 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====e6a6002a> ]
[2024-10-02 01:04:55:771 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e6a6002a> ]
[2024-10-02 01:05:10:712 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： bf07e01e> ]
[2024-10-02 01:06:08:613 ERROR](CommandProcessor.java:174)处理消息[1282,command:game.server.logic.section.handler.SweepSectionHandler]耗时:11
[2024-10-02 01:07:45:928 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3709MB usedMemory:387MB
[2024-10-02 01:07:52:174 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13004_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:08:00:830 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13015_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:08:09:094 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13026_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:17:556 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13005_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:23:625 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13006_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:33:434 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13017_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:40:131 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13027_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:48:161 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13028_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:50:085 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel exceptionCaught ==============java.io.IOException: Connection reset by peer> ]
[2024-10-02 01:09:50:085 INFO](NettyGameServer.java:349)--->[Login] exceptionCaught, close sessionId:bf07e01e,cause:Connection reset by peer
[2024-10-02 01:09:50:087 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： bf07e01e> ]
[2024-10-02 01:09:50:087 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============bf07e01e> ]
[2024-10-02 01:09:59:159 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13017_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:09:59:159 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:10:02:799 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： c1ca4ed1> ]
[2024-10-02 01:10:37:099 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13016_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:12:15:966 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： c1ca4ed1> ]
[2024-10-02 01:12:15:967 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============c1ca4ed1> ]
[2024-10-02 01:12:15:967 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====c1ca4ed1> ]
[2024-10-02 01:12:16:036 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============c1ca4ed1> ]
[2024-10-02 01:12:16:475 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 8d36eae0> ]
[2024-10-02 01:12:24:021 ERROR](CommandProcessor.java:174)处理消息[774,command:game.server.logic.hero.handler.HeroSkillLvlUpHandler]耗时:19
[2024-10-02 01:12:45:929 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3790MB usedMemory:306MB
[2024-10-02 01:14:24:502 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536886164","productid":432,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:14:24:503 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[432],开始丢给RechargeProcessor处理
[2024-10-02 01:14:24:521 ERROR](RechargeFinishScript.java:81)收到充值商品:432 订单信息:[ 支付类型:3 , 订单号: 536887636, 充值金额: 328.0充值个数:1 ]
[2024-10-02 01:14:24:531 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:432 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:14:24:531 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887636 ， 玩家 : 536886164 ]
[2024-10-02 01:14:24:531 ERROR](RechargeService.java:442)订单[536887636] 脚本支付流程！
[2024-10-02 01:14:24:543 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 01:16:21:602 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 7 , 耗时： 3
[2024-10-02 01:17:37:337 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1012],开始丢给RechargeProcessor处理
[2024-10-02 01:17:37:337 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:17:37:338 ERROR](RechargeFinishScript.java:81)收到充值商品:1012 订单信息:[ 支付类型:1 , 订单号: 536887637, 充值金额: 198.0充值个数:1 ]
[2024-10-02 01:17:37:351 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1012 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:17:37:351 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 01:17:39:658 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1013],开始丢给RechargeProcessor处理
[2024-10-02 01:17:39:658 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:17:39:658 ERROR](RechargeFinishScript.java:81)收到充值商品:1013 订单信息:[ 支付类型:1 , 订单号: 536887638, 充值金额: 328.0充值个数:1 ]
[2024-10-02 01:17:39:670 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1013 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:17:39:670 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 01:17:42:098 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1011],开始丢给RechargeProcessor处理
[2024-10-02 01:17:42:098 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:17:42:098 ERROR](RechargeFinishScript.java:81)收到充值商品:1011 订单信息:[ 支付类型:1 , 订单号: 536887639, 充值金额: 98.0充值个数:1 ]
[2024-10-02 01:17:42:111 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1011 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:17:42:111 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 01:17:45:929 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3699MB usedMemory:397MB
[2024-10-02 01:17:46:922 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1015],开始丢给RechargeProcessor处理
[2024-10-02 01:17:46:922 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:17:46:923 ERROR](RechargeFinishScript.java:81)收到充值商品:1015 订单信息:[ 支付类型:1 , 订单号: 536887640, 充值金额: 648.0充值个数:1 ]
[2024-10-02 01:17:46:936 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1015 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:17:46:936 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 01:17:50:352 ERROR](CommandProcessor.java:174)处理消息[1204,command:game.server.logic.fund.handler.ReqResShopRewardHandler]耗时:13
[2024-10-02 01:18:21:601 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 2
[2024-10-02 01:19:16:040 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 8d36eae0> ]
[2024-10-02 01:19:16:041 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============8d36eae0> ]
[2024-10-02 01:19:16:041 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====8d36eae0> ]
[2024-10-02 01:19:16:117 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============8d36eae0> ]
[2024-10-02 01:19:48:587 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 406cb4ea> ]
[2024-10-02 01:20:21:600 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 01:20:29:796 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10145_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:21:29:754 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_50000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:21:53:111 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:21:57:669 ERROR](CommandProcessor.java:174)处理消息[3073,command:game.server.logic.mail.handler.ReqGetMailAdjunctHandler]耗时:12
[2024-10-02 01:22:18:808 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:22:40:361 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"clearbag","playerid":"536886164","token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 10180
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 13001
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 13034
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 19002
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 15001
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 14161
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 10097
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 13023
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 10210
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 14216
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 14278
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 13057
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 14181
[2024-10-02 01:22:40:361 ERROR](Item.java:124)道具不存在或不可出售: 10159
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14180
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 13056
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14192
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10108
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14182
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14212
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10144
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14160
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10061
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14101
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10157
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10142
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10143
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10141
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 15000
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10063
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14218
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10036
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 10148
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14215
[2024-10-02 01:22:40:362 ERROR](Item.java:124)道具不存在或不可出售: 14285
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10147
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 13013
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 13034
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 14188
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10075
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10070
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10079
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10077
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10071
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10078
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10073
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10074
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10066
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 14146
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 10100
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 19001
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 14185
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 13034
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 13034
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 13012
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 13002
[2024-10-02 01:22:40:363 ERROR](Item.java:124)道具不存在或不可出售: 14257
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13026
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13015
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13004
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13017
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13028
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13027
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13006
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13005
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 13016
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 10094
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 10092
[2024-10-02 01:22:40:364 ERROR](Item.java:124)道具不存在或不可出售: 10145
[2024-10-02 01:22:40:364 ERROR](GameHttpNettyNewServerImpl.java:208)items data: [{"id":10180,"num":1,"numMax":99999,"sellPrice":0},{"id":13001,"num":58782,"numMax":99999,"sellPrice":0},{"id":13034,"num":99999,"numMax":99999,"sellPrice":0},{"id":19002,"num":46,"numMax":99999,"sellPrice":0},{"id":15001,"num":50,"numMax":99999,"sellPrice":0},{"id":14161,"num":239,"numMax":99999,"sellPrice":0},{"id":10097,"num":45,"numMax":99999,"sellPrice":0},{"id":13023,"num":58705,"numMax":99999,"sellPrice":0},{"id":10210,"num":31184,"numMax":99999,"sellPrice":0},{"id":14216,"num":1,"numMax":99999,"sellPrice":0},{"id":14278,"num":11,"numMax":99999,"sellPrice":0},{"id":13057,"num":102,"numMax":99999,"sellPrice":0},{"id":14181,"num":9,"numMax":99999,"sellPrice":0},{"id":10159,"num":4,"numMax":99999,"sellPrice":0},{"id":14180,"num":66,"numMax":99999,"sellPrice":0},{"id":13056,"num":60,"numMax":99999,"sellPrice":0},{"id":14192,"num":4,"numMax":99999,"sellPrice":0},{"id":10108,"num":2150,"numMax":99999,"sellPrice":0},{"id":14182,"num":10,"numMax":99999,"sellPrice":0},{"id":14212,"num":91,"numMax":99999,"sellPrice":0},{"id":10144,"num":2,"numMax":99999,"sellPrice":0},{"id":14160,"num":15132,"numMax":99999,"sellPrice":0},{"id":10061,"num":8,"numMax":99999,"sellPrice":0},{"id":14101,"num":700,"numMax":99999,"sellPrice":0},{"id":10157,"num":4,"numMax":99999,"sellPrice":0},{"id":10142,"num":7,"numMax":99999,"sellPrice":0},{"id":10143,"num":8,"numMax":99999,"sellPrice":0},{"id":10141,"num":3,"numMax":99999,"sellPrice":0},{"id":15000,"num":1,"numMax":99999,"sellPrice":0},{"id":10063,"num":65,"numMax":99999,"sellPrice":0},{"id":14218,"num":65,"numMax":99999,"sellPrice":0},{"id":10036,"num":1,"numMax":99999,"sellPrice":0},{"id":10148,"num":2,"numMax":99999,"sellPrice":0},{"id":14215,"num":18,"numMax":99999,"sellPrice":0},{"id":14285,"num":7279,"numMax":99999,"sellPrice":0},{"id":10147,"num":6,"numMax":99999,"sellPrice":0},{"id":13013,"num":2,"numMax":99999,"sellPrice":0},{"id":13034,"num":99999,"numMax":99999,"sellPrice":0},{"id":14188,"num":1,"numMax":99999,"sellPrice":0},{"id":10075,"num":2,"numMax":99999,"sellPrice":0},{"id":10070,"num":3,"numMax":99999,"sellPrice":0},{"id":10079,"num":2,"numMax":99999,"sellPrice":0},{"id":10077,"num":1,"numMax":99999,"sellPrice":0},{"id":10071,"num":1,"numMax":99999,"sellPrice":0},{"id":10078,"num":1,"numMax":99999,"sellPrice":0},{"id":10073,"num":2,"numMax":99999,"sellPrice":0},{"id":10074,"num":2,"numMax":99999,"sellPrice":0},{"id":10066,"num":1,"numMax":99999,"sellPrice":0},{"id":14146,"num":1,"numMax":99999,"sellPrice":0},{"id":10100,"num":89,"numMax":99999,"sellPrice":0},{"id":19001,"num":18,"numMax":99999,"sellPrice":0},{"id":14185,"num":9018,"numMax":99999,"sellPrice":0},{"id":13034,"num":99999,"numMax":99999,"sellPrice":0},{"id":13034,"num":49549,"numMax":99999,"sellPrice":0},{"id":13012,"num":58553,"numMax":99999,"sellPrice":0},{"id":13002,"num":5,"numMax":99999,"sellPrice":0},{"id":14257,"num":499,"numMax":99999,"sellPrice":0},{"id":13026,"num":49912,"numMax":99999,"sellPrice":0},{"id":13015,"num":49956,"numMax":99999,"sellPrice":0},{"id":13004,"num":49956,"numMax":99999,"sellPrice":0},{"id":13017,"num":9894,"numMax":99999,"sellPrice":0},{"id":13028,"num":4792,"numMax":99999,"sellPrice":0},{"id":13027,"num":4742,"numMax":99999,"sellPrice":0},{"id":13006,"num":4896,"numMax":99999,"sellPrice":0},{"id":13005,"num":4872,"numMax":99999,"sellPrice":0},{"id":13016,"num":4872,"numMax":99999,"sellPrice":0},{"id":10094,"num":10,"numMax":99999,"sellPrice":0},{"id":10092,"num":15,"numMax":99999,"sellPrice":0},{"id":10145,"num":49500,"numMax":99999,"sellPrice":0}]
[2024-10-02 01:22:45:930 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3409MB usedMemory:687MB
[2024-10-02 01:24:21:648 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 30 , 耗时： 45
[2024-10-02 01:26:21:603 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 7 , 耗时： 2
[2024-10-02 01:27:45:930 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3850MB usedMemory:246MB
[2024-10-02 01:28:21:603 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 2
[2024-10-02 01:29:47:432 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-8_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:31:42:427 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 406cb4ea> ]
[2024-10-02 01:31:42:427 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============406cb4ea> ]
[2024-10-02 01:31:46:753 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： a1f0a572> ]
[2024-10-02 01:32:21:606 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 12 , 耗时： 5
[2024-10-02 01:32:45:930 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3564MB usedMemory:532MB
[2024-10-02 01:33:38:183 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： a1f0a572> ]
[2024-10-02 01:33:38:183 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============a1f0a572> ]
[2024-10-02 01:33:41:009 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 40e8ed68> ]
[2024-10-02 01:34:21:603 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 01:35:53:569 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 40e8ed68> ]
[2024-10-02 01:35:53:569 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============40e8ed68> ]
[2024-10-02 01:35:53:570 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====40e8ed68> ]
[2024-10-02 01:36:00:348 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12051_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:36:00:348 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:36:02:734 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============40e8ed68> ]
[2024-10-02 01:36:02:847 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 86e025f8> ]
[2024-10-02 01:36:21:603 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 01:37:45:931 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3701MB usedMemory:395MB
[2024-10-02 01:38:19:656 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14100_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:38:21:603 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 01:38:26:596 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14113_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:40:20:029 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:17
[2024-10-02 01:40:21:608 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 32 , 耗时： 7
[2024-10-02 01:41:15:973 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:18
[2024-10-02 01:42:45:931 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3493MB usedMemory:603MB
[2024-10-02 01:43:03:628 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 86e025f8> ]
[2024-10-02 01:43:03:629 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============86e025f8> ]
[2024-10-02 01:43:03:629 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====86e025f8> ]
[2024-10-02 01:43:13:630 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============86e025f8> ]
[2024-10-02 01:43:17:127 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536886164","productid":1015,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:43:17:135 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1015],开始丢给RechargeProcessor处理
[2024-10-02 01:43:17:142 ERROR](UpdateRechargeBeanScript.java:52)角色不在线，离线充值: 536887641
[2024-10-02 01:43:17:142 ERROR](RechargeService.java:442)订单[536887641] 脚本支付流程！
[2024-10-02 01:43:17:155 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 01:43:44:058 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536886164","productid":49,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:43:44:066 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[49],开始丢给RechargeProcessor处理
[2024-10-02 01:43:44:073 ERROR](UpdateRechargeBeanScript.java:52)角色不在线，离线充值: 536887642
[2024-10-02 01:43:44:073 ERROR](RechargeService.java:442)订单[536887642] 脚本支付流程！
[2024-10-02 01:43:44:084 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 01:43:46:787 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： f9fcbced> ]
[2024-10-02 01:43:47:029 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887641
[2024-10-02 01:43:47:029 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887642
[2024-10-02 01:43:47:055 ERROR](RechargeFinishScript.java:81)收到充值商品:1015 订单信息:[ 支付类型:3 , 订单号: 536887641, 充值金额: 648.0充值个数:1 ]
[2024-10-02 01:43:47:064 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1015 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:43:47:091 ERROR](RechargeFinishScript.java:81)收到充值商品:49 订单信息:[ 支付类型:3 , 订单号: 536887642, 充值金额: 64800.0充值个数:1 ]
[2024-10-02 01:43:47:092 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:49 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:44:13:825 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[558],开始丢给RechargeProcessor处理
[2024-10-02 01:44:13:825 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:44:13:826 ERROR](RechargeFinishScript.java:81)收到充值商品:558 订单信息:[ 支付类型:1 , 订单号: 536887643, 充值金额: 2000.0充值个数:1 ]
[2024-10-02 01:44:13:839 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:558 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:44:13:839 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 01:44:21:607 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 5
[2024-10-02 01:44:25:326 ERROR](BackpackService.java:1463)N选1宝箱：14212领取索引超过掉落索引
[2024-10-02 01:44:27:265 ERROR](BackpackService.java:1463)N选1宝箱：14212领取索引超过掉落索引
[2024-10-02 01:44:38:135 INFO](GetJourneyDiamondsScript.java:79)钻石已领取: 2, player: 536886164
[2024-10-02 01:45:06:175 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1002],开始丢给RechargeProcessor处理
[2024-10-02 01:45:06:176 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:45:06:176 ERROR](RechargeFinishScript.java:81)收到充值商品:1002 订单信息:[ 支付类型:1 , 订单号: 536887644, 充值金额: 388.0充值个数:1 ]
[2024-10-02 01:45:06:183 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1002 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:45:26:197 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： f9fcbced> ]
[2024-10-02 01:45:26:197 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============f9fcbced> ]
[2024-10-02 01:45:31:156 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： b17d581f> ]
[2024-10-02 01:45:41:081 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[405],开始丢给RechargeProcessor处理
[2024-10-02 01:45:41:081 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 01:45:41:081 ERROR](RechargeFinishScript.java:81)收到充值商品:405 订单信息:[ 支付类型:1 , 订单号: 536887645, 充值金额: 648.0充值个数:1 ]
[2024-10-02 01:45:41:095 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:405 ] 充值完成，订单状态修改为 4！
[2024-10-02 01:45:41:096 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:15
[2024-10-02 01:45:48:639 ERROR](CommandProcessor.java:174)处理消息[529,command:game.server.logic.player.handler.GetRechargeActivityRewardHandler]耗时:18
[2024-10-02 01:46:21:605 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 20 , 耗时： 4
[2024-10-02 01:47:45:932 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3668MB usedMemory:428MB
[2024-10-02 01:49:02:077 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:15
[2024-10-02 01:50:21:604 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 1
[2024-10-02 01:51:45:673 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： b17d581f> ]
[2024-10-02 01:51:45:674 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b17d581f> ]
[2024-10-02 01:51:45:674 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====b17d581f> ]
[2024-10-02 01:51:55:676 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b17d581f> ]
[2024-10-02 01:52:26:969 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"18007_9"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:52:26:969 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:52:41:778 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"19009_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:52:41:779 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:52:45:932 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3509MB usedMemory:587MB
[2024-10-02 01:53:39:659 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12112_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:53:39:673 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:53:44:276 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 2b6833e4> ]
[2024-10-02 01:54:21:604 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 01:56:21:604 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 9 , 耗时： 2
[2024-10-02 01:56:42:925 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 2b6833e4> ]
[2024-10-02 01:56:42:925 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============2b6833e4> ]
[2024-10-02 01:56:42:925 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====2b6833e4> ]
[2024-10-02 01:56:52:927 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============2b6833e4> ]
[2024-10-02 01:56:56:835 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12125_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:56:56:835 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:57:10:451 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12106_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:57:10:452 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:57:34:021 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12111_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 01:57:34:021 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 01:57:36:670 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 9df1772f> ]
[2024-10-02 01:57:45:933 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3652MB usedMemory:444MB
[2024-10-02 01:58:21:604 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 02:00:21:604 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 02:02:45:933 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3607MB usedMemory:489MB
[2024-10-02 02:03:18:585 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[1305],开始丢给RechargeProcessor处理
[2024-10-02 02:03:18:585 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 02:03:18:585 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:1 , 订单号: 536887646, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 02:03:18:596 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:1305 ] 充值完成，订单状态修改为 4！
[2024-10-02 02:03:18:596 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 02:03:31:865 ERROR](CommandProcessor.java:174)处理消息[10495,command:game.server.logic.crossIndigo.handler.InnerCrossIndigoResponseHandler]耗时:12
[2024-10-02 02:03:34:145 INFO](CrossIndigoService.java:4280)------------>   sendMsgId { 10248 }
[2024-10-02 02:03:40:225 INFO](CrossIndigoService.java:4280)------------>   sendMsgId { 10248 }
[2024-10-02 02:03:41:490 INFO](CrossIndigoService.java:4280)------------>   sendMsgId { 10248 }
[2024-10-02 02:04:21:606 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 10 , 耗时： 2
[2024-10-02 02:04:31:945 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 9df1772f> ]
[2024-10-02 02:04:31:946 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9df1772f> ]
[2024-10-02 02:04:31:946 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====9df1772f> ]
[2024-10-02 02:04:41:948 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9df1772f> ]
[2024-10-02 02:04:50:611 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 02:04:50:611 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 02:04:56:209 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 02:04:56:209 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 02:05:01:529 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 02:05:01:530 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 02:05:06:048 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 00ee5450> ]
[2024-10-02 02:07:45:933 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3906MB usedMemory:190MB
[2024-10-02 02:08:21:607 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 2
[2024-10-02 02:10:21:608 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 16 , 耗时： 3
[2024-10-02 02:11:40:215 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10068_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 02:12:45:934 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3639MB usedMemory:457MB
[2024-10-02 02:15:38:125 ERROR](CommandProcessor.java:174)处理消息[1282,command:game.server.logic.section.handler.SweepSectionHandler]耗时:18
[2024-10-02 02:16:21:099 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 00ee5450> ]
[2024-10-02 02:16:21:100 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============00ee5450> ]
[2024-10-02 02:17:45:934 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3786MB usedMemory:310MB
[2024-10-02 02:22:45:935 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3758MB usedMemory:338MB
[2024-10-02 02:24:13:920 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x706b1509, /*************:64897 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 02:24:14:108 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============706b1509> ]
[2024-10-02 02:27:45:935 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3730MB usedMemory:366MB
[2024-10-02 02:32:45:935 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3702MB usedMemory:394MB
[2024-10-02 02:37:45:937 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3676MB usedMemory:420MB
[2024-10-02 02:42:35:978 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 9575abcf> ]
[2024-10-02 02:42:45:937 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3411MB usedMemory:685MB
[2024-10-02 02:43:10:982 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10061_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 02:43:31:674 ERROR](CommandProcessor.java:174)处理消息[517,command:game.server.logic.player.handler.ReqDiamondExchangeHandler]耗时:23
[2024-10-02 02:44:21:266 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-4_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 02:44:21:623 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 4
[2024-10-02 02:46:17:902 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:22
[2024-10-02 02:47:45:937 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3858MB usedMemory:238MB
[2024-10-02 02:48:03:609 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:15
[2024-10-02 02:52:45:938 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3795MB usedMemory:301MB
[2024-10-02 02:54:22:959 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:31
[2024-10-02 02:57:45:938 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3738MB usedMemory:358MB
[2024-10-02 03:01:00:782 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:25
[2024-10-02 03:01:38:135 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:12
[2024-10-02 03:02:45:939 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3677MB usedMemory:419MB
[2024-10-02 03:05:06:971 INFO](GetJourneyDiamondsScript.java:79)钻石已领取: 2, player: 536886164
[2024-10-02 03:05:08:593 INFO](GetJourneyDiamondsScript.java:79)钻石已领取: 2, player: 536886164
[2024-10-02 03:05:22:508 INFO](GetJourneyDiamondsScript.java:79)钻石已领取: 2, player: 536886164
[2024-10-02 03:06:05:494 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:28
[2024-10-02 03:06:21:627 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 6 , 耗时： 2
[2024-10-02 03:06:43:791 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:19
[2024-10-02 03:07:41:954 ERROR](CommandProcessor.java:174)处理消息[6672,command:game.server.logic.research.handler.ReqDecomOnekeyHandler]耗时:11
[2024-10-02 03:07:45:939 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3559MB usedMemory:537MB
[2024-10-02 03:08:40:627 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 9575abcf> ]
[2024-10-02 03:08:40:627 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9575abcf> ]
[2024-10-02 03:10:21:628 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 10 , 耗时： 3
[2024-10-02 03:12:45:939 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3889MB usedMemory:207MB
[2024-10-02 03:17:45:940 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3862MB usedMemory:234MB
[2024-10-02 03:18:08:116 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xb6f84ca2, /***********:34056 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 03:18:08:288 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b6f84ca2> ]
[2024-10-02 03:18:38:534 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============95a56695> ]
[2024-10-02 03:18:38:534 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====95a56695> ]
[2024-10-02 03:18:38:699 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============95a56695> ]
[2024-10-02 03:18:38:853 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x90ee593a, /***********:59280 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 03:18:39:016 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============90ee593a> ]
[2024-10-02 03:22:45:940 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3834MB usedMemory:262MB
[2024-10-02 03:27:45:941 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3805MB usedMemory:291MB
[2024-10-02 03:32:45:941 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3779MB usedMemory:317MB
[2024-10-02 03:37:45:941 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3751MB usedMemory:345MB
[2024-10-02 03:42:45:942 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3601MB usedMemory:495MB
[2024-10-02 03:47:45:942 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3573MB usedMemory:523MB
[2024-10-02 03:52:45:943 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3545MB usedMemory:551MB
[2024-10-02 03:57:45:943 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3517MB usedMemory:579MB
[2024-10-02 04:02:45:944 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3495MB usedMemory:601MB
[2024-10-02 04:07:45:944 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3469MB usedMemory:627MB
[2024-10-02 04:12:45:944 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3824MB usedMemory:272MB
[2024-10-02 04:14:56:076 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 4a793c05> ]
[2024-10-02 04:14:56:528 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887599
[2024-10-02 04:14:56:551 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887599, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 04:14:56:552 ERROR](RechargeFinishScript.java:269)[ 玩家 ********* 的充值商品:1305 订单号： 536887599 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 04:17:45:945 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3671MB usedMemory:425MB
[2024-10-02 04:19:29:818 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： cfc8c04b> ]
[2024-10-02 04:19:34:566 INFO](GetJourneyDiamondsScript.java:79)钻石已领取: 2, player: 536886164
[2024-10-02 04:19:41:958 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[402],开始丢给RechargeProcessor处理
[2024-10-02 04:19:41:958 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 04:19:41:958 ERROR](RechargeFinishScript.java:81)收到充值商品:402 订单信息:[ 支付类型:1 , 订单号: 536887647, 充值金额: 30.0充值个数:1 ]
[2024-10-02 04:19:41:969 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:402 ] 充值完成，订单状态修改为 4！
[2024-10-02 04:19:41:970 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 04:20:21:630 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 6 , 耗时： 2
[2024-10-02 04:21:39:693 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： cfc8c04b> ]
[2024-10-02 04:21:39:693 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============cfc8c04b> ]
[2024-10-02 04:22:45:945 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3485MB usedMemory:611MB
[2024-10-02 04:23:52:438 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 86003c73> ]
[2024-10-02 04:25:51:226 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:37
[2024-10-02 04:26:01:521 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:77
[2024-10-02 04:26:10:108 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:18
[2024-10-02 04:26:15:707 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:37
[2024-10-02 04:26:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 23 , 耗时： 3
[2024-10-02 04:26:36:150 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:22
[2024-10-02 04:27:45:946 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3691MB usedMemory:405MB
[2024-10-02 04:27:46:924 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 86003c73> ]
[2024-10-02 04:27:46:925 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============86003c73> ]
[2024-10-02 04:27:46:925 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====86003c73> ]
[2024-10-02 04:27:46:996 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============86003c73> ]
[2024-10-02 04:28:19:695 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10009_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:28:19:708 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:28:21:632 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 3
[2024-10-02 04:29:07:728 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10165_99"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:29:07:742 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:29:38:594 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 54465457> ]
[2024-10-02 04:30:21:629 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 0
[2024-10-02 04:31:51:181 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 54465457> ]
[2024-10-02 04:31:51:182 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============54465457> ]
[2024-10-02 04:31:51:182 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====54465457> ]
[2024-10-02 04:31:51:326 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============54465457> ]
[2024-10-02 04:32:21:643 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 102 , 耗时： 14
[2024-10-02 04:32:45:946 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3451MB usedMemory:645MB
[2024-10-02 04:33:30:746 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536886164","productid":964,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:33:30:755 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[964],开始丢给RechargeProcessor处理
[2024-10-02 04:33:30:761 ERROR](UpdateRechargeBeanScript.java:52)角色不在线，离线充值: 536887648
[2024-10-02 04:33:30:761 ERROR](RechargeService.java:442)订单[536887648] 脚本支付流程！
[2024-10-02 04:33:30:773 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 04:33:43:810 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 89564a9f> ]
[2024-10-02 04:33:44:049 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887648
[2024-10-02 04:33:44:072 ERROR](RechargeFinishScript.java:81)收到充值商品:964 订单信息:[ 支付类型:3 , 订单号: 536887648, 充值金额: 1280.0充值个数:1 ]
[2024-10-02 04:33:44:079 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:964 ] 充值完成，订单状态修改为 4！
[2024-10-02 04:34:21:629 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 0 , 耗时： 0
[2024-10-02 04:36:05:181 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14160_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:36:21:630 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 1
[2024-10-02 04:36:22:814 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:13
[2024-10-02 04:36:23:394 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:23:959 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:24:211 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:24:663 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:24:912 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:13
[2024-10-02 04:36:25:864 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:26:126 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:26:477 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:26:688 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:27:012 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:27:254 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:28:014 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:28:215 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:28:656 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:29:050 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:29:254 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:29:493 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:30:152 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:30:350 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:30:815 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:31:195 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:31:374 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:31:579 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:31:849 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:32:222 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:32:611 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:15
[2024-10-02 04:36:32:795 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:33:429 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:33:609 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:33:808 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:34:284 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:13
[2024-10-02 04:36:34:904 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:35:103 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:35:557 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:47
[2024-10-02 04:36:35:752 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:36:145 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:36:760 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:37:211 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:37:445 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:37:666 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:38:778 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:38:989 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:39:418 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:39:832 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:40:022 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:40:244 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:13
[2024-10-02 04:36:40:498 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:40:722 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:14
[2024-10-02 04:36:40:957 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:41:655 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:14
[2024-10-02 04:36:42:324 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:42:523 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:42:700 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:42:903 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:43:138 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:43:313 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:12
[2024-10-02 04:36:43:559 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:36:43:891 ERROR](CommandProcessor.java:174)处理消息[1800,command:game.server.logic.drawcard.handler.LTimeDrawCardHandler]耗时:11
[2024-10-02 04:37:45:947 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3584MB usedMemory:512MB
[2024-10-02 04:38:21:647 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 135 , 耗时： 18
[2024-10-02 04:38:56:356 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 89564a9f> ]
[2024-10-02 04:38:56:357 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============89564a9f> ]
[2024-10-02 04:38:56:357 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====89564a9f> ]
[2024-10-02 04:38:56:543 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============89564a9f> ]
[2024-10-02 04:39:12:100 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"403706_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:39:12:100 ERROR](BeanFactory.java:213)超过1星专属宝物[403706]不能创建进入背包!
[2024-10-02 04:39:12:106 ERROR](Mail.java:311)邮件系统创建的道具 ID：[403706] 不存在
[2024-10-02 04:39:12:116 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:39:15:228 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 22056529> ]
[2024-10-02 04:39:45:645 INFO](RechargeService.java:509)玩家[roleId:536886164]请求充值,payId[14],开始丢给RechargeProcessor处理
[2024-10-02 04:39:45:645 INFO](RechargeService.java:329)玩家[roleId:536886164]代币充值    订单生成  成功
[2024-10-02 04:39:45:646 ERROR](RechargeFinishScript.java:81)收到充值商品:14 订单信息:[ 支付类型:1 , 订单号: 536887649, 充值金额: 188.0充值个数:1 ]
[2024-10-02 04:39:45:647 ERROR](BuyLimittimeGiftScript.java:83)限时礼包购买成功,playerId:536886164,type:0,orderId:536887649,gift:{"cdTime":1727858385647,"endTime":0,"heroId":0,"type":0}
[2024-10-02 04:39:45:648 ERROR](RechargeFinishScript.java:250)[ 玩家：536886164的充值商品:14 ] 充值完成，订单状态修改为 4！
[2024-10-02 04:40:14:792 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"403706_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:40:14:793 ERROR](BeanFactory.java:213)超过1星专属宝物[403706]不能创建进入背包!
[2024-10-02 04:40:14:793 ERROR](Mail.java:311)邮件系统创建的道具 ID：[403706] 不存在
[2024-10-02 04:40:21:632 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 3
[2024-10-02 04:40:34:678 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:40:42:981 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"403700_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:41:06:430 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-3_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:41:14:806 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-4_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:41:28:570 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10017_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:41:43:863 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10062_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:42:17:054 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10061_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:42:21:632 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 10 , 耗时： 3
[2024-10-02 04:42:45:947 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3732MB usedMemory:364MB
[2024-10-02 04:44:11:631 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 22056529> ]
[2024-10-02 04:44:11:632 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============22056529> ]
[2024-10-02 04:44:11:632 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====22056529> ]
[2024-10-02 04:44:11:794 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============22056529> ]
[2024-10-02 04:44:40:463 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14215_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:44:40:477 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:44:43:289 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 70cac556> ]
[2024-10-02 04:46:21:630 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 04:47:45:948 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3525MB usedMemory:571MB
[2024-10-02 04:48:16:621 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 70cac556> ]
[2024-10-02 04:48:16:621 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============70cac556> ]
[2024-10-02 04:48:16:621 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====70cac556> ]
[2024-10-02 04:48:16:786 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============70cac556> ]
[2024-10-02 04:48:27:111 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14220_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:48:27:126 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:48:29:459 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 7ee612f8> ]
[2024-10-02 04:49:06:614 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13001_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:15:751 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13002_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:26:932 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13003_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:32:609 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13004_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:38:664 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13005_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:42:099 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 7ee612f8> ]
[2024-10-02 04:49:42:099 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7ee612f8> ]
[2024-10-02 04:49:42:099 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====7ee612f8> ]
[2024-10-02 04:49:42:233 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7ee612f8> ]
[2024-10-02 04:49:46:349 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13006_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:52:456 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13007_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:49:59:376 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13008_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:50:05:402 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13009_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:50:14:152 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13010_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:50:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 9 , 耗时： 2
[2024-10-02 04:50:22:908 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13011_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:50:30:582 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13012_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:50:45:792 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13013_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:50:56:045 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13014_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:05:992 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13015_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:13:778 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14187_6"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:13:795 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:51:16:237 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13016_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:17:605 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 9cbc357d> ]
[2024-10-02 04:51:27:882 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 4a793c05> ]
[2024-10-02 04:51:27:883 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4a793c05> ]
[2024-10-02 04:51:29:138 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13017_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:29:138 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=*********
[2024-10-02 04:51:34:039 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 23882b45> ]
[2024-10-02 04:51:34:221 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887599
[2024-10-02 04:51:34:244 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887599, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 04:51:34:244 ERROR](RechargeFinishScript.java:269)[ 玩家 ********* 的充值商品:1305 订单号： 536887599 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 04:51:35:474 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13018_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:40:907 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13019_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:46:342 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13020_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:51:54:029 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13020_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:00:568 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13021_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:07:743 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13022_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:14:831 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13023_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:20:179 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13024_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 04:52:22:082 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:79
[2024-10-02 04:52:25:694 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13025_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:28:042 ERROR](CommandProcessor.java:174)处理消息[777,command:game.server.logic.hero.handler.PutOnWareHandler]耗时:31
[2024-10-02 04:52:35:055 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13026_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:41:209 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13027_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:45:948 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3527MB usedMemory:569MB
[2024-10-02 04:52:46:220 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13028_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:51:946 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13029_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:52:57:855 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13030_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:09:376 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13031_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:17:018 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13032_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:22:205 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13033_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:28:335 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13034_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:38:331 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-8_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:51:663 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 9cbc357d> ]
[2024-10-02 04:53:51:664 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9cbc357d> ]
[2024-10-02 04:53:51:664 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====9cbc357d> ]
[2024-10-02 04:53:51:826 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9cbc357d> ]
[2024-10-02 04:53:59:748 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14217_6"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 04:53:59:762 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536886164
[2024-10-02 04:54:04:325 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 98997e6f> ]
[2024-10-02 04:54:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 04:57:33:719 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:68
[2024-10-02 04:57:45:948 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3758MB usedMemory:338MB
[2024-10-02 04:58:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 9 , 耗时： 1
[2024-10-02 05:00:00:765 INFO](GameLineManager.java:270)AcrossDay, 2024-10-02T05:00:00
[2024-10-02 05:00:00:841 ERROR](OperateActivityService.java:2805)活动已关闭
[2024-10-02 05:00:00:880 INFO](NettyGameServer.java:599)当前 BeforeGC totalMemory:4096MB freeMemory:3612MB usedMemory:484MB
[2024-10-02 05:00:01:738 INFO](NettyGameServer.java:599)当前 AfterGC totalMemory:4096MB freeMemory:3927MB usedMemory:169MB
[2024-10-02 05:00:50:729 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:30
[2024-10-02 05:00:55:572 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:14
[2024-10-02 05:01:25:573 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 98997e6f> ]
[2024-10-02 05:01:25:573 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============98997e6f> ]
[2024-10-02 05:01:25:573 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====98997e6f> ]
[2024-10-02 05:01:25:634 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============98997e6f> ]
[2024-10-02 05:01:25:747 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 12344735> ]
[2024-10-02 05:02:21:633 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 13 , 耗时： 3
[2024-10-02 05:02:30:998 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 23882b45> ]
[2024-10-02 05:02:30:999 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============23882b45> ]
[2024-10-02 05:02:45:949 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3862MB usedMemory:234MB
[2024-10-02 05:03:20:652 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14101_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:04:21:634 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 17 , 耗时： 4
[2024-10-02 05:04:53:203 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13057_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:05:16:367 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404700_99"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:06:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 05:07:17:244 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"clearbag","playerid":"536886164","token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14160
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 19001
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 10063
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 10017
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14161
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14113
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14100
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 13034
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 10210
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14212
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14218
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14149
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 10147
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 10108
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 10145
[2024-10-02 05:07:17:245 ERROR](Item.java:124)道具不存在或不可出售: 14182
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13056
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13057
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13025
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 10014
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 10088
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13004
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13015
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 18007
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 14216
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 10159
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13055
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 14240
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 10015
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 10068
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 19002
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13003
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13026
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13027
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13005
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13016
[2024-10-02 05:07:17:246 ERROR](Item.java:124)道具不存在或不可出售: 13017
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 13028
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 13006
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 10097
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 10106
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14167
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 10072
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14219
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14200
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 10075
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14284
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14180
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 10155
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 10154
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14187
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14217
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 13040
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 14101
[2024-10-02 05:07:17:247 ERROR](Item.java:124)道具不存在或不可出售: 13057
[2024-10-02 05:07:17:247 ERROR](GameHttpNettyNewServerImpl.java:208)items data: [{"id":14160,"num":96280,"numMax":99999,"sellPrice":0},{"id":19001,"num":3997,"numMax":99999,"sellPrice":0},{"id":10063,"num":35,"numMax":99999,"sellPrice":0},{"id":10017,"num":3885,"numMax":99999,"sellPrice":0},{"id":14161,"num":105,"numMax":99999,"sellPrice":0},{"id":14113,"num":9819,"numMax":99999,"sellPrice":0},{"id":14100,"num":1274,"numMax":99999,"sellPrice":0},{"id":13034,"num":104,"numMax":99999,"sellPrice":0},{"id":10210,"num":59558,"numMax":99999,"sellPrice":0},{"id":14212,"num":3,"numMax":99999,"sellPrice":0},{"id":14218,"num":25,"numMax":99999,"sellPrice":0},{"id":14149,"num":2,"numMax":99999,"sellPrice":0},{"id":10147,"num":4,"numMax":99999,"sellPrice":0},{"id":10108,"num":350,"numMax":99999,"sellPrice":0},{"id":10145,"num":3,"numMax":99999,"sellPrice":0},{"id":14182,"num":4,"numMax":99999,"sellPrice":0},{"id":13056,"num":44,"numMax":99999,"sellPrice":0},{"id":13057,"num":99999,"numMax":99999,"sellPrice":0},{"id":13025,"num":1,"numMax":99999,"sellPrice":0},{"id":10014,"num":229,"numMax":99999,"sellPrice":0},{"id":10088,"num":290,"numMax":99999,"sellPrice":0},{"id":13004,"num":7,"numMax":99999,"sellPrice":0},{"id":13015,"num":7,"numMax":99999,"sellPrice":0},{"id":18007,"num":9,"numMax":99999,"sellPrice":0},{"id":14216,"num":1,"numMax":99999,"sellPrice":0},{"id":10159,"num":4,"numMax":99999,"sellPrice":0},{"id":13055,"num":9,"numMax":99999,"sellPrice":0},{"id":14240,"num":12,"numMax":99999,"sellPrice":0},{"id":10015,"num":43,"numMax":99999,"sellPrice":0},{"id":10068,"num":99764,"numMax":99999,"sellPrice":0},{"id":19002,"num":3,"numMax":99999,"sellPrice":0},{"id":13003,"num":20,"numMax":99999,"sellPrice":0},{"id":13026,"num":6,"numMax":99999,"sellPrice":0},{"id":13027,"num":5,"numMax":99999,"sellPrice":0},{"id":13005,"num":26,"numMax":99999,"sellPrice":0},{"id":13016,"num":6,"numMax":99999,"sellPrice":0},{"id":13017,"num":6,"numMax":99999,"sellPrice":0},{"id":13028,"num":5,"numMax":99999,"sellPrice":0},{"id":13006,"num":34,"numMax":99999,"sellPrice":0},{"id":10097,"num":5,"numMax":99999,"sellPrice":0},{"id":10106,"num":10,"numMax":99999,"sellPrice":0},{"id":14167,"num":3,"numMax":99999,"sellPrice":0},{"id":10072,"num":2,"numMax":99999,"sellPrice":0},{"id":14219,"num":1,"numMax":99999,"sellPrice":0},{"id":14200,"num":5,"numMax":99999,"sellPrice":0},{"id":10075,"num":2,"numMax":99999,"sellPrice":0},{"id":14284,"num":1,"numMax":99999,"sellPrice":0},{"id":14180,"num":2,"numMax":99999,"sellPrice":0},{"id":10155,"num":8,"numMax":99999,"sellPrice":0},{"id":10154,"num":2,"numMax":99999,"sellPrice":0},{"id":14187,"num":4,"numMax":99999,"sellPrice":0},{"id":14217,"num":6,"numMax":99999,"sellPrice":0},{"id":13040,"num":2,"numMax":99999,"sellPrice":0},{"id":14101,"num":99419,"numMax":99999,"sellPrice":0},{"id":13057,"num":40,"numMax":99999,"sellPrice":0}]
[2024-10-02 05:07:23:460 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404700_9"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:07:37:493 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13057_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:07:45:949 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3804MB usedMemory:292MB
[2024-10-02 05:07:56:580 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 12344735> ]
[2024-10-02 05:07:56:580 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============12344735> ]
[2024-10-02 05:08:01:236 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"clearbag","playerid":"536886164","token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:08:01:236 ERROR](GameHttpNettyNewServerImpl.java:259)gm error: 
java.lang.NullPointerException
[2024-10-02 05:08:14:772 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 1ea40209> ]
[2024-10-02 05:08:21:633 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 24 , 耗时： 3
[2024-10-02 05:12:45:950 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3483MB usedMemory:613MB
[2024-10-02 05:13:30:028 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13057_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:13:48:843 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404700_2"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:14:21:631 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 05:15:17:345 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404700_2"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:16:48:527 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"403700_2"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:17:45:950 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3438MB usedMemory:658MB
[2024-10-02 05:17:49:198 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"403701_2"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:18:21:632 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 05:18:41:143 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404701_10"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:19:09:781 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404701_10"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:20:08:659 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404701_30"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:20:21:632 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 1
[2024-10-02 05:20:53:651 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"404701_6"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:22:21:632 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 05:22:45:951 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3892MB usedMemory:204MB
[2024-10-02 05:24:35:386 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536886164","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10108_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 05:26:21:644 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 82 , 耗时： 13
[2024-10-02 05:26:57:434 ERROR](CommandProcessor.java:174)处理消息[7941,command:game.server.logic.amplifier.handler.ReqSaveLastTrainningHandler]耗时:15
[2024-10-02 05:27:45:951 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3623MB usedMemory:473MB
[2024-10-02 05:28:21:648 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 137 , 耗时： 17
[2024-10-02 05:30:21:643 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 97 , 耗时： 12
[2024-10-02 05:32:21:636 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 32 , 耗时： 5
[2024-10-02 05:32:45:951 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3562MB usedMemory:534MB
[2024-10-02 05:34:46:543 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 1ea40209> ]
[2024-10-02 05:34:46:543 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============1ea40209> ]
[2024-10-02 05:37:45:952 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3495MB usedMemory:601MB
[2024-10-02 05:41:25:378 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x83c68451, /***********:56572 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 05:41:25:538 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============83c68451> ]
[2024-10-02 05:41:55:729 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============49451173> ]
[2024-10-02 05:41:55:729 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====49451173> ]
[2024-10-02 05:41:55:921 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============49451173> ]
[2024-10-02 05:41:56:078 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xde0283c0, /***********:39394 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 05:41:56:234 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============de0283c0> ]
[2024-10-02 05:42:45:952 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3849MB usedMemory:247MB
[2024-10-02 05:47:45:953 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3822MB usedMemory:274MB
[2024-10-02 05:52:45:953 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3795MB usedMemory:301MB
[2024-10-02 05:57:45:953 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3768MB usedMemory:328MB
[2024-10-02 06:02:45:954 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3740MB usedMemory:356MB
[2024-10-02 06:07:45:954 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3714MB usedMemory:382MB
[2024-10-02 06:12:45:955 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3564MB usedMemory:532MB
[2024-10-02 06:17:45:955 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3536MB usedMemory:560MB
[2024-10-02 06:22:45:956 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3510MB usedMemory:586MB
[2024-10-02 06:27:45:956 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3483MB usedMemory:613MB
[2024-10-02 06:32:45:956 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3457MB usedMemory:639MB
[2024-10-02 06:37:45:957 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3429MB usedMemory:667MB
[2024-10-02 06:42:45:957 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3784MB usedMemory:312MB
[2024-10-02 06:47:45:958 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3756MB usedMemory:340MB
[2024-10-02 06:52:45:958 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3730MB usedMemory:366MB
[2024-10-02 06:57:45:959 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3702MB usedMemory:394MB
[2024-10-02 07:02:45:959 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3675MB usedMemory:421MB
[2024-10-02 07:07:45:959 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3648MB usedMemory:448MB
[2024-10-02 07:12:45:960 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3498MB usedMemory:598MB
[2024-10-02 07:17:45:960 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3470MB usedMemory:626MB
[2024-10-02 07:22:45:961 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3444MB usedMemory:652MB
[2024-10-02 07:26:11:716 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： ddbdab76> ]
[2024-10-02 07:26:11:716 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ddbdab76> ]
[2024-10-02 07:27:45:961 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3908MB usedMemory:188MB
[2024-10-02 07:32:45:962 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3888MB usedMemory:208MB
[2024-10-02 07:37:45:962 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3871MB usedMemory:225MB
[2024-10-02 07:42:45:963 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3730MB usedMemory:366MB
[2024-10-02 07:47:45:963 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3712MB usedMemory:384MB
[2024-10-02 07:52:45:963 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3693MB usedMemory:403MB
[2024-10-02 07:57:45:964 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3675MB usedMemory:421MB
[2024-10-02 08:00:00:967 INFO](PirateBossService.java:645)---start PirateBoss----
[2024-10-02 08:02:45:965 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3657MB usedMemory:439MB
[2024-10-02 08:07:45:965 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3639MB usedMemory:457MB
[2024-10-02 08:12:45:966 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3497MB usedMemory:599MB
[2024-10-02 08:17:45:966 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3479MB usedMemory:617MB
[2024-10-02 08:22:45:967 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3460MB usedMemory:636MB
[2024-10-02 08:25:12:339 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xda1808f1, /***********:13945 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 08:25:12:487 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============da1808f1> ]
[2024-10-02 08:27:45:967 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3441MB usedMemory:655MB
[2024-10-02 08:32:45:968 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3422MB usedMemory:674MB
[2024-10-02 08:37:45:968 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3907MB usedMemory:189MB
[2024-10-02 08:40:22:784 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 0d2ddae5> ]
[2024-10-02 08:40:52:849 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 0d2ddae5> ]
[2024-10-02 08:40:52:850 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============0d2ddae5> ]
[2024-10-02 08:40:52:850 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====0d2ddae5> ]
[2024-10-02 08:40:58:250 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============0d2ddae5> ]
[2024-10-02 08:40:58:250 ERROR](LoginService.java:1053)java.nio.channels.ClosedChannelException

[2024-10-02 08:42:45:970 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3710MB usedMemory:386MB
[2024-10-02 08:47:45:970 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3692MB usedMemory:404MB
[2024-10-02 08:52:45:971 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3674MB usedMemory:422MB
[2024-10-02 08:57:45:972 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3656MB usedMemory:440MB
[2024-10-02 09:02:45:972 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3637MB usedMemory:459MB
[2024-10-02 09:07:45:973 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3619MB usedMemory:477MB
[2024-10-02 09:12:45:973 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3477MB usedMemory:619MB
[2024-10-02 09:17:45:974 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3459MB usedMemory:637MB
[2024-10-02 09:22:45:974 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3441MB usedMemory:655MB
[2024-10-02 09:27:45:975 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3423MB usedMemory:673MB
[2024-10-02 09:32:45:977 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3907MB usedMemory:189MB
[2024-10-02 09:37:45:977 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3888MB usedMemory:208MB
[2024-10-02 09:42:45:977 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3748MB usedMemory:348MB
[2024-10-02 09:47:45:978 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3730MB usedMemory:366MB
[2024-10-02 09:52:45:978 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3711MB usedMemory:385MB
[2024-10-02 09:57:45:979 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3694MB usedMemory:402MB
[2024-10-02 10:00:00:841 INFO](CheckGuildwarStatusScript.java:82)联盟战转为报名期
[2024-10-02 10:00:00:841 INFO](IndigoService.java:622)==========空闲阶段结束》》》》》》》》》》》》报名阶段开始==========================
[2024-10-02 10:02:45:979 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3675MB usedMemory:421MB
[2024-10-02 10:06:30:166 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xea0fa5d3, /**********:49657 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 10:06:30:308 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ea0fa5d3> ]
[2024-10-02 10:06:43:980 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x0a174bb4, /**********:20671 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 10:06:44:122 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============0a174bb4> ]
[2024-10-02 10:07:45:979 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3656MB usedMemory:440MB
[2024-10-02 10:10:44:174 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x7af87663, /**************:34736 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 10:10:44:353 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7af87663> ]
[2024-10-02 10:12:33:006 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： d9ae8e1c> ]
[2024-10-02 10:12:45:980 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3889MB usedMemory:207MB
[2024-10-02 10:14:10:421 INFO](RechargeService.java:509)玩家[roleId:536885275]请求充值,payId[433],开始丢给RechargeProcessor处理
[2024-10-02 10:14:10:421 INFO](RechargeService.java:329)玩家[roleId:536885275]代币充值    订单生成  成功
[2024-10-02 10:14:10:421 ERROR](RechargeFinishScript.java:81)收到充值商品:433 订单信息:[ 支付类型:1 , 订单号: 536887650, 充值金额: 648.0充值个数:1 ]
[2024-10-02 10:14:10:441 ERROR](RechargeFinishScript.java:250)[ 玩家：536885275的充值商品:433 ] 充值完成，订单状态修改为 4！
[2024-10-02 10:14:10:442 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:21
[2024-10-02 10:14:21:665 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 29 , 耗时： 8
[2024-10-02 10:16:21:658 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 7 , 耗时： 1
[2024-10-02 10:16:56:986 ERROR](CommandProcessor.java:174)处理消息[4646,command:game.server.logic.guild.handler.ReqReaserchRewardHandler]耗时:11
[2024-10-02 10:17:27:682 ERROR](CommandProcessor.java:174)处理消息[4648,command:game.server.logic.guild.handler.ReqGetTrainingInfoHandler]耗时:18
[2024-10-02 10:17:27:700 INFO](MailService.java:400)邮件发送=======标题：玩家训练经验====playerId=536883031
[2024-10-02 10:17:45:980 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3803MB usedMemory:293MB
[2024-10-02 10:18:21:660 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 13 , 耗时： 3
[2024-10-02 10:19:54:527 ERROR](CommandProcessor.java:174)处理消息[8989,command:game.server.logic.operateActivity.handler.ReqFirstAccRewardHandler]耗时:17
[2024-10-02 10:20:21:659 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 10:22:21:659 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 7 , 耗时： 2
[2024-10-02 10:22:45:981 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3752MB usedMemory:344MB
[2024-10-02 10:25:23:549 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x01bd3ddd, /**************:41490 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 10:25:23:898 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============01bd3ddd> ]
[2024-10-02 10:25:29:058 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x3b77062b, /**************:41506 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 10:25:29:397 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============3b77062b> ]
[2024-10-02 10:25:29:792 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel exceptionCaught ==============java.io.IOException: Connection reset by peer> ]
[2024-10-02 10:25:29:793 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============47b370ce> ]
[2024-10-02 10:27:28:560 ERROR](CommandProcessor.java:174)处理消息[2305,command:game.server.logic.arena.handler.GetArenaHandler]耗时:100
[2024-10-02 10:27:45:981 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3619MB usedMemory:477MB
[2024-10-02 10:28:21:659 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 10:28:46:569 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:18
[2024-10-02 10:30:21:659 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 2
[2024-10-02 10:32:21:659 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 10:32:45:981 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3574MB usedMemory:522MB
[2024-10-02 10:33:20:770 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:38
[2024-10-02 10:34:21:659 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 10:37:45:982 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3517MB usedMemory:579MB
[2024-10-02 10:42:45:982 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3866MB usedMemory:230MB
[2024-10-02 10:47:45:983 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3838MB usedMemory:258MB
[2024-10-02 10:48:31:529 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:33
[2024-10-02 10:49:17:342 INFO](SnatchTerritoryService.java:1162)道馆争夺开始 -------道馆id： 2317
[2024-10-02 10:49:17:346 INFO](SnatchTerritoryService.java:1430)道馆争夺结束 -------道馆id： 2317 ， ------ 剩余玩家阵营 1 , use time 2
[2024-10-02 10:49:18:181 INFO](SnatchTerritory.java:1031)玩家： 百里雄风 , id: 536885275 ,（战斗结束，阵营交替）进攻方 战斗结束被遣返！ 
[2024-10-02 10:49:18:182 INFO](SendAwardAndChangeStatusHandler.java:282)道馆： 2317 , 发奖流程结束！ 
[2024-10-02 10:49:22:901 INFO](SnatchTerritory.java:952)进攻方玩家： 百里雄风 , id 536885275  ,  从道馆 2317剔除
[2024-10-02 10:50:21:662 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 13 , 耗时： 4
[2024-10-02 10:50:24:124 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： f2d97f6d> ]
[2024-10-02 10:51:13:690 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： f2d97f6d> ]
[2024-10-02 10:51:13:690 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============f2d97f6d> ]
[2024-10-02 10:52:21:660 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 13 , 耗时： 2
[2024-10-02 10:52:45:983 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3558MB usedMemory:538MB
[2024-10-02 10:54:21:661 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 3
[2024-10-02 10:54:44:150 ERROR](CommandProcessor.java:174)处理消息[2824,command:game.server.logic.friend.handler.SendEnergyHandler]耗时:54
[2024-10-02 10:55:07:137 ERROR](CommandProcessor.java:174)处理消息[3592,command:game.server.logic.snatch.handler.OneKeySnatchHandler]耗时:21
[2024-10-02 10:55:10:657 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xfd8da087, /*************:64208 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 10:55:10:850 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============fd8da087> ]
[2024-10-02 10:56:19:548 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： d9ae8e1c> ]
[2024-10-02 10:56:19:549 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d9ae8e1c> ]
[2024-10-02 10:56:21:660 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 2
[2024-10-02 10:57:45:984 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3724MB usedMemory:372MB
[2024-10-02 11:02:45:984 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3705MB usedMemory:391MB
[2024-10-02 11:06:04:911 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： e23bd7bf> ]
[2024-10-02 11:06:21:692 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 33
[2024-10-02 11:06:30:942 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:19
[2024-10-02 11:07:45:984 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3548MB usedMemory:548MB
[2024-10-02 11:07:58:882 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:17
[2024-10-02 11:08:30:842 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:20
[2024-10-02 11:08:39:627 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:18
[2024-10-02 11:08:57:354 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:17
[2024-10-02 11:09:28:322 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:17
[2024-10-02 11:09:40:529 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:15
[2024-10-02 11:10:02:583 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:15
[2024-10-02 11:12:45:985 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3785MB usedMemory:311MB
[2024-10-02 11:13:54:820 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： e23bd7bf> ]
[2024-10-02 11:13:54:821 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e23bd7bf> ]
[2024-10-02 11:13:54:821 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====e23bd7bf> ]
[2024-10-02 11:13:54:910 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e23bd7bf> ]
[2024-10-02 11:14:36:560 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 4dd04f7f> ]
[2024-10-02 11:15:28:118 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 4dd04f7f> ]
[2024-10-02 11:15:28:119 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4dd04f7f> ]
[2024-10-02 11:15:28:119 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====4dd04f7f> ]
[2024-10-02 11:15:28:330 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4dd04f7f> ]
[2024-10-02 11:16:21:660 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 1
[2024-10-02 11:17:45:985 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3634MB usedMemory:462MB
[2024-10-02 11:22:45:986 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3617MB usedMemory:479MB
[2024-10-02 11:27:45:986 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3599MB usedMemory:497MB
[2024-10-02 11:32:45:991 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3580MB usedMemory:516MB
[2024-10-02 11:37:45:991 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3561MB usedMemory:535MB
[2024-10-02 11:42:45:992 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3420MB usedMemory:676MB
[2024-10-02 11:47:45:992 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3906MB usedMemory:190MB
[2024-10-02 11:52:45:993 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3888MB usedMemory:208MB
[2024-10-02 11:53:50:308 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 584a762a> ]
[2024-10-02 11:57:45:993 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3725MB usedMemory:371MB
[2024-10-02 11:57:51:062 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 584a762a> ]
[2024-10-02 11:57:51:063 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============584a762a> ]
[2024-10-02 12:02:45:993 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3673MB usedMemory:423MB
[2024-10-02 12:07:45:994 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3655MB usedMemory:441MB
[2024-10-02 12:12:45:994 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3514MB usedMemory:582MB
[2024-10-02 12:17:45:995 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3496MB usedMemory:600MB
[2024-10-02 12:22:45:995 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3476MB usedMemory:620MB
[2024-10-02 12:23:21:558 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x75bf4983, /**************:64532 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 12:23:21:769 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============75bf4983> ]
[2024-10-02 12:27:46:007 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3457MB usedMemory:639MB
[2024-10-02 12:30:26:202 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 842d0bb3> ]
[2024-10-02 12:30:36:733 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 842d0bb3> ]
[2024-10-02 12:30:36:733 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============842d0bb3> ]
[2024-10-02 12:32:46:007 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3797MB usedMemory:299MB
[2024-10-02 12:37:46:008 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3777MB usedMemory:319MB
[2024-10-02 12:42:46:008 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3636MB usedMemory:460MB
[2024-10-02 12:47:46:008 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3616MB usedMemory:480MB
[2024-10-02 12:52:46:009 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3598MB usedMemory:498MB
[2024-10-02 12:57:46:009 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3580MB usedMemory:516MB
[2024-10-02 13:00:00:175 INFO](WorldBossService.java:351)---start WorldBoss----
[2024-10-02 13:02:46:010 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3563MB usedMemory:533MB
[2024-10-02 13:07:46:010 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3542MB usedMemory:554MB
[2024-10-02 13:12:46:011 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3905MB usedMemory:191MB
[2024-10-02 13:17:46:011 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3887MB usedMemory:209MB
[2024-10-02 13:22:46:011 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3868MB usedMemory:228MB
[2024-10-02 13:23:19:180 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 01caad62> ]
[2024-10-02 13:24:25:762 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:11
[2024-10-02 13:24:59:391 INFO](WorldBossService.java:317)---resetTime WorldBoss----
[2024-10-02 13:24:59:413 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536883041
[2024-10-02 13:24:59:482 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536881113
[2024-10-02 13:24:59:499 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536882509
[2024-10-02 13:24:59:595 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536885275
[2024-10-02 13:24:59:635 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536885797
[2024-10-02 13:24:59:646 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536886084
[2024-10-02 13:24:59:654 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536886183
[2024-10-02 13:24:59:668 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536886069
[2024-10-02 13:24:59:678 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536886374
[2024-10-02 13:24:59:687 INFO](MailService.java:400)邮件发送=======标题：【世界BOSS】公会伤害榜====playerId=536886486
[2024-10-02 13:25:43:016 ERROR](CommandProcessor.java:174)处理消息[2305,command:game.server.logic.arena.handler.GetArenaHandler]耗时:122
[2024-10-02 13:25:49:168 ERROR](CommandProcessor.java:174)处理消息[2308,command:game.server.logic.arena.handler.TopRankingHandler]耗时:260
[2024-10-02 13:27:46:012 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3677MB usedMemory:419MB
[2024-10-02 13:27:50:186 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel exceptionCaught ==============java.io.IOException: Connection reset by peer> ]
[2024-10-02 13:27:50:187 INFO](NettyGameServer.java:349)--->[Login] exceptionCaught, close sessionId:01caad62,cause:Connection reset by peer
[2024-10-02 13:27:50:190 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 01caad62> ]
[2024-10-02 13:27:50:190 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============01caad62> ]
[2024-10-02 13:32:46:012 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3603MB usedMemory:493MB
[2024-10-02 13:37:46:013 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3585MB usedMemory:511MB
[2024-10-02 13:42:46:013 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3443MB usedMemory:653MB
[2024-10-02 13:47:46:013 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3424MB usedMemory:672MB
[2024-10-02 13:52:46:014 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3915MB usedMemory:181MB
[2024-10-02 13:57:46:014 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3896MB usedMemory:200MB
[2024-10-02 14:02:46:015 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3878MB usedMemory:218MB
[2024-10-02 14:07:10:308 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xe2ed8408, /*************:63824 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 14:07:11:013 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e2ed8408> ]
[2024-10-02 14:07:46:015 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3860MB usedMemory:236MB
[2024-10-02 14:12:46:015 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3718MB usedMemory:378MB
[2024-10-02 14:17:46:016 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3698MB usedMemory:398MB
[2024-10-02 14:22:46:016 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3680MB usedMemory:416MB
[2024-10-02 14:27:46:017 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3661MB usedMemory:435MB
[2024-10-02 14:32:46:018 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3643MB usedMemory:453MB
[2024-10-02 14:37:46:018 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3624MB usedMemory:472MB
[2024-10-02 14:42:46:019 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3484MB usedMemory:612MB
[2024-10-02 14:47:46:019 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3466MB usedMemory:630MB
[2024-10-02 14:52:46:020 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3447MB usedMemory:649MB
[2024-10-02 14:57:46:020 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3429MB usedMemory:667MB
[2024-10-02 14:58:40:908 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 7e0dba5a> ]
[2024-10-02 15:02:46:020 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3772MB usedMemory:324MB
[2024-10-02 15:07:46:021 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3717MB usedMemory:379MB
[2024-10-02 15:12:46:021 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3540MB usedMemory:556MB
[2024-10-02 15:13:10:271 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:15
[2024-10-02 15:16:27:457 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:21
[2024-10-02 15:17:46:022 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3488MB usedMemory:608MB
[2024-10-02 15:18:02:305 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:24
[2024-10-02 15:22:46:022 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3448MB usedMemory:648MB
[2024-10-02 15:27:46:022 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3417MB usedMemory:679MB
[2024-10-02 15:30:59:049 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x4d7dea43, /***************:49816 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 15:30:59:224 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4d7dea43> ]
[2024-10-02 15:32:13:527 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 7e0dba5a> ]
[2024-10-02 15:32:13:527 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7e0dba5a> ]
[2024-10-02 15:32:13:527 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====7e0dba5a> ]
[2024-10-02 15:32:13:595 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7e0dba5a> ]
[2024-10-02 15:32:46:023 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3856MB usedMemory:240MB
[2024-10-02 15:37:46:023 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3837MB usedMemory:259MB
[2024-10-02 15:42:46:024 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3696MB usedMemory:400MB
[2024-10-02 15:47:46:025 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3677MB usedMemory:419MB
[2024-10-02 15:52:46:025 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3658MB usedMemory:438MB
[2024-10-02 15:57:46:025 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3640MB usedMemory:456MB
[2024-10-02 16:02:46:026 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3622MB usedMemory:474MB
[2024-10-02 16:05:51:424 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x386820f9, /************:64993 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 16:05:51:650 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============386820f9> ]
[2024-10-02 16:07:46:026 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3604MB usedMemory:492MB
[2024-10-02 16:12:46:027 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3461MB usedMemory:635MB
[2024-10-02 16:17:46:027 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3443MB usedMemory:653MB
[2024-10-02 16:22:46:028 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3425MB usedMemory:671MB
[2024-10-02 16:27:46:028 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3908MB usedMemory:188MB
[2024-10-02 16:32:46:028 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3890MB usedMemory:206MB
[2024-10-02 16:37:46:029 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3870MB usedMemory:226MB
[2024-10-02 16:40:46:462 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： cd278162> ]
[2024-10-02 16:40:46:502 INFO](SessionUtils.java:19)sessionId:cd278162-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:cd278162, reason=5,closing:false
[2024-10-02 16:40:46:589 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： cd278162> ]
[2024-10-02 16:40:46:590 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============cd278162> ]
[2024-10-02 16:40:46:599 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@56673380, sessionId:cd278162
[2024-10-02 16:41:03:054 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 36e85faf> ]
[2024-10-02 16:41:03:068 INFO](SessionUtils.java:19)sessionId:36e85faf-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:36e85faf, reason=15,closing:false
[2024-10-02 16:41:03:157 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 36e85faf> ]
[2024-10-02 16:41:03:158 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============36e85faf> ]
[2024-10-02 16:41:03:167 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@d047e48, sessionId:36e85faf
[2024-10-02 16:41:26:783 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： ac8cff30> ]
[2024-10-02 16:41:26:811 INFO](AccountDao.java:25)新建用户：**********
[2024-10-02 16:42:13:722 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:43
[2024-10-02 16:42:21:683 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 10
[2024-10-02 16:42:46:029 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3618MB usedMemory:478MB
[2024-10-02 16:44:38:871 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： ac8cff30> ]
[2024-10-02 16:44:38:872 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ac8cff30> ]
[2024-10-02 16:44:38:872 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====ac8cff30> ]
[2024-10-02 16:44:48:874 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ac8cff30> ]
[2024-10-02 16:47:46:030 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3586MB usedMemory:510MB
[2024-10-02 16:52:46:030 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3568MB usedMemory:528MB
[2024-10-02 16:57:46:030 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3549MB usedMemory:547MB
[2024-10-02 17:01:48:372 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 52c03b82> ]
[2024-10-02 17:02:16:802 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xd59784ae, /************:39690 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 17:02:17:632 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d59784ae> ]
[2024-10-02 17:02:21:675 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 2
[2024-10-02 17:02:25:899 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:41
[2024-10-02 17:02:46:031 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3416MB usedMemory:680MB
[2024-10-02 17:04:21:675 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 2
[2024-10-02 17:05:13:435 ERROR](CommandProcessor.java:174)处理消息[7426,command:game.server.logic.recharge.handler.ReqRechargeForTestHandler]耗时:21
[2024-10-02 17:06:21:677 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 4
[2024-10-02 17:07:06:638 ERROR](CommandProcessor.java:174)处理消息[4866,command:game.server.logic.rank.handker.PraiseHandler]耗时:16
[2024-10-02 17:07:12:591 ERROR](CommandProcessor.java:174)处理消息[7426,command:game.server.logic.recharge.handler.ReqRechargeForTestHandler]耗时:21
[2024-10-02 17:07:40:665 ERROR](CommandProcessor.java:174)处理消息[532,command:game.server.logic.player.handler.GetOnlineRewardHandler]耗时:17
[2024-10-02 17:07:46:031 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3850MB usedMemory:246MB
[2024-10-02 17:08:21:675 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 1
[2024-10-02 17:12:46:032 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3692MB usedMemory:404MB
[2024-10-02 17:14:11:376 ERROR](CommandProcessor.java:174)处理消息[7426,command:game.server.logic.recharge.handler.ReqRechargeForTestHandler]耗时:21
[2024-10-02 17:14:21:676 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 17:16:21:675 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 1
[2024-10-02 17:17:46:032 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3645MB usedMemory:451MB
[2024-10-02 17:18:21:675 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 1
[2024-10-02 17:21:16:837 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:126
[2024-10-02 17:22:18:167 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 77bcf260> ]
[2024-10-02 17:22:18:470 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887599
[2024-10-02 17:22:18:509 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887599, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 17:22:18:511 ERROR](RechargeFinishScript.java:269)[ 玩家 ********* 的充值商品:1305 订单号： 536887599 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 17:22:34:693 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:11
[2024-10-02 17:22:37:681 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 52c03b82> ]
[2024-10-02 17:22:37:682 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============52c03b82> ]
[2024-10-02 17:22:37:682 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====52c03b82> ]
[2024-10-02 17:22:46:032 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3874MB usedMemory:222MB
[2024-10-02 17:22:47:683 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============52c03b82> ]
[2024-10-02 17:22:50:231 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:69
[2024-10-02 17:23:09:260 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:19
[2024-10-02 17:23:23:070 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 77f53acd> ]
[2024-10-02 17:23:30:802 INFO](RechargeService.java:509)玩家[roleId:*********]请求充值,payId[1001],开始丢给RechargeProcessor处理
[2024-10-02 17:23:30:802 INFO](RechargeService.java:329)玩家[roleId:*********]代币充值    订单生成  成功
[2024-10-02 17:23:30:803 ERROR](RechargeFinishScript.java:81)收到充值商品:1001 订单信息:[ 支付类型:1 , 订单号: 536887652, 充值金额: 88.0充值个数:1 ]
[2024-10-02 17:23:30:815 ERROR](RechargeFinishScript.java:250)[ 玩家：*********的充值商品:1001 ] 充值完成，订单状态修改为 4！
[2024-10-02 17:23:30:815 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 17:23:56:480 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:41
[2024-10-02 17:24:21:679 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 5
[2024-10-02 17:24:27:137 ERROR](CommandProcessor.java:174)处理消息[1282,command:game.server.logic.section.handler.SweepSectionHandler]耗时:14
[2024-10-02 17:24:52:778 ERROR](CommandProcessor.java:174)处理消息[1282,command:game.server.logic.section.handler.SweepSectionHandler]耗时:13
[2024-10-02 17:25:05:316 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 77bcf260> ]
[2024-10-02 17:25:05:317 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============77bcf260> ]
[2024-10-02 17:25:34:573 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 3685610b> ]
[2024-10-02 17:25:34:759 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887599
[2024-10-02 17:25:34:784 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887599, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 17:25:34:784 ERROR](RechargeFinishScript.java:269)[ 玩家 ********* 的充值商品:1305 订单号： 536887599 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 17:25:57:027 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 77f53acd> ]
[2024-10-02 17:25:57:027 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============77f53acd> ]
[2024-10-02 17:25:57:028 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====77f53acd> ]
[2024-10-02 17:25:57:117 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============77f53acd> ]
[2024-10-02 17:26:17:118 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:26:20:645 ERROR](CommandProcessor.java:174)处理消息[3073,command:game.server.logic.mail.handler.ReqGetMailAdjunctHandler]耗时:73
[2024-10-02 17:26:25:932 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:26:33:136 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:26:49:175 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:00:213 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:11:030 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:20:878 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:27:332 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:35:459 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:38:715 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： abb9d11a> ]
[2024-10-02 17:27:38:754 INFO](SessionUtils.java:19)sessionId:abb9d11a-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:abb9d11a, reason=5,closing:false
[2024-10-02 17:27:38:884 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： abb9d11a> ]
[2024-10-02 17:27:38:884 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============abb9d11a> ]
[2024-10-02 17:27:38:893 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@480dae8b, sessionId:abb9d11a
[2024-10-02 17:27:41:779 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:46:033 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3451MB usedMemory:645MB
[2024-10-02 17:27:47:306 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:27:47:932 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 33867a49> ]
[2024-10-02 17:27:47:954 INFO](AccountDao.java:25)新建用户：qwer520
[2024-10-02 17:27:54:314 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:28:00:772 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:28:04:175 ERROR](CommandProcessor.java:174)处理消息[3073,command:game.server.logic.mail.handler.ReqGetMailAdjunctHandler]耗时:11
[2024-10-02 17:28:08:623 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"-7_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:28:21:676 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 17:29:10:671 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10018_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:29:25:950 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10019_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:29:34:436 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10020_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:29:52:574 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 786c48a6> ]
[2024-10-02 17:29:53:037 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536883710
[2024-10-02 17:29:53:037 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536883741
[2024-10-02 17:29:53:037 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536883742
[2024-10-02 17:29:53:037 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536883768
[2024-10-02 17:29:53:061 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536883710, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 17:29:53:061 ERROR](RechargeFinishScript.java:269)[ 玩家 536883041 的充值商品:1305 订单号： 536883710 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 17:29:53:091 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536883741, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 17:29:53:092 ERROR](RechargeFinishScript.java:269)[ 玩家 536883041 的充值商品:1305 订单号： 536883741 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 17:29:53:119 ERROR](RechargeFinishScript.java:81)收到充值商品:1304 订单信息:[ 支付类型:3 , 订单号: 536883742, 充值金额: 988.0充值个数:1 ]
[2024-10-02 17:29:53:119 ERROR](RechargeFinishScript.java:269)[ 玩家 536883041 的充值商品:1304 订单号： 536883742 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 17:29:53:148 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536883768, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 17:29:53:148 ERROR](RechargeFinishScript.java:269)[ 玩家 536883041 的充值商品:1305 订单号： 536883768 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 17:30:01:478 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10064_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:30:10:512 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10064_100"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:30:19:655 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 33867a49> ]
[2024-10-02 17:30:19:655 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============33867a49> ]
[2024-10-02 17:30:19:656 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====33867a49> ]
[2024-10-02 17:30:19:713 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10066_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:30:19:796 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============33867a49> ]
[2024-10-02 17:30:21:678 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 3
[2024-10-02 17:30:28:773 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:17
[2024-10-02 17:30:31:437 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10066_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:30:41:591 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10067_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:30:48:503 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： e14010ae> ]
[2024-10-02 17:30:49:110 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10068_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:30:55:736 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10069_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:31:09:391 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10070_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:31:18:996 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： e14010ae> ]
[2024-10-02 17:31:18:996 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e14010ae> ]
[2024-10-02 17:31:18:996 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====e14010ae> ]
[2024-10-02 17:31:19:137 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e14010ae> ]
[2024-10-02 17:31:19:793 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10071_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:31:28:394 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10072_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:31:33:979 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 65eb18e3> ]
[2024-10-02 17:31:41:265 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10073_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:31:42:513 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 65eb18e3> ]
[2024-10-02 17:31:42:514 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============65eb18e3> ]
[2024-10-02 17:31:47:711 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10074_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:31:56:295 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10075_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:04:868 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10076_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:10:092 ERROR](CommandProcessor.java:145)java.lang.ArrayIndexOutOfBoundsException: 80
at game.server.logic.comboSkill.ComboSkillManager.skillLevelUp(ComboSkillManager.java:174)
at game.server.logic.comboSkill.handler.ReqComboSkillLevelUpHandler.action(ReqComboSkillLevelUpHandler.java:21)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

,threadName:GameLine_0,command:game.server.logic.comboSkill.handler.ReqComboSkillLevelUpHandler
[2024-10-02 17:32:10:639 ERROR](CommandProcessor.java:145)java.lang.ArrayIndexOutOfBoundsException: 80
at game.server.logic.comboSkill.ComboSkillManager.skillLevelUp(ComboSkillManager.java:174)
at game.server.logic.comboSkill.handler.ReqComboSkillLevelUpHandler.action(ReqComboSkillLevelUpHandler.java:21)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

,threadName:GameLine_0,command:game.server.logic.comboSkill.handler.ReqComboSkillLevelUpHandler
[2024-10-02 17:32:11:333 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10077_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:20:613 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10078_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:34:158 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10079_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:40:681 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887653","productid":1206,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:40:686 INFO](RechargeService.java:509)玩家[roleId:536887653]请求充值,payId[1206],开始丢给RechargeProcessor处理
[2024-10-02 17:32:40:686 ERROR](UpdateRechargeBeanScript.java:38)充值状态修改失败: 536887654当前状态：2
[2024-10-02 17:32:40:687 ERROR](UpdateRechargeBeanScript.java:52)角色不在线，离线充值: 536887654
[2024-10-02 17:32:40:687 ERROR](RechargeService.java:442)订单[536887654] 脚本支付流程！
[2024-10-02 17:32:40:699 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 17:32:43:282 ERROR](CommandProcessor.java:145)java.lang.ArrayIndexOutOfBoundsException: 80
at game.server.logic.comboSkill.ComboSkillManager.skillLevelUp(ComboSkillManager.java:174)
at game.server.logic.comboSkill.handler.ReqComboSkillLevelUpHandler.action(ReqComboSkillLevelUpHandler.java:21)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

,threadName:GameLine_0,command:game.server.logic.comboSkill.handler.ReqComboSkillLevelUpHandler
[2024-10-02 17:32:46:033 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3841MB usedMemory:255MB
[2024-10-02 17:32:47:448 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10080_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:32:53:871 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10081_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:33:03:272 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10082_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:33:04:192 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887653","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14212_999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:33:04:201 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887653
[2024-10-02 17:33:11:623 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10083_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:33:28:219 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10108_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:33:35:328 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 786c48a6> ]
[2024-10-02 17:33:35:328 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============786c48a6> ]
[2024-10-02 17:33:39:371 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10109_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:34:04:411 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10110_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:34:21:676 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 17:34:38:205 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:7928
[2024-10-02 17:34:47:593 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10110_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:34:53:050 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10110_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:35:12:211 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10115_99999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:35:46:871 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10180_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:36:21:676 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 17:37:28:430 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14244_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:37:39:665 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14245_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:37:46:034 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3542MB usedMemory:554MB
[2024-10-02 17:37:54:155 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14251_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:38:35:702 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"17010_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:38:44:835 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"17007_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:38:54:932 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"17009_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:39:04:963 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"18001_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:39:19:972 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"18007_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:39:38:150 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400107_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:39:38:151 ERROR](BeanFactory.java:213)超过1星专属宝物[400107]不能创建进入背包!
[2024-10-02 17:39:38:151 ERROR](Mail.java:311)邮件系统创建的道具 ID：[400107] 不存在
[2024-10-02 17:39:49:071 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400107_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:39:49:071 ERROR](BeanFactory.java:213)超过1星专属宝物[400107]不能创建进入背包!
[2024-10-02 17:39:49:075 ERROR](Mail.java:311)邮件系统创建的道具 ID：[400107] 不存在
[2024-10-02 17:39:57:741 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400107_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:39:57:741 ERROR](BeanFactory.java:213)超过1星专属宝物[400107]不能创建进入背包!
[2024-10-02 17:39:57:742 ERROR](Mail.java:311)邮件系统创建的道具 ID：[400107] 不存在
[2024-10-02 17:40:08:564 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400106_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:40:08:564 ERROR](BeanFactory.java:213)超过1星专属宝物[400106]不能创建进入背包!
[2024-10-02 17:40:08:568 ERROR](Mail.java:311)邮件系统创建的道具 ID：[400106] 不存在
[2024-10-02 17:40:16:455 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"19120_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:40:21:677 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 17:40:25:660 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400100_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:40:37:361 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400101_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:40:48:097 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"400102_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:40:48:097 ERROR](BeanFactory.java:213)超过1星专属宝物[400102]不能创建进入背包!
[2024-10-02 17:40:48:101 ERROR](Mail.java:311)邮件系统创建的道具 ID：[400102] 不存在
[2024-10-02 17:41:34:259 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 3685610b> ]
[2024-10-02 17:41:34:260 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============3685610b> ]
[2024-10-02 17:41:34:260 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====3685610b> ]
[2024-10-02 17:41:34:413 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============3685610b> ]
[2024-10-02 17:42:46:034 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3870MB usedMemory:226MB
[2024-10-02 17:47:46:035 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3849MB usedMemory:247MB
[2024-10-02 17:49:29:391 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 295bd037> ]
[2024-10-02 17:49:29:611 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887654
[2024-10-02 17:49:29:634 ERROR](RechargeFinishScript.java:81)收到充值商品:1206 订单信息:[ 支付类型:3 , 订单号: 536887654, 充值金额: 39680.0充值个数:1 ]
[2024-10-02 17:49:29:658 ERROR](RechargeFinishScript.java:250)[ 玩家：536887653的充值商品:1206 ] 充值完成，订单状态修改为 4！
[2024-10-02 17:50:21:676 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 0 , 耗时： 0
[2024-10-02 17:52:21:678 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 2
[2024-10-02 17:52:46:035 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3712MB usedMemory:384MB
[2024-10-02 17:53:03:473 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 295bd037> ]
[2024-10-02 17:53:03:473 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============295bd037> ]
[2024-10-02 17:53:03:474 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====295bd037> ]
[2024-10-02 17:53:03:595 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============295bd037> ]
[2024-10-02 17:53:46:847 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887653","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14144_50"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:53:46:856 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887653
[2024-10-02 17:54:00:871 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887653","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14213_50"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 17:54:00:881 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887653
[2024-10-02 17:54:03:285 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 5f9f0c03> ]
[2024-10-02 17:54:21:677 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 17:55:40:973 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 5f9f0c03> ]
[2024-10-02 17:55:40:974 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============5f9f0c03> ]
[2024-10-02 17:57:46:049 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3563MB usedMemory:533MB
[2024-10-02 17:58:06:666 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x2feb50ee, /***************:65206 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 17:58:06:887 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============2feb50ee> ]
[2024-10-02 18:02:46:049 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3543MB usedMemory:553MB
[2024-10-02 18:07:46:050 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3523MB usedMemory:573MB
[2024-10-02 18:10:16:753 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x7cf876e7, /************:42308 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 18:10:16:979 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7cf876e7> ]
[2024-10-02 18:12:46:050 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3888MB usedMemory:208MB
[2024-10-02 18:17:46:051 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3869MB usedMemory:227MB
[2024-10-02 18:20:54:864 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： a5d777b1> ]
[2024-10-02 18:20:55:055 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887599
[2024-10-02 18:20:55:078 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887599, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 18:20:55:089 ERROR](RechargeFinishScript.java:250)[ 玩家：*********的充值商品:1305 ] 充值完成，订单状态修改为 4！
[2024-10-02 18:21:24:701 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"403700_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 18:22:21:678 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 3 , 耗时： 1
[2024-10-02 18:22:46:051 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3730MB usedMemory:366MB
[2024-10-02 18:23:18:167 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： a5d777b1> ]
[2024-10-02 18:23:18:167 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============a5d777b1> ]
[2024-10-02 18:23:18:168 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====a5d777b1> ]
[2024-10-02 18:23:18:555 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============a5d777b1> ]
[2024-10-02 18:24:52:502 INFO](CrossServer.java:247)client session, sessionId: 9 create
[2024-10-02 18:24:57:047 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xa517e66a, /***************:64709 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 18:24:57:770 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============a517e66a> ]
[2024-10-02 18:27:46:052 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3692MB usedMemory:404MB
[2024-10-02 18:32:46:052 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3674MB usedMemory:422MB
[2024-10-02 18:36:00:810 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： a7614275> ]
[2024-10-02 18:36:00:848 INFO](SessionUtils.java:19)sessionId:a7614275-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:a7614275, reason=5,closing:false
[2024-10-02 18:36:00:911 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： a7614275> ]
[2024-10-02 18:36:00:912 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============a7614275> ]
[2024-10-02 18:36:00:921 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@329279e4, sessionId:a7614275
[2024-10-02 18:36:20:962 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： eceb6dd4> ]
[2024-10-02 18:36:20:975 INFO](SessionUtils.java:19)sessionId:eceb6dd4-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:eceb6dd4, reason=15,closing:false
[2024-10-02 18:36:21:037 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： eceb6dd4> ]
[2024-10-02 18:36:21:037 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============eceb6dd4> ]
[2024-10-02 18:36:21:046 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@5c60c11b, sessionId:eceb6dd4
[2024-10-02 18:36:22:515 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 572d317c> ]
[2024-10-02 18:36:22:527 INFO](SessionUtils.java:19)sessionId:572d317c-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:572d317c, reason=15,closing:false
[2024-10-02 18:36:22:583 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 572d317c> ]
[2024-10-02 18:36:22:583 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============572d317c> ]
[2024-10-02 18:36:22:592 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@555b9049, sessionId:572d317c
[2024-10-02 18:36:33:702 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 4eb773cb> ]
[2024-10-02 18:36:33:716 INFO](SessionUtils.java:19)sessionId:4eb773cb-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:4eb773cb, reason=15,closing:false
[2024-10-02 18:36:33:771 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 4eb773cb> ]
[2024-10-02 18:36:33:771 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4eb773cb> ]
[2024-10-02 18:36:33:780 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@3327d4f6, sessionId:4eb773cb
[2024-10-02 18:36:35:277 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 30cccf21> ]
[2024-10-02 18:36:35:289 INFO](SessionUtils.java:19)sessionId:30cccf21-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:30cccf21, reason=15,closing:false
[2024-10-02 18:36:35:362 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 30cccf21> ]
[2024-10-02 18:36:35:365 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============30cccf21> ]
[2024-10-02 18:36:35:373 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@978dab2, sessionId:30cccf21
[2024-10-02 18:36:35:622 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 093e068d> ]
[2024-10-02 18:36:35:635 INFO](SessionUtils.java:19)sessionId:093e068d-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:093e068d, reason=15,closing:false
[2024-10-02 18:36:35:685 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 093e068d> ]
[2024-10-02 18:36:35:685 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============093e068d> ]
[2024-10-02 18:36:35:694 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@5ee758c1, sessionId:093e068d
[2024-10-02 18:36:36:251 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============23b460af> ]
[2024-10-02 18:36:45:162 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 497db16f> ]
[2024-10-02 18:36:45:175 INFO](SessionUtils.java:19)sessionId:497db16f-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:497db16f, reason=5,closing:false
[2024-10-02 18:36:45:232 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 497db16f> ]
[2024-10-02 18:36:45:233 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============497db16f> ]
[2024-10-02 18:36:45:247 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@75d6e44e, sessionId:497db16f
[2024-10-02 18:37:02:730 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： e9b1ce65> ]
[2024-10-02 18:37:02:765 INFO](SessionUtils.java:19)sessionId:e9b1ce65-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:e9b1ce65, reason=15,closing:false
[2024-10-02 18:37:02:938 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： e9b1ce65> ]
[2024-10-02 18:37:02:939 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e9b1ce65> ]
[2024-10-02 18:37:02:947 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@6163d2bc, sessionId:e9b1ce65
[2024-10-02 18:37:24:334 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： efcb2c6b> ]
[2024-10-02 18:37:24:347 INFO](SessionUtils.java:19)sessionId:efcb2c6b-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:efcb2c6b, reason=15,closing:false
[2024-10-02 18:37:24:407 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： efcb2c6b> ]
[2024-10-02 18:37:24:407 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============efcb2c6b> ]
[2024-10-02 18:37:24:416 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@7f58cdb2, sessionId:efcb2c6b
[2024-10-02 18:37:25:179 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 9e103a18> ]
[2024-10-02 18:37:25:192 INFO](SessionUtils.java:19)sessionId:9e103a18-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:9e103a18, reason=15,closing:false
[2024-10-02 18:37:25:245 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 9e103a18> ]
[2024-10-02 18:37:25:245 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9e103a18> ]
[2024-10-02 18:37:25:254 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@3bf89f8e, sessionId:9e103a18
[2024-10-02 18:37:25:575 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 4e15e04a> ]
[2024-10-02 18:37:25:588 INFO](SessionUtils.java:19)sessionId:4e15e04a-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:4e15e04a, reason=15,closing:false
[2024-10-02 18:37:25:644 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 4e15e04a> ]
[2024-10-02 18:37:25:644 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4e15e04a> ]
[2024-10-02 18:37:25:653 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@5e3fd9e7, sessionId:4e15e04a
[2024-10-02 18:37:25:964 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 2c41f2d2> ]
[2024-10-02 18:37:25:981 INFO](SessionUtils.java:19)sessionId:2c41f2d2-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:2c41f2d2, reason=15,closing:false
[2024-10-02 18:37:26:045 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 2c41f2d2> ]
[2024-10-02 18:37:26:045 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============2c41f2d2> ]
[2024-10-02 18:37:26:054 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@4994a1b2, sessionId:2c41f2d2
[2024-10-02 18:37:26:403 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============78dc1836> ]
[2024-10-02 18:37:46:052 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3620MB usedMemory:476MB
[2024-10-02 18:42:46:053 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3478MB usedMemory:618MB
[2024-10-02 18:47:46:053 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3460MB usedMemory:636MB
[2024-10-02 18:52:16:718 INFO](CrossServer.java:257)client session:9,ip:null close
[2024-10-02 18:52:46:054 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3440MB usedMemory:656MB
[2024-10-02 18:57:46:068 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3421MB usedMemory:675MB
[2024-10-02 19:02:46:068 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3402MB usedMemory:694MB
[2024-10-02 19:07:46:068 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3885MB usedMemory:211MB
[2024-10-02 19:10:54:946 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： e4ef21c5> ]
[2024-10-02 19:10:54:986 INFO](SessionUtils.java:19)sessionId:e4ef21c5-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:e4ef21c5, reason=5,closing:false
[2024-10-02 19:10:55:113 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： e4ef21c5> ]
[2024-10-02 19:10:55:113 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============e4ef21c5> ]
[2024-10-02 19:10:55:122 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@2ec044d8, sessionId:e4ef21c5
[2024-10-02 19:10:57:970 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 9de72cd6> ]
[2024-10-02 19:10:57:988 INFO](AccountDao.java:25)新建用户：********
[2024-10-02 19:12:21:681 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 2
[2024-10-02 19:12:46:069 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3627MB usedMemory:469MB
[2024-10-02 19:13:06:175 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 9de72cd6> ]
[2024-10-02 19:13:06:176 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9de72cd6> ]
[2024-10-02 19:13:06:176 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====9de72cd6> ]
[2024-10-02 19:13:06:312 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============9de72cd6> ]
[2024-10-02 19:17:46:069 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3598MB usedMemory:498MB
[2024-10-02 19:22:46:070 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3580MB usedMemory:516MB
[2024-10-02 19:27:46:070 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3562MB usedMemory:534MB
[2024-10-02 19:30:00:189 INFO](IndigoService.java:627)===========报名阶段结束》》》》》》》》》》》》》准备阶段开始==========================
[2024-10-02 19:30:00:194 INFO](TimeOpenProcessor.java:198)Wed Oct 02 19:30:00 GMT+08:00 2024===========限时功能28====倒计时
[2024-10-02 19:30:00:194 INFO](TimeOpenProcessor.java:198)Wed Oct 02 19:30:00 GMT+08:00 2024===========限时功能108====倒计时
[2024-10-02 19:32:46:073 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3543MB usedMemory:553MB
[2024-10-02 19:32:46:371 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 4ebefbed> ]
[2024-10-02 19:32:46:410 INFO](SessionUtils.java:19)sessionId:4ebefbed-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:4ebefbed, reason=5,closing:false
[2024-10-02 19:32:46:486 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 4ebefbed> ]
[2024-10-02 19:32:46:486 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4ebefbed> ]
[2024-10-02 19:32:46:496 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@20e3fe3, sessionId:4ebefbed
[2024-10-02 19:33:00:191 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 927dbaba> ]
[2024-10-02 19:33:00:208 INFO](AccountDao.java:25)新建用户：asd0987
[2024-10-02 19:33:03:938 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 301cd6f3> ]
[2024-10-02 19:33:16:522 ERROR](CommandProcessor.java:174)处理消息[533,command:game.server.logic.player.handler.GetSpecialCardRewardHandler]耗时:29
[2024-10-02 19:33:23:423 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:33
[2024-10-02 19:34:21:688 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 37 , 耗时： 8
[2024-10-02 19:35:03:445 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 927dbaba> ]
[2024-10-02 19:35:03:446 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============927dbaba> ]
[2024-10-02 19:35:03:854 ERROR](CommandProcessor.java:174)处理消息[6672,command:game.server.logic.research.handler.ReqDecomOnekeyHandler]耗时:66
[2024-10-02 19:35:24:368 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[530],开始丢给RechargeProcessor处理
[2024-10-02 19:35:24:369 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 19:35:24:369 ERROR](RechargeFinishScript.java:81)收到充值商品:530 订单信息:[ 支付类型:1 , 订单号: 536887657, 充值金额: 6.0充值个数:99 ]
[2024-10-02 19:35:24:381 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:530 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:35:24:381 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 19:35:33:410 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[532],开始丢给RechargeProcessor处理
[2024-10-02 19:35:33:410 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 19:35:33:411 ERROR](RechargeFinishScript.java:81)收到充值商品:532 订单信息:[ 支付类型:1 , 订单号: 536887658, 充值金额: 30.0充值个数:999 ]
[2024-10-02 19:35:33:424 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:532 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:35:33:424 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 19:35:38:703 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[531],开始丢给RechargeProcessor处理
[2024-10-02 19:35:38:703 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 19:35:38:704 ERROR](RechargeFinishScript.java:81)收到充值商品:531 订单信息:[ 支付类型:1 , 订单号: 536887659, 充值金额: 30.0充值个数:99 ]
[2024-10-02 19:35:38:715 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:531 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:35:38:716 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 19:36:05:110 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[537],开始丢给RechargeProcessor处理
[2024-10-02 19:36:05:110 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 19:36:05:111 ERROR](RechargeFinishScript.java:81)收到充值商品:537 订单信息:[ 支付类型:1 , 订单号: 536887660, 充值金额: 328.0充值个数:25 ]
[2024-10-02 19:36:05:125 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:537 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:36:05:125 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:15
[2024-10-02 19:36:21:682 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 11 , 耗时： 2
[2024-10-02 19:37:46:073 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3714MB usedMemory:382MB
[2024-10-02 19:38:21:681 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 1
[2024-10-02 19:38:25:839 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 095596d5> ]
[2024-10-02 19:39:16:555 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 095596d5> ]
[2024-10-02 19:39:16:555 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============095596d5> ]
[2024-10-02 19:39:16:557 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====095596d5> ]
[2024-10-02 19:39:16:703 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============095596d5> ]
[2024-10-02 19:39:55:331 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0x7fbd4432, /*************:65015 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 19:39:55:573 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============7fbd4432> ]
[2024-10-02 19:40:20:628 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 301cd6f3> ]
[2024-10-02 19:40:20:628 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============301cd6f3> ]
[2024-10-02 19:40:21:682 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 12 , 耗时： 2
[2024-10-02 19:40:37:923 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： f34aee1b> ]
[2024-10-02 19:42:21:683 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 3
[2024-10-02 19:42:35:857 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:16
[2024-10-02 19:42:46:073 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3602MB usedMemory:494MB
[2024-10-02 19:45:14:389 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:43
[2024-10-02 19:46:35:767 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： f34aee1b> ]
[2024-10-02 19:46:35:767 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============f34aee1b> ]
[2024-10-02 19:46:35:767 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====f34aee1b> ]
[2024-10-02 19:46:35:864 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============f34aee1b> ]
[2024-10-02 19:47:38:026 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 75df9e96> ]
[2024-10-02 19:47:46:074 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3461MB usedMemory:635MB
[2024-10-02 19:47:59:186 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xdaa06bb7, /*************:60828 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 19:47:59:338 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============daa06bb7> ]
[2024-10-02 19:49:57:470 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 315afa75> ]
[2024-10-02 19:49:57:659 INFO](SessionUtils.java:19)sessionId:75df9e96-->close [because] --LoginService----->[Login]  ：客户端被顶号   game.core.pub.session.Session@aeb8239,sessionId:315afa75, oldSession=75df9e96,closing:false
[2024-10-02 19:50:07:684 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 75df9e96> ]
[2024-10-02 19:50:07:685 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============75df9e96> ]
[2024-10-02 19:50:28:551 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 315afa75> ]
[2024-10-02 19:50:28:552 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============315afa75> ]
[2024-10-02 19:50:28:552 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====315afa75> ]
[2024-10-02 19:50:28:871 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============315afa75> ]
[2024-10-02 19:52:46:074 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3828MB usedMemory:268MB
[2024-10-02 19:53:03:298 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887656","productid":49,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 19:53:03:302 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[49],开始丢给RechargeProcessor处理
[2024-10-02 19:53:03:311 ERROR](UpdateRechargeBeanScript.java:52)角色不在线，离线充值: 536887661
[2024-10-02 19:53:03:311 ERROR](RechargeService.java:442)订单[536887661] 脚本支付流程！
[2024-10-02 19:53:03:335 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 19:53:19:499 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14215_200"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 19:53:19:508 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887656
[2024-10-02 19:53:37:216 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14212_200"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 19:53:37:228 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887656
[2024-10-02 19:53:53:262 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： b85343ba> ]
[2024-10-02 19:53:53:451 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887661
[2024-10-02 19:53:53:482 ERROR](RechargeFinishScript.java:81)收到充值商品:49 订单信息:[ 支付类型:3 , 订单号: 536887661, 充值金额: 64800.0充值个数:1 ]
[2024-10-02 19:53:53:484 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:49 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:54:21:686 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 5
[2024-10-02 19:55:06:411 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:12
[2024-10-02 19:55:53:473 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887656","productid":1106,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 19:55:53:473 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1106],开始丢给RechargeProcessor处理
[2024-10-02 19:55:53:491 ERROR](RechargeFinishScript.java:81)收到充值商品:1106 订单信息:[ 支付类型:3 , 订单号: 536887662, 充值金额: 49688.0充值个数:1 ]
[2024-10-02 19:55:53:497 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1106 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:55:53:497 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887662 ， 玩家 : 536887656 ]
[2024-10-02 19:55:53:497 ERROR](RechargeService.java:442)订单[536887662] 脚本支付流程！
[2024-10-02 19:55:53:508 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 19:56:21:684 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 15 , 耗时： 3
[2024-10-02 19:56:38:992 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[905],开始丢给RechargeProcessor处理
[2024-10-02 19:56:38:992 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:56:38:992 ERROR](RechargeFinishScript.java:81)收到充值商品:905 订单信息:[ 支付类型:1 , 订单号: 536887663, 充值金额: 18888.0充值个数:1 ]
[2024-10-02 19:56:38:999 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:905 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:56:44:643 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[904],开始丢给RechargeProcessor处理
[2024-10-02 19:56:44:644 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:56:44:644 ERROR](RechargeFinishScript.java:81)收到充值商品:904 订单信息:[ 支付类型:1 , 订单号: 536887664, 充值金额: 8888.0充值个数:1 ]
[2024-10-02 19:56:44:646 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:904 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:56:59:584 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[430],开始丢给RechargeProcessor处理
[2024-10-02 19:56:59:584 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:56:59:585 ERROR](RechargeFinishScript.java:81)收到充值商品:430 订单信息:[ 支付类型:1 , 订单号: 536887665, 充值金额: 6.0充值个数:1 ]
[2024-10-02 19:56:59:596 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:430 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:56:59:596 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 19:57:06:318 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[431],开始丢给RechargeProcessor处理
[2024-10-02 19:57:06:318 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:06:318 ERROR](RechargeFinishScript.java:81)收到充值商品:431 订单信息:[ 支付类型:1 , 订单号: 536887666, 充值金额: 98.0充值个数:1 ]
[2024-10-02 19:57:06:330 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:431 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:57:06:330 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 19:57:12:956 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[432],开始丢给RechargeProcessor处理
[2024-10-02 19:57:12:956 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:12:957 ERROR](RechargeFinishScript.java:81)收到充值商品:432 订单信息:[ 支付类型:1 , 订单号: 536887667, 充值金额: 328.0充值个数:1 ]
[2024-10-02 19:57:12:968 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:432 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:57:12:969 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 19:57:17:917 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[433],开始丢给RechargeProcessor处理
[2024-10-02 19:57:17:917 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:17:918 ERROR](RechargeFinishScript.java:81)收到充值商品:433 订单信息:[ 支付类型:1 , 订单号: 536887668, 充值金额: 648.0充值个数:1 ]
[2024-10-02 19:57:17:933 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:433 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:57:17:933 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:16
[2024-10-02 19:57:34:886 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[502],开始丢给RechargeProcessor处理
[2024-10-02 19:57:34:886 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:34:886 ERROR](RechargeFinishScript.java:81)收到充值商品:502 订单信息:[ 支付类型:1 , 订单号: 536887669, 充值金额: 88.0充值个数:1 ]
[2024-10-02 19:57:34:894 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:502 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:57:37:332 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[501],开始丢给RechargeProcessor处理
[2024-10-02 19:57:37:332 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:37:332 ERROR](RechargeFinishScript.java:81)收到充值商品:501 订单信息:[ 支付类型:1 , 订单号: 536887670, 充值金额: 30.0充值个数:1 ]
[2024-10-02 19:57:37:365 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:501 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:57:37:365 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:33
[2024-10-02 19:57:38:823 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[500],开始丢给RechargeProcessor处理
[2024-10-02 19:57:38:823 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:38:824 ERROR](RechargeFinishScript.java:81)收到充值商品:500 订单信息:[ 支付类型:1 , 订单号: 536887671, 充值金额: 6.0充值个数:1 ]
[2024-10-02 19:57:38:831 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:500 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:57:46:075 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3668MB usedMemory:428MB
[2024-10-02 19:57:53:693 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[6],开始丢给RechargeProcessor处理
[2024-10-02 19:57:53:693 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:57:53:693 ERROR](RechargeFinishScript.java:81)收到充值商品:6 订单信息:[ 支付类型:1 , 订单号: 536887672, 充值金额: 648.0充值个数:1 ]
[2024-10-02 19:57:53:701 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:6 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:58:05:818 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1004],开始丢给RechargeProcessor处理
[2024-10-02 19:58:05:818 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:58:05:818 ERROR](RechargeFinishScript.java:81)收到充值商品:1004 订单信息:[ 支付类型:1 , 订单号: 536887673, 充值金额: 988.0充值个数:1 ]
[2024-10-02 19:58:05:827 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1004 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:58:09:211 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1005],开始丢给RechargeProcessor处理
[2024-10-02 19:58:09:211 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:58:09:211 ERROR](RechargeFinishScript.java:81)收到充值商品:1005 订单信息:[ 支付类型:1 , 订单号: 536887674, 充值金额: 988.0充值个数:1 ]
[2024-10-02 19:58:09:219 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1005 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:58:17:171 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[14],开始丢给RechargeProcessor处理
[2024-10-02 19:58:17:171 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 19:58:17:171 ERROR](RechargeFinishScript.java:81)收到充值商品:14 订单信息:[ 支付类型:1 , 订单号: 536887675, 充值金额: 188.0充值个数:1 ]
[2024-10-02 19:58:17:172 ERROR](BuyLimittimeGiftScript.java:83)限时礼包购买成功,playerId:536887656,type:0,orderId:536887675,gift:{"cdTime":1727913497172,"endTime":0,"heroId":0,"type":0}
[2024-10-02 19:58:17:173 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:14 ] 充值完成，订单状态修改为 4！
[2024-10-02 19:58:21:689 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 55 , 耗时： 8
[2024-10-02 19:58:58:191 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:17
[2024-10-02 19:59:39:885 ERROR](CommandProcessor.java:174)处理消息[4866,command:game.server.logic.rank.handker.PraiseHandler]耗时:18
[2024-10-02 19:59:41:639 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:40
[2024-10-02 20:00:00:451 INFO](TimeOpenProcessor.java:212)Wed Oct 02 20:00:00 GMT+08:00 2024===========限时功能28====进行中
[2024-10-02 20:00:00:451 INFO](IndigoService.java:645)============人数不足,比赛未开始==========================
[2024-10-02 20:00:00:451 INFO](TimeOpenProcessor.java:212)Wed Oct 02 20:00:00 GMT+08:00 2024===========限时功能108====进行中
[2024-10-02 20:00:04:534 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1011],开始丢给RechargeProcessor处理
[2024-10-02 20:00:04:534 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 20:00:04:534 ERROR](RechargeFinishScript.java:81)收到充值商品:1011 订单信息:[ 支付类型:1 , 订单号: 536887676, 充值金额: 98.0充值个数:1 ]
[2024-10-02 20:00:04:546 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1011 ] 充值完成，订单状态修改为 4！
[2024-10-02 20:00:04:547 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 20:00:08:799 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1012],开始丢给RechargeProcessor处理
[2024-10-02 20:00:08:800 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 20:00:08:800 ERROR](RechargeFinishScript.java:81)收到充值商品:1012 订单信息:[ 支付类型:1 , 订单号: 536887677, 充值金额: 198.0充值个数:1 ]
[2024-10-02 20:00:08:817 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1012 ] 充值完成，订单状态修改为 4！
[2024-10-02 20:00:08:817 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:18
[2024-10-02 20:00:10:216 ERROR](CommandProcessor.java:174)处理消息[1204,command:game.server.logic.fund.handler.ReqResShopRewardHandler]耗时:12
[2024-10-02 20:00:12:231 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1013],开始丢给RechargeProcessor处理
[2024-10-02 20:00:12:231 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 20:00:12:231 ERROR](RechargeFinishScript.java:81)收到充值商品:1013 订单信息:[ 支付类型:1 , 订单号: 536887678, 充值金额: 328.0充值个数:1 ]
[2024-10-02 20:00:12:244 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1013 ] 充值完成，订单状态修改为 4！
[2024-10-02 20:00:12:244 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 20:00:15:314 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1014],开始丢给RechargeProcessor处理
[2024-10-02 20:00:15:314 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 20:00:15:314 ERROR](RechargeFinishScript.java:81)收到充值商品:1014 订单信息:[ 支付类型:1 , 订单号: 536887679, 充值金额: 328.0充值个数:1 ]
[2024-10-02 20:00:15:337 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1014 ] 充值完成，订单状态修改为 4！
[2024-10-02 20:00:15:338 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:24
[2024-10-02 20:00:18:371 INFO](RechargeService.java:509)玩家[roleId:536887656]请求充值,payId[1015],开始丢给RechargeProcessor处理
[2024-10-02 20:00:18:371 INFO](RechargeService.java:329)玩家[roleId:536887656]代币充值    订单生成  成功
[2024-10-02 20:00:18:372 ERROR](RechargeFinishScript.java:81)收到充值商品:1015 订单信息:[ 支付类型:1 , 订单号: 536887680, 充值金额: 648.0充值个数:1 ]
[2024-10-02 20:00:18:385 ERROR](RechargeFinishScript.java:250)[ 玩家：536887656的充值商品:1015 ] 充值完成，订单状态修改为 4！
[2024-10-02 20:00:18:385 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 20:00:21:684 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 5 , 耗时： 3
[2024-10-02 20:00:58:110 ERROR](BackpackService.java:1463)N选1宝箱：14215领取索引超过掉落索引
[2024-10-02 20:01:28:111 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： b85343ba> ]
[2024-10-02 20:01:28:111 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b85343ba> ]
[2024-10-02 20:01:28:111 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====b85343ba> ]
[2024-10-02 20:01:28:272 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b85343ba> ]
[2024-10-02 20:01:32:589 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12103_15000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:01:32:600 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887656
[2024-10-02 20:01:44:943 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12104_15000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:01:44:953 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887656
[2024-10-02 20:01:56:752 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"12111_15000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:01:56:762 INFO](MailService.java:400)邮件发送=======标题：系统邮件====playerId=536887656
[2024-10-02 20:01:59:020 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 3cfe3342> ]
[2024-10-02 20:02:21:683 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 8 , 耗时： 2
[2024-10-02 20:02:46:075 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3444MB usedMemory:652MB
[2024-10-02 20:03:03:346 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"13057_5000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:04:06:669 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"10110_80000"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:04:21:683 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 10 , 耗时： 2
[2024-10-02 20:04:41:515 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:8431
[2024-10-02 20:06:21:683 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 10 , 耗时： 2
[2024-10-02 20:06:22:201 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"536887656","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"402700_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:07:22:186 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 3cfe3342> ]
[2024-10-02 20:07:22:187 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============3cfe3342> ]
[2024-10-02 20:07:46:075 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3430MB usedMemory:666MB
[2024-10-02 20:12:46:076 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3797MB usedMemory:299MB
[2024-10-02 20:16:05:528 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： d9c75822> ]
[2024-10-02 20:16:12:100 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:16
[2024-10-02 20:16:38:704 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14188_1"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:17:01:623 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:19
[2024-10-02 20:17:25:433 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14188_23"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:17:46:076 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3632MB usedMemory:464MB
[2024-10-02 20:18:21:689 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 9 , 耗时： 7
[2024-10-02 20:19:58:148 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14065_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:20:20:831 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14066_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:20:21:683 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 7 , 耗时： 1
[2024-10-02 20:20:27:270 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14067_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:20:33:333 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14068_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:21:42:382 ERROR](CommandProcessor.java:174)处理消息[777,command:game.server.logic.hero.handler.PutOnWareHandler]耗时:64
[2024-10-02 20:22:21:685 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 24 , 耗时： 3
[2024-10-02 20:22:46:077 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3565MB usedMemory:531MB
[2024-10-02 20:22:47:342 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14101_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:22:53:280 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"mail","playerid":"*********","mail":{"sender":"GM","title":"\u7cfb\u7edf\u90ae\u4ef6","content":"\u7cfb\u7edf\u90ae\u4ef6","reward":"14101_9999"},"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 20:24:21:687 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 48 , 耗时： 5
[2024-10-02 20:25:06:241 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： d9c75822> ]
[2024-10-02 20:25:06:242 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d9c75822> ]
[2024-10-02 20:25:06:242 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====d9c75822> ]
[2024-10-02 20:25:06:454 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d9c75822> ]
[2024-10-02 20:27:46:078 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3501MB usedMemory:595MB
[2024-10-02 20:29:00:707 INFO](TimeOpenProcessor.java:225)Wed Oct 02 20:29:00 GMT+08:00 2024===========限时功能28====停止中
[2024-10-02 20:29:00:707 INFO](TimeOpenProcessor.java:225)Wed Oct 02 20:29:00 GMT+08:00 2024===========限时功能108====停止中
[2024-10-02 20:32:46:078 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3482MB usedMemory:614MB
[2024-10-02 20:37:46:079 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3462MB usedMemory:634MB
[2024-10-02 20:42:46:079 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3829MB usedMemory:267MB
[2024-10-02 20:47:46:080 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3811MB usedMemory:285MB
[2024-10-02 20:52:46:080 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3792MB usedMemory:304MB
[2024-10-02 20:57:46:080 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3773MB usedMemory:323MB
[2024-10-02 21:00:00:937 INFO](WorldBossService.java:351)---start WorldBoss----
[2024-10-02 21:00:00:937 INFO](SnatchTerritoryService.java:787)所有道馆馆主竞拍结束结束 -----------------
[2024-10-02 21:02:46:081 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3754MB usedMemory:342MB
[2024-10-02 21:07:46:081 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3736MB usedMemory:360MB
[2024-10-02 21:12:46:082 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3593MB usedMemory:503MB
[2024-10-02 21:17:46:082 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3575MB usedMemory:521MB
[2024-10-02 21:19:00:394 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 8d76ef71> ]
[2024-10-02 21:19:24:109 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 8d76ef71> ]
[2024-10-02 21:19:24:109 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============8d76ef71> ]
[2024-10-02 21:20:05:265 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： ac6bc0ba> ]
[2024-10-02 21:20:25:895 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:12
[2024-10-02 21:20:31:502 ERROR](CommandProcessor.java:174)处理消息[516,command:game.server.logic.player.handler.GetPlayerInfoHandler]耗时:21
[2024-10-02 21:21:23:494 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： ac6bc0ba> ]
[2024-10-02 21:21:23:494 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ac6bc0ba> ]
[2024-10-02 21:21:23:495 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====ac6bc0ba> ]
[2024-10-02 21:21:23:597 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： dec1b32d> ]
[2024-10-02 21:21:33:496 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ac6bc0ba> ]
[2024-10-02 21:22:46:083 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3650MB usedMemory:446MB
[2024-10-02 21:23:13:368 ERROR](CommandProcessor.java:174)处理消息[2824,command:game.server.logic.friend.handler.SendEnergyHandler]耗时:40
[2024-10-02 21:23:36:861 ERROR](CommandProcessor.java:174)处理消息[2817,command:game.server.logic.friend.handler.InviteFriendHandler]耗时:19
[2024-10-02 21:27:46:083 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3541MB usedMemory:555MB
[2024-10-02 21:30:00:189 INFO](WorldBossService.java:317)---resetTime WorldBoss----
[2024-10-02 21:32:46:083 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3506MB usedMemory:590MB
[2024-10-02 21:37:46:084 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3473MB usedMemory:623MB
[2024-10-02 21:42:46:084 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3841MB usedMemory:255MB
[2024-10-02 21:45:01:961 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 15e41e18> ]
[2024-10-02 21:45:02:003 INFO](SessionUtils.java:19)sessionId:15e41e18-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:15e41e18, reason=5,closing:false
[2024-10-02 21:45:02:166 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 15e41e18> ]
[2024-10-02 21:45:02:166 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============15e41e18> ]
[2024-10-02 21:45:02:178 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@bb36474, sessionId:15e41e18
[2024-10-02 21:45:08:138 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 3b73179f> ]
[2024-10-02 21:45:08:157 INFO](AccountDao.java:25)新建用户：***********
[2024-10-02 21:45:31:318 ERROR](CommandProcessor.java:174)处理消息[2050,command:game.server.logic.fight.handler.ReqSectionFightCheckHandler]耗时:120
[2024-10-02 21:46:12:105 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 3b73179f> ]
[2024-10-02 21:46:12:105 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============3b73179f> ]
[2024-10-02 21:46:21:688 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 2
[2024-10-02 21:47:46:085 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3698MB usedMemory:398MB
[2024-10-02 21:50:00:369 INFO](ArenaProcessorManager.java:639)竞技场维护完毕
[2024-10-02 21:50:00:374 INFO](ArenaRewardHandler.java:48)开始发放竞技场排行奖励
[2024-10-02 21:50:00:374 INFO](ArenaRewardHandler.java:93)竞技场发放第[1]名,playerId[536883031]奖励
[2024-10-02 21:50:00:391 INFO](ArenaRewardHandler.java:93)竞技场发放第[2]名,playerId[536881115]奖励
[2024-10-02 21:50:00:416 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536881115
[2024-10-02 21:50:00:438 INFO](ArenaRewardHandler.java:93)竞技场发放第[3]名,playerId[536883041]奖励
[2024-10-02 21:50:00:474 INFO](ArenaRewardHandler.java:93)竞技场发放第[4]名,playerId[536883203]奖励
[2024-10-02 21:50:00:474 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536883041
[2024-10-02 21:50:00:494 INFO](ArenaRewardHandler.java:93)竞技场发放第[5]名,playerId[536881118]奖励
[2024-10-02 21:50:00:515 INFO](ArenaRewardHandler.java:93)竞技场发放第[6]名,playerId[536886069]奖励
[2024-10-02 21:50:00:533 INFO](ArenaRewardHandler.java:93)竞技场发放第[7]名,playerId[536885275]奖励
[2024-10-02 21:50:00:539 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886069
[2024-10-02 21:50:00:550 INFO](ArenaRewardHandler.java:93)竞技场发放第[12]名,playerId[536883030]奖励
[2024-10-02 21:50:00:551 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536885275
[2024-10-02 21:50:00:575 INFO](ArenaRewardHandler.java:93)竞技场发放第[13]名,playerId[536881151]奖励
[2024-10-02 21:50:00:602 INFO](ArenaRewardHandler.java:93)竞技场发放第[14]名,playerId[536882509]奖励
[2024-10-02 21:50:00:629 INFO](ArenaRewardHandler.java:93)竞技场发放第[20]名,playerId[536882657]奖励
[2024-10-02 21:50:00:647 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536882509
[2024-10-02 21:50:00:653 INFO](ArenaRewardHandler.java:93)竞技场发放第[22]名,playerId[536881525]奖励
[2024-10-02 21:50:00:667 INFO](ArenaRewardHandler.java:93)竞技场发放第[65]名,playerId[536881260]奖励
[2024-10-02 21:50:00:681 INFO](ArenaRewardHandler.java:93)竞技场发放第[77]名,playerId[536883101]奖励
[2024-10-02 21:50:00:736 INFO](ArenaRewardHandler.java:93)竞技场发放第[446]名,playerId[536885227]奖励
[2024-10-02 21:50:00:750 INFO](ArenaRewardHandler.java:93)竞技场发放第[809]名,playerId[536881128]奖励
[2024-10-02 21:50:00:762 INFO](ArenaRewardHandler.java:93)竞技场发放第[1234]名,playerId[536885487]奖励
[2024-10-02 21:50:00:767 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536881115
[2024-10-02 21:50:00:776 INFO](ArenaRewardHandler.java:93)竞技场发放第[2055]名,playerId[536887223]奖励
[2024-10-02 21:50:00:798 INFO](ArenaRewardHandler.java:93)竞技场发放第[2162]名,playerId[536882225]奖励
[2024-10-02 21:50:00:810 INFO](ArenaRewardHandler.java:93)竞技场发放第[2942]名,playerId[536882760]奖励
[2024-10-02 21:50:00:823 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536883041
[2024-10-02 21:50:00:828 INFO](ArenaRewardHandler.java:93)竞技场发放第[3679]名,playerId[536881129]奖励
[2024-10-02 21:50:00:840 INFO](ArenaRewardHandler.java:93)竞技场发放第[7869]名,playerId[536883341]奖励
[2024-10-02 21:50:00:841 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886164
[2024-10-02 21:50:00:861 INFO](ArenaRewardHandler.java:93)竞技场发放第[7928]名,playerId[536883171]奖励
[2024-10-02 21:50:00:874 INFO](ArenaRewardHandler.java:93)竞技场发放第[10001]名,playerId[536881121]奖励
[2024-10-02 21:50:00:885 INFO](ArenaRewardHandler.java:93)竞技场发放第[10002]名,playerId[536881127]奖励
[2024-10-02 21:50:00:898 INFO](ArenaRewardHandler.java:93)竞技场发放第[10005]名,playerId[536881138]奖励
[2024-10-02 21:50:00:911 INFO](ArenaRewardHandler.java:93)竞技场发放第[10006]名,playerId[536881136]奖励
[2024-10-02 21:50:00:926 INFO](ArenaRewardHandler.java:93)竞技场发放第[10009]名,playerId[536881116]奖励
[2024-10-02 21:50:00:943 INFO](ArenaRewardHandler.java:93)竞技场发放第[10010]名,playerId[536881119]奖励
[2024-10-02 21:50:00:946 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536881608
[2024-10-02 21:50:00:961 INFO](ArenaRewardHandler.java:93)竞技场发放第[10011]名,playerId[536881374]奖励
[2024-10-02 21:50:00:971 INFO](ArenaRewardHandler.java:93)竞技场发放第[10012]名,playerId[536881375]奖励
[2024-10-02 21:50:00:980 INFO](ArenaRewardHandler.java:93)竞技场发放第[10013]名,playerId[536881493]奖励
[2024-10-02 21:50:01:004 INFO](ArenaRewardHandler.java:93)竞技场发放第[10014]名,playerId[536881519]奖励
[2024-10-02 21:50:01:016 INFO](ArenaRewardHandler.java:93)竞技场发放第[10015]名,playerId[536881543]奖励
[2024-10-02 21:50:01:023 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536881128
[2024-10-02 21:50:01:034 INFO](ArenaRewardHandler.java:93)竞技场发放第[10017]名,playerId[536882127]奖励
[2024-10-02 21:50:01:046 INFO](ArenaRewardHandler.java:93)竞技场发放第[10018]名,playerId[536881689]奖励
[2024-10-02 21:50:01:052 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536882509
[2024-10-02 21:50:01:058 INFO](ArenaRewardHandler.java:93)竞技场发放第[10019]名,playerId[536881608]奖励
[2024-10-02 21:50:01:065 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536881113
[2024-10-02 21:50:01:067 INFO](ArenaRewardHandler.java:93)竞技场发放第[10020]名,playerId[536882226]奖励
[2024-10-02 21:50:01:078 INFO](ArenaRewardHandler.java:93)竞技场发放第[10022]名,playerId[536882261]奖励
[2024-10-02 21:50:01:096 INFO](ArenaRewardHandler.java:93)竞技场发放第[10024]名,playerId[536882306]奖励
[2024-10-02 21:50:01:107 INFO](ArenaRewardHandler.java:93)竞技场发放第[10025]名,playerId[536882434]奖励
[2024-10-02 21:50:01:114 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536883293
[2024-10-02 21:50:01:117 INFO](ArenaRewardHandler.java:93)竞技场发放第[10027]名,playerId[536882639]奖励
[2024-10-02 21:50:01:130 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536884850
[2024-10-02 21:50:01:135 INFO](ArenaRewardHandler.java:93)竞技场发放第[10028]名,playerId[536881113]奖励
[2024-10-02 21:50:01:146 INFO](ArenaRewardHandler.java:93)竞技场发放第[10030]名,playerId[536881117]奖励
[2024-10-02 21:50:01:154 INFO](ArenaRewardHandler.java:93)竞技场发放第[10031]名,playerId[536882794]奖励
[2024-10-02 21:50:01:165 INFO](ArenaRewardHandler.java:93)竞技场发放第[10032]名,playerId[536882924]奖励
[2024-10-02 21:50:01:187 INFO](ArenaRewardHandler.java:93)竞技场发放第[10039]名,playerId[536883293]奖励
[2024-10-02 21:50:01:203 INFO](ArenaRewardHandler.java:93)竞技场发放第[10041]名,playerId[536883669]奖励
[2024-10-02 21:50:01:213 INFO](ArenaRewardHandler.java:93)竞技场发放第[10042]名,playerId[536884443]奖励
[2024-10-02 21:50:01:227 INFO](ArenaRewardHandler.java:93)竞技场发放第[10043]名,playerId[536882756]奖励
[2024-10-02 21:50:01:259 INFO](ArenaRewardHandler.java:93)竞技场发放第[10044]名,playerId[536884850]奖励
[2024-10-02 21:50:01:267 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536885707
[2024-10-02 21:50:01:270 INFO](ArenaRewardHandler.java:93)竞技场发放第[10045]名,playerId[536882604]奖励
[2024-10-02 21:50:01:280 INFO](ArenaRewardHandler.java:93)竞技场发放第[10047]名,playerId[536885011]奖励
[2024-10-02 21:50:01:281 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536885275
[2024-10-02 21:50:01:291 INFO](ArenaRewardHandler.java:93)竞技场发放第[10049]名,playerId[536885235]奖励
[2024-10-02 21:50:01:299 INFO](ArenaRewardHandler.java:93)竞技场发放第[10051]名,playerId[536885368]奖励
[2024-10-02 21:50:01:299 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536885797
[2024-10-02 21:50:01:307 INFO](ArenaRewardHandler.java:93)竞技场发放第[10052]名,playerId[536885471]奖励
[2024-10-02 21:50:01:307 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886104
[2024-10-02 21:50:01:317 INFO](ArenaRewardHandler.java:93)竞技场发放第[10054]名,playerId[536885540]奖励
[2024-10-02 21:50:01:323 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886084
[2024-10-02 21:50:01:328 INFO](ArenaRewardHandler.java:93)竞技场发放第[10055]名,playerId[536885564]奖励
[2024-10-02 21:50:01:336 INFO](ArenaRewardHandler.java:93)竞技场发放第[10056]名,playerId[536885609]奖励
[2024-10-02 21:50:01:344 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886069
[2024-10-02 21:50:01:346 INFO](ArenaRewardHandler.java:93)竞技场发放第[10057]名,playerId[536885669]奖励
[2024-10-02 21:50:01:356 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886183
[2024-10-02 21:50:01:368 INFO](ArenaRewardHandler.java:93)竞技场发放第[10058]名,playerId[536885707]奖励
[2024-10-02 21:50:01:371 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886374
[2024-10-02 21:50:01:388 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536886445
[2024-10-02 21:50:01:388 INFO](ArenaRewardHandler.java:93)竞技场发放第[10059]名,playerId[536885797]奖励
[2024-10-02 21:50:01:397 INFO](ArenaRewardHandler.java:93)竞技场发放第[10060]名,playerId[536886041]奖励
[2024-10-02 21:50:01:398 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536882639
[2024-10-02 21:50:01:406 INFO](ArenaRewardHandler.java:93)竞技场发放第[10061]名,playerId[536886104]奖励
[2024-10-02 21:50:01:411 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536882127
[2024-10-02 21:50:01:423 INFO](ArenaRewardHandler.java:93)竞技场发放第[10062]名,playerId[536886084]奖励
[2024-10-02 21:50:01:423 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=536887223
[2024-10-02 21:50:01:436 INFO](MailService.java:400)邮件发送=======标题：跨服竞技场每日结算奖励====playerId=*********
[2024-10-02 21:50:01:436 INFO](ArenaRewardHandler.java:93)竞技场发放第[10064]名,playerId[536886183]奖励
[2024-10-02 21:50:01:451 INFO](ArenaRewardHandler.java:93)竞技场发放第[10065]名,playerId[536886374]奖励
[2024-10-02 21:50:01:465 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536881128
[2024-10-02 21:50:01:466 INFO](ArenaRewardHandler.java:93)竞技场发放第[10066]名,playerId[536886445]奖励
[2024-10-02 21:50:01:476 INFO](ArenaRewardHandler.java:93)竞技场发放第[10067]名,playerId[536886790]奖励
[2024-10-02 21:50:01:498 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536887223
[2024-10-02 21:50:01:520 INFO](ArenaRewardHandler.java:93)竞技场发放第[10068]名,playerId[536886486]奖励
[2024-10-02 21:50:01:520 INFO](ArenaRewardHandler.java:93)竞技场发放第[10069]名,playerId[536886832]奖励
[2024-10-02 21:50:01:552 INFO](ArenaRewardHandler.java:93)竞技场发放第[10070]名,playerId[536887111]奖励
[2024-10-02 21:50:01:569 INFO](ArenaRewardHandler.java:93)竞技场发放第[10071]名,playerId[536887170]奖励
[2024-10-02 21:50:01:582 INFO](ArenaRewardHandler.java:93)竞技场发放第[10073]名,playerId[*********]奖励
[2024-10-02 21:50:01:596 INFO](ArenaRewardHandler.java:93)竞技场发放第[10074]名,playerId[536886164]奖励
[2024-10-02 21:50:01:610 INFO](ArenaRewardHandler.java:93)竞技场发放第[10075]名,playerId[536887651]奖励
[2024-10-02 21:50:01:610 INFO](ArenaRewardHandler.java:114)竞技场排行奖励发放完毕
[2024-10-02 21:50:01:777 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536882127
[2024-10-02 21:50:01:796 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536881608
[2024-10-02 21:50:01:867 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536882639
[2024-10-02 21:50:01:881 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536881113
[2024-10-02 21:50:01:915 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536883293
[2024-10-02 21:50:01:948 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536884850
[2024-10-02 21:50:02:013 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536885707
[2024-10-02 21:50:02:041 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536885797
[2024-10-02 21:50:02:047 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886041
[2024-10-02 21:50:02:052 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886104
[2024-10-02 21:50:02:063 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886084
[2024-10-02 21:50:02:071 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886183
[2024-10-02 21:50:02:081 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886374
[2024-10-02 21:50:02:091 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886445
[2024-10-02 21:50:02:096 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886790
[2024-10-02 21:50:02:105 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886486
[2024-10-02 21:50:02:111 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536887111
[2024-10-02 21:50:02:117 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536887170
[2024-10-02 21:50:02:126 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=*********
[2024-10-02 21:50:02:135 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536886164
[2024-10-02 21:50:02:140 INFO](MailService.java:400)邮件发送=======标题：竞技场每日结算奖励====playerId=536887651
[2024-10-02 21:50:21:689 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 3
[2024-10-02 21:50:41:785 ERROR](CommandProcessor.java:174)处理消息[6405,command:game.server.logic.vsTower.handler.ReqRefreshFightTargetHandler]耗时:50
[2024-10-02 21:51:08:414 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:52
[2024-10-02 21:51:32:651 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:54
[2024-10-02 21:52:01:678 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:78
[2024-10-02 21:52:18:820 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:69
[2024-10-02 21:52:21:687 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 21:52:36:219 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:72
[2024-10-02 21:52:46:085 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3874MB usedMemory:222MB
[2024-10-02 21:53:06:014 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:63
[2024-10-02 21:53:24:288 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:66
[2024-10-02 21:53:42:361 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:63
[2024-10-02 21:53:58:981 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:58
[2024-10-02 21:54:16:555 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:71
[2024-10-02 21:54:21:687 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 21:54:27:922 ERROR](CommandProcessor.java:174)处理消息[6405,command:game.server.logic.vsTower.handler.ReqRefreshFightTargetHandler]耗时:63
[2024-10-02 21:54:50:735 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： dec1b32d> ]
[2024-10-02 21:54:50:736 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============dec1b32d> ]
[2024-10-02 21:55:05:499 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： b170cbca> ]
[2024-10-02 21:55:27:074 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:66
[2024-10-02 21:55:47:319 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:66
[2024-10-02 21:56:21:687 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 21:57:00:205 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： b170cbca> ]
[2024-10-02 21:57:00:205 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b170cbca> ]
[2024-10-02 21:57:00:205 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====b170cbca> ]
[2024-10-02 21:57:00:382 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============b170cbca> ]
[2024-10-02 21:57:09:906 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： bf4c8973> ]
[2024-10-02 21:57:46:085 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3571MB usedMemory:525MB
[2024-10-02 21:57:47:087 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:61
[2024-10-02 21:58:25:556 ERROR](CommandProcessor.java:174)处理消息[6404,command:game.server.logic.vsTower.handler.ReqVsTowerFightCheckHandler]耗时:76
[2024-10-02 21:59:50:376 ERROR](CommandProcessor.java:174)处理消息[10495,command:game.server.logic.crossIndigo.handler.InnerCrossIndigoResponseHandler]耗时:11
[2024-10-02 22:00:00:472 INFO](SnatchTerritoryService.java:801)道馆占领次数，发放每日排名奖励-------------------
[2024-10-02 22:00:00:473 INFO](PirateBossService.java:619)---resetTime PirateBoss----
[2024-10-02 22:00:00:473 INFO](SnatchTerritoryService.java:811)道馆占领次数，发放每日排名奖励完毕！-------------------
[2024-10-02 22:00:00:494 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536883041
[2024-10-02 22:00:00:588 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536881113
[2024-10-02 22:00:00:607 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536882509
[2024-10-02 22:00:00:708 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536885275
[2024-10-02 22:00:00:747 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536885797
[2024-10-02 22:00:00:758 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536886084
[2024-10-02 22:00:00:766 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536886183
[2024-10-02 22:00:00:780 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536886069
[2024-10-02 22:00:00:790 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536886374
[2024-10-02 22:00:00:799 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536886486
[2024-10-02 22:00:00:815 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536881115
[2024-10-02 22:00:00:834 INFO](MailService.java:400)邮件发送=======标题：公会每日占领排名奖励====playerId=536887223
[2024-10-02 22:00:21:687 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 22:00:58:268 ERROR](CommandProcessor.java:174)处理消息[2305,command:game.server.logic.arena.handler.GetArenaHandler]耗时:102
[2024-10-02 22:01:59:441 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： bf4c8973> ]
[2024-10-02 22:01:59:441 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============bf4c8973> ]
[2024-10-02 22:02:46:086 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3539MB usedMemory:557MB
[2024-10-02 22:05:54:489 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： cd80f846> ]
[2024-10-02 22:05:54:529 INFO](SessionUtils.java:19)sessionId:cd80f846-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:cd80f846, reason=5,closing:false
[2024-10-02 22:05:54:596 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： cd80f846> ]
[2024-10-02 22:05:54:596 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============cd80f846> ]
[2024-10-02 22:05:54:607 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@42a4bcb4, sessionId:cd80f846
[2024-10-02 22:05:57:382 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： d0525288> ]
[2024-10-02 22:05:57:402 INFO](AccountDao.java:25)新建用户：Yx7677632
[2024-10-02 22:06:16:672 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： d0525288> ]
[2024-10-02 22:06:16:672 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d0525288> ]
[2024-10-02 22:06:21:688 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 2
[2024-10-02 22:07:46:086 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3396MB usedMemory:700MB
[2024-10-02 22:12:46:087 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3770MB usedMemory:326MB
[2024-10-02 22:17:46:087 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3752MB usedMemory:344MB
[2024-10-02 22:21:05:635 ERROR](PrintLogs.java:26)[日志 : game.core.netty.net.codec.server.CustomCiphertextDecoder massage : <channel [id: 0xcc5d474d, /*************:63465 => /**********:9001] head flag decode error! channel close >]
[2024-10-02 22:21:05:994 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============cc5d474d> ]
[2024-10-02 22:22:46:087 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3733MB usedMemory:363MB
[2024-10-02 22:27:46:088 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3715MB usedMemory:381MB
[2024-10-02 22:32:46:090 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3697MB usedMemory:399MB
[2024-10-02 22:37:46:092 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3677MB usedMemory:419MB
[2024-10-02 22:43:46:091 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3533MB usedMemory:563MB
[2024-10-02 22:48:46:091 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3514MB usedMemory:582MB
[2024-10-02 22:53:46:092 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3496MB usedMemory:600MB
[2024-10-02 22:58:46:092 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3477MB usedMemory:619MB
[2024-10-02 23:03:46:092 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3457MB usedMemory:639MB
[2024-10-02 23:08:46:093 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3820MB usedMemory:276MB
[2024-10-02 23:10:49:574 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： ee52fd31> ]
[2024-10-02 23:10:57:784 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[409],开始丢给RechargeProcessor处理
[2024-10-02 23:10:57:784 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:10:57:785 ERROR](RechargeFinishScript.java:81)收到充值商品:409 订单信息:[ 支付类型:1 , 订单号: 536887683, 充值金额: 1000.0充值个数:1 ]
[2024-10-02 23:10:57:802 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:409 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:10:57:803 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:19
[2024-10-02 23:11:14:338 ERROR](CommandProcessor.java:174)处理消息[529,command:game.server.logic.player.handler.GetRechargeActivityRewardHandler]耗时:24
[2024-10-02 23:11:34:714 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[6],开始丢给RechargeProcessor处理
[2024-10-02 23:11:34:714 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:11:34:715 ERROR](RechargeFinishScript.java:81)收到充值商品:6 订单信息:[ 支付类型:1 , 订单号: 536887684, 充值金额: 648.0充值个数:1 ]
[2024-10-02 23:11:34:721 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:6 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:11:37:494 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[6],开始丢给RechargeProcessor处理
[2024-10-02 23:11:37:494 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:11:37:494 ERROR](RechargeFinishScript.java:81)收到充值商品:6 订单信息:[ 支付类型:1 , 订单号: 536887685, 充值金额: 648.0充值个数:1 ]
[2024-10-02 23:11:37:505 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:6 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:11:37:506 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 23:11:56:352 ERROR](CommandProcessor.java:174)处理消息[529,command:game.server.logic.player.handler.GetRechargeActivityRewardHandler]耗时:18
[2024-10-02 23:12:00:930 ERROR](CommandProcessor.java:174)处理消息[529,command:game.server.logic.player.handler.GetRechargeActivityRewardHandler]耗时:23
[2024-10-02 23:12:15:849 ERROR](CommandProcessor.java:174)处理消息[529,command:game.server.logic.player.handler.GetRechargeActivityRewardHandler]耗时:25
[2024-10-02 23:12:21:701 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 85 , 耗时： 12
[2024-10-02 23:12:50:006 ERROR](CommandProcessor.java:174)处理消息[535,command:game.server.logic.player.handler.GetPrivilageRewardHandler]耗时:12
[2024-10-02 23:13:46:093 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3649MB usedMemory:447MB
[2024-10-02 23:14:21:691 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 17 , 耗时： 2
[2024-10-02 23:16:21:690 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 2 , 耗时： 1
[2024-10-02 23:16:49:764 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1101],开始丢给RechargeProcessor处理
[2024-10-02 23:16:49:764 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:16:49:764 ERROR](RechargeFinishScript.java:81)收到充值商品:1101 订单信息:[ 支付类型:1 , 订单号: 536887686, 充值金额: 368.0充值个数:1 ]
[2024-10-02 23:16:49:772 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1101 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:16:53:199 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1101],开始丢给RechargeProcessor处理
[2024-10-02 23:16:53:199 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:16:53:200 ERROR](RechargeFinishScript.java:81)收到充值商品:1101 订单信息:[ 支付类型:1 , 订单号: 536887687, 充值金额: 368.0充值个数:1 ]
[2024-10-02 23:16:53:207 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1101 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:16:57:104 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1101],开始丢给RechargeProcessor处理
[2024-10-02 23:16:57:104 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:16:57:105 ERROR](RechargeFinishScript.java:81)收到充值商品:1101 订单信息:[ 支付类型:1 , 订单号: 536887688, 充值金额: 368.0充值个数:1 ]
[2024-10-02 23:16:57:112 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1101 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:02:879 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1102],开始丢给RechargeProcessor处理
[2024-10-02 23:17:02:879 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:02:880 ERROR](RechargeFinishScript.java:81)收到充值商品:1102 订单信息:[ 支付类型:1 , 订单号: 536887689, 充值金额: 1688.0充值个数:1 ]
[2024-10-02 23:17:02:886 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1102 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:06:509 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1102],开始丢给RechargeProcessor处理
[2024-10-02 23:17:06:509 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:06:510 ERROR](RechargeFinishScript.java:81)收到充值商品:1102 订单信息:[ 支付类型:1 , 订单号: 536887690, 充值金额: 1688.0充值个数:1 ]
[2024-10-02 23:17:06:530 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1102 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:06:531 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:22
[2024-10-02 23:17:10:589 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1102],开始丢给RechargeProcessor处理
[2024-10-02 23:17:10:589 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:10:590 ERROR](RechargeFinishScript.java:81)收到充值商品:1102 订单信息:[ 支付类型:1 , 订单号: 536887691, 充值金额: 1688.0充值个数:1 ]
[2024-10-02 23:17:10:600 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1102 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:10:600 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:11
[2024-10-02 23:17:17:789 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1103],开始丢给RechargeProcessor处理
[2024-10-02 23:17:17:789 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:17:790 ERROR](RechargeFinishScript.java:81)收到充值商品:1103 订单信息:[ 支付类型:1 , 订单号: 536887692, 充值金额: 5688.0充值个数:1 ]
[2024-10-02 23:17:17:798 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1103 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:30:226 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1103],开始丢给RechargeProcessor处理
[2024-10-02 23:17:30:226 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:30:227 ERROR](RechargeFinishScript.java:81)收到充值商品:1103 订单信息:[ 支付类型:1 , 订单号: 536887693, 充值金额: 5688.0充值个数:1 ]
[2024-10-02 23:17:30:239 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1103 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:30:240 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:14
[2024-10-02 23:17:34:794 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1103],开始丢给RechargeProcessor处理
[2024-10-02 23:17:34:794 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:34:794 ERROR](RechargeFinishScript.java:81)收到充值商品:1103 订单信息:[ 支付类型:1 , 订单号: 536887694, 充值金额: 5688.0充值个数:1 ]
[2024-10-02 23:17:34:801 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1103 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:39:569 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1104],开始丢给RechargeProcessor处理
[2024-10-02 23:17:39:569 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:39:569 ERROR](RechargeFinishScript.java:81)收到充值商品:1104 订单信息:[ 支付类型:1 , 订单号: 536887695, 充值金额: 16688.0充值个数:1 ]
[2024-10-02 23:17:39:576 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1104 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:45:379 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1104],开始丢给RechargeProcessor处理
[2024-10-02 23:17:45:379 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:45:379 ERROR](RechargeFinishScript.java:81)收到充值商品:1104 订单信息:[ 支付类型:1 , 订单号: 536887696, 充值金额: 16688.0充值个数:1 ]
[2024-10-02 23:17:45:386 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1104 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:49:674 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1104],开始丢给RechargeProcessor处理
[2024-10-02 23:17:49:674 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:49:674 ERROR](RechargeFinishScript.java:81)收到充值商品:1104 订单信息:[ 支付类型:1 , 订单号: 536887697, 充值金额: 16688.0充值个数:1 ]
[2024-10-02 23:17:49:681 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1104 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:56:999 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1105],开始丢给RechargeProcessor处理
[2024-10-02 23:17:56:999 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:17:57:000 ERROR](RechargeFinishScript.java:81)收到充值商品:1105 订单信息:[ 支付类型:1 , 订单号: 536887698, 充值金额: 29688.0充值个数:1 ]
[2024-10-02 23:17:57:011 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1105 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:17:57:011 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 23:18:02:304 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1105],开始丢给RechargeProcessor处理
[2024-10-02 23:18:02:304 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:18:02:304 ERROR](RechargeFinishScript.java:81)收到充值商品:1105 订单信息:[ 支付类型:1 , 订单号: 536887699, 充值金额: 29688.0充值个数:1 ]
[2024-10-02 23:18:02:334 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1105 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:18:02:334 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:30
[2024-10-02 23:18:07:029 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1105],开始丢给RechargeProcessor处理
[2024-10-02 23:18:07:029 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:18:07:030 ERROR](RechargeFinishScript.java:81)收到充值商品:1105 订单信息:[ 支付类型:1 , 订单号: 536887700, 充值金额: 29688.0充值个数:1 ]
[2024-10-02 23:18:07:042 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1105 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:18:07:042 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:13
[2024-10-02 23:18:11:974 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1106],开始丢给RechargeProcessor处理
[2024-10-02 23:18:11:975 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:18:11:975 ERROR](RechargeFinishScript.java:81)收到充值商品:1106 订单信息:[ 支付类型:1 , 订单号: 536887701, 充值金额: 49688.0充值个数:1 ]
[2024-10-02 23:18:11:982 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1106 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:18:17:661 ERROR](CommandProcessor.java:174)处理消息[7426,command:game.server.logic.recharge.handler.ReqRechargeForTestHandler]耗时:21
[2024-10-02 23:18:21:701 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 80 , 耗时： 12
[2024-10-02 23:18:46:094 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3546MB usedMemory:550MB
[2024-10-02 23:18:47:641 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： ee52fd31> ]
[2024-10-02 23:18:47:641 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ee52fd31> ]
[2024-10-02 23:18:47:641 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====ee52fd31> ]
[2024-10-02 23:18:47:736 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============ee52fd31> ]
[2024-10-02 23:18:57:267 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887170","productid":49,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 23:18:57:267 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[49],开始丢给RechargeProcessor处理
[2024-10-02 23:18:57:286 ERROR](RechargeFinishScript.java:81)收到充值商品:49 订单信息:[ 支付类型:3 , 订单号: 536887702, 充值金额: 64800.0充值个数:1 ]
[2024-10-02 23:18:57:287 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:49 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:18:57:287 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887702 ， 玩家 : 536887170 ]
[2024-10-02 23:18:57:287 ERROR](RechargeService.java:442)订单[536887702] 脚本支付流程！
[2024-10-02 23:18:57:298 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 23:18:59:809 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 4ae33864> ]
[2024-10-02 23:19:27:730 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 4ae33864> ]
[2024-10-02 23:19:27:744 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============4ae33864> ]
[2024-10-02 23:19:31:589 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 892a0633> ]
[2024-10-02 23:19:42:324 ERROR](CommandProcessor.java:174)处理消息[529,command:game.server.logic.player.handler.GetRechargeActivityRewardHandler]耗时:20
[2024-10-02 23:20:06:059 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[8],开始丢给RechargeProcessor处理
[2024-10-02 23:20:06:059 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:20:06:060 ERROR](RechargeFinishScript.java:81)收到充值商品:8 订单信息:[ 支付类型:1 , 订单号: 536887703, 充值金额: 2000.0充值个数:1 ]
[2024-10-02 23:20:06:067 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:8 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:20:21:697 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 34 , 耗时： 6
[2024-10-02 23:20:27:579 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[558],开始丢给RechargeProcessor处理
[2024-10-02 23:20:27:579 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:20:27:580 ERROR](RechargeFinishScript.java:81)收到充值商品:558 订单信息:[ 支付类型:1 , 订单号: 536887704, 充值金额: 2000.0充值个数:18 ]
[2024-10-02 23:20:27:594 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:558 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:20:27:594 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:15
[2024-10-02 23:20:48:274 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1106],开始丢给RechargeProcessor处理
[2024-10-02 23:20:48:274 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:20:48:274 ERROR](RechargeFinishScript.java:81)收到充值商品:1106 订单信息:[ 支付类型:1 , 订单号: 536887705, 充值金额: 49688.0充值个数:1 ]
[2024-10-02 23:20:48:282 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1106 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:20:59:820 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887170","productid":49,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 23:20:59:820 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[49],开始丢给RechargeProcessor处理
[2024-10-02 23:20:59:842 ERROR](RechargeFinishScript.java:81)收到充值商品:49 订单信息:[ 支付类型:3 , 订单号: 536887706, 充值金额: 64800.0充值个数:1 ]
[2024-10-02 23:20:59:843 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:49 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:20:59:843 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887706 ， 玩家 : 536887170 ]
[2024-10-02 23:20:59:843 ERROR](RechargeService.java:442)订单[536887706] 脚本支付流程！
[2024-10-02 23:20:59:854 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 23:21:05:684 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1106],开始丢给RechargeProcessor处理
[2024-10-02 23:21:05:684 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:21:05:684 ERROR](RechargeFinishScript.java:81)收到充值商品:1106 订单信息:[ 支付类型:1 , 订单号: 536887707, 充值金额: 49688.0充值个数:1 ]
[2024-10-02 23:21:05:691 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1106 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:21:35:692 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 892a0633> ]
[2024-10-02 23:21:35:692 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============892a0633> ]
[2024-10-02 23:21:35:692 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====892a0633> ]
[2024-10-02 23:21:35:876 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============892a0633> ]
[2024-10-02 23:21:55:070 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887170","productid":558,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 23:21:55:070 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[558],开始丢给RechargeProcessor处理
[2024-10-02 23:21:55:088 ERROR](RechargeFinishScript.java:81)收到充值商品:558 订单信息:[ 支付类型:3 , 订单号: 536887708, 充值金额: 2000.0充值个数:1 ]
[2024-10-02 23:21:55:093 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:558 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:21:55:093 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887708 ， 玩家 : 536887170 ]
[2024-10-02 23:21:55:093 ERROR](RechargeService.java:442)订单[536887708] 脚本支付流程！
[2024-10-02 23:21:55:106 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 23:21:57:156 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 85b0e37f> ]
[2024-10-02 23:22:21:693 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 15 , 耗时： 2
[2024-10-02 23:23:24:036 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887170","productid":1106,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 23:23:24:037 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1106],开始丢给RechargeProcessor处理
[2024-10-02 23:23:24:065 ERROR](RechargeFinishScript.java:81)收到充值商品:1106 订单信息:[ 支付类型:3 , 订单号: 536887709, 充值金额: 49688.0充值个数:1 ]
[2024-10-02 23:23:24:067 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:1106 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:23:24:067 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887709 ， 玩家 : 536887170 ]
[2024-10-02 23:23:24:067 ERROR](RechargeService.java:442)订单[536887709] 脚本支付流程！
[2024-10-02 23:23:24:080 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 23:23:33:915 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887170","productid":1305,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 23:23:33:915 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[1305],开始丢给RechargeProcessor处理
[2024-10-02 23:23:33:934 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887710, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 23:23:33:935 ERROR](RechargeFinishScript.java:269)[ 玩家 536887170 的充值商品:1305 订单号： 536887710 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 23:23:33:935 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887710 ， 玩家 : 536887170 ]
[2024-10-02 23:23:33:935 ERROR](RechargeService.java:442)订单[536887710] 脚本支付流程！
[2024-10-02 23:23:33:947 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 23:23:46:094 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3596MB usedMemory:500MB
[2024-10-02 23:24:21:692 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 1 , 耗时： 1
[2024-10-02 23:25:30:376 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 85b0e37f> ]
[2024-10-02 23:25:30:376 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============85b0e37f> ]
[2024-10-02 23:25:34:328 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 3434c1af> ]
[2024-10-02 23:25:34:547 INFO](RechargeProcessor.java:264)离线充值信息成功给到GameLine: 536887710
[2024-10-02 23:25:34:572 ERROR](RechargeFinishScript.java:81)收到充值商品:1305 订单信息:[ 支付类型:3 , 订单号: 536887710, 充值金额: 1988.0充值个数:1 ]
[2024-10-02 23:25:34:572 ERROR](RechargeFinishScript.java:269)[ 玩家 536887170 的充值商品:1305 订单号： 536887710 ] 充值失败！ 已将放入离线充值列表!
[2024-10-02 23:26:21:693 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 4 , 耗时： 2
[2024-10-02 23:26:22:016 ERROR](CommandProcessor.java:174)处理消息[1025,command:game.server.logic.backpack.handler.ReqUseItemHandler]耗时:15
[2024-10-02 23:27:35:020 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[557],开始丢给RechargeProcessor处理
[2024-10-02 23:27:35:020 INFO](RechargeService.java:329)玩家[roleId:536887170]代币充值    订单生成  成功
[2024-10-02 23:27:35:021 ERROR](RechargeFinishScript.java:81)收到充值商品:557 订单信息:[ 支付类型:1 , 订单号: 536887711, 充值金额: 1000.0充值个数:15 ]
[2024-10-02 23:27:35:032 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:557 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:27:35:032 ERROR](CommandProcessor.java:174)处理消息[7433,command:game.server.logic.recharge.handler.ReqRechargeForDBHandler]耗时:12
[2024-10-02 23:28:21:698 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 41 , 耗时： 6
[2024-10-02 23:28:36:628 ERROR](CommandProcessor.java:174)处理消息[532,command:game.server.logic.player.handler.GetOnlineRewardHandler]耗时:15
[2024-10-02 23:28:42:385 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 270695da> ]
[2024-10-02 23:28:42:429 INFO](SessionUtils.java:19)sessionId:270695da-->close [because] --->[Login] loginFailed, 登陆验证未通过!, sessionId:270695da, reason=5,closing:false
[2024-10-02 23:28:42:537 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 270695da> ]
[2024-10-02 23:28:42:537 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============270695da> ]
[2024-10-02 23:28:42:546 ERROR](LoginService.java:661)--->[Login] connectionClosed, player is null:game.core.pub.session.Session@6419c8ff, sessionId:270695da
[2024-10-02 23:28:46:094 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3406MB usedMemory:690MB
[2024-10-02 23:28:50:463 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： 52b58256> ]
[2024-10-02 23:28:50:480 INFO](AccountDao.java:25)新建用户：2005801
[2024-10-02 23:29:10:726 ERROR](CommandProcessor.java:174)处理消息[1795,command:game.server.logic.drawcard.handler.TenDiamondDrawCardHandler]耗时:58
[2024-10-02 23:30:02:166 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 52b58256> ]
[2024-10-02 23:30:02:166 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============52b58256> ]
[2024-10-02 23:30:02:166 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====52b58256> ]
[2024-10-02 23:30:02:527 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============52b58256> ]
[2024-10-02 23:30:03:103 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Create session  ： d8c76e53> ]
[2024-10-02 23:30:21:696 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 17 , 耗时： 4
[2024-10-02 23:30:36:946 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： d8c76e53> ]
[2024-10-02 23:30:36:946 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d8c76e53> ]
[2024-10-02 23:30:36:946 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <userEventTriggered ====== channel读写超时 =====d8c76e53> ]
[2024-10-02 23:30:37:135 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============d8c76e53> ]
[2024-10-02 23:30:39:404 ERROR](GameHttpNettyNewServerImpl.java:134)json str : {"action":"recharge","playerid":"536887170","productid":905,"token":"IwY#UGJqJWxB8xZs"}
[2024-10-02 23:30:39:404 INFO](RechargeService.java:509)玩家[roleId:536887170]请求充值,payId[905],开始丢给RechargeProcessor处理
[2024-10-02 23:30:39:421 ERROR](RechargeFinishScript.java:81)收到充值商品:905 订单信息:[ 支付类型:3 , 订单号: 536887713, 充值金额: 18888.0充值个数:1 ]
[2024-10-02 23:30:39:422 ERROR](RechargeFinishScript.java:250)[ 玩家：536887170的充值商品:905 ] 充值完成，订单状态修改为 4！
[2024-10-02 23:30:39:423 ERROR](UpdateRechargeBeanScript.java:89) [ 充值成功信息，继续脚本发放道具流程！     订单号: 536887713 ， 玩家 : 536887170 ]
[2024-10-02 23:30:39:423 ERROR](RechargeService.java:442)订单[536887713] 脚本支付流程！
[2024-10-02 23:30:39:434 INFO](SDKHttpClient.java:744)模拟充值订单上传返回数据：ok
[2024-10-02 23:31:26:590 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <Close session  ： 3434c1af> ]
[2024-10-02 23:31:26:590 ERROR](PrintLogs.java:48)[日志 : game.core.netty.net.communication.CommunicationHandlerAdapter massage : <channel断开 ==============3434c1af> ]
[2024-10-02 23:32:21:692 INFO](ExecuteInsertLogHandler.java:95)回存日志，日志量： 0 , 耗时： 0
[2024-10-02 23:33:46:095 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3876MB usedMemory:220MB
[2024-10-02 23:38:46:095 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3641MB usedMemory:455MB
[2024-10-02 23:43:46:096 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3624MB usedMemory:472MB
[2024-10-02 23:48:46:096 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3606MB usedMemory:490MB
[2024-10-02 23:53:46:096 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3587MB usedMemory:509MB
[2024-10-02 23:58:46:097 INFO](NettyGameServer.java:621)当前在线玩家: [0] totalMemory:4096MB freeMemory:3567MB usedMemory:529MB
