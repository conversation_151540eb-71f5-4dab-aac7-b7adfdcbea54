package logic.login;

import java.util.List;

import data.bean.t_leader_geneBean;
import data.bean.t_leader_moleculeBean;
import game.core.pub.script.IScript;
import game.server.logic.gene.GeneManager;
import game.server.logic.hero.bean.Hero;
import game.server.logic.hero.bean.HeroRelate;
import game.server.logic.hero.bean.HeroSkill;
import game.server.logic.player.Player;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
/**
 * Sửa chữa BUG phần thưởng gen
 * (<PERSON> vấn đề cấu hình, một số tài khoản cũ giai đoạn đầu mở khóa kỹ năng gen chưa được sửa đổi)
 * /Do tải tướng sẽ kiểm tra lại kỹ năng, kỹ năng thay thế gen có độ ưu tiên cao nhất nên đặt cuối cùng
 * <AUTHOR>
 * @date 2018年8月13日
 */
public class GeneRewardBugFixScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs script = (ScriptArgs) arg;
		Player player = (Player) script.get(ScriptArgs.Key.PLAYER);
		GeneManager geneManager = player.getGeneManager();
		Hero leader = player.getHeroManager().getLeader();
		List<t_leader_moleculeBean> genes = BeanTemplet.getLeaderMoleculeBeanList();
		for (t_leader_moleculeBean t_leader_moleculeBean : genes) {
			if (t_leader_moleculeBean.getId() > geneManager.getCurMolecule())
				continue;
			if(t_leader_moleculeBean.getFollow_id() == 0) {//Phân tử cuối cùng, phần thưởng gen
				int curGeneId = t_leader_moleculeBean.getId() / 100;
				// Gửi phần thưởng gen
				t_leader_geneBean geneBean = BeanTemplet.getLeaderGeneBean(curGeneId);
				if(geneBean.getReward_type() == 5) {
					String[] newSkills = geneBean.getReward().split(";");
					for (String skill : newSkills) {
						int skillType = Integer.parseInt(skill.split(",")[0]);
						int skillCid = Integer.parseInt(skill.split(",")[1]);
						HeroSkill heroSkill = null;
						if (skillType == 1) {
							heroSkill = leader.getSkillMap().get(0);
						} else if (skillType == 2) {
							heroSkill = leader.getSkillMap().get(1);
						}
						heroSkill.setId(skillCid);
					}
				}else if(geneBean.getReward_type() == 4) {
					String[] relates = geneBean.getReward().split(",");
					for (String relate : relates) {
						int relateId = Integer.parseInt(relate);
						HeroRelate heroRelate = leader.getRelateById(relateId);
						if (heroRelate == null) {
							continue;
						}
						heroRelate.setUnlock(true);
					}
				}
			}
		}
		return null;
	}

}
