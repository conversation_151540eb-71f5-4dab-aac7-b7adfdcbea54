package indigo;

import Message.Inner.GRCrossIndigo.GRPlayerInfo;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;

/**
 * 向gameServer获取玩家详细信息
 * <AUTHOR>
 *
 */
public class ReqPlayerInfoScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(ScriptArgs.Key.ARG1);
		long playerId = (long) args.get(ScriptArgs.Key.ARG2);
		getPlayerInfo(serverId, playerId);
		return null;
	}
	
	private void getPlayerInfo(int serverId, long playerId) {
		IndigoService indigoService = IndigoService.getInstance();
		GRPlayerInfo.Builder builder = GRPlayerInfo.newBuilder();
		builder.setPlayerId(playerId);
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(0, GRPlayerInfo.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
