package logic.suffle;


import Message.Inner.GRCrossSuffleMsg;
import Message.Inner.InnerServer;
import Message.S2CCrossSuffleMsg;
import game.core.mina.message.SMessage;
import game.core.pub.script.IScript;
import game.server.cross.CrossServer;
import game.server.logic.player.Player;
import game.server.logic.player.PlayerManager;
import game.server.logic.suffle.SuffleService;
import game.server.logic.util.ScriptArgs;
import Message.Inner.GRCrossSuffleMsg.GRSuffleSorce;
import Message.S2CCrossSuffleMsg.SuffleScoreRsp;
import Message.S2CCrossSuffleMsg.SuffleScoreRspID;
import Message.S2CCrossSuffleMsg.PlayerSorceMsg;
import game.server.util.MessageUtils;

public class RefreshSuffleSorceScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRSuffleSorce getData = (GRSuffleSorce) args.get(ScriptArgs.Key.ARG1);

		// 封装协议准备下发数据
		Player player =null;
		for(PlayerSorceMsg playerSorceMsg:getData.getPlayerSorcesList())
		{
			player = PlayerManager.getPlayerByPlayerId(playerSorceMsg.getPlayerId());
			if(player!=null)
			{
				if(player.isOnline()) {
					SuffleScoreRsp.Builder builder = SuffleScoreRsp.newBuilder();
					builder.setPlayerSorce(playerSorceMsg);
					MessageUtils.send(player.getSession(),
							player.getFactory().fetchSMessage(SuffleScoreRspID.SuffleScoreRspMsgID_VALUE, builder.build().toByteArray()));
				}
			}
		}
		return null;
	}

}
