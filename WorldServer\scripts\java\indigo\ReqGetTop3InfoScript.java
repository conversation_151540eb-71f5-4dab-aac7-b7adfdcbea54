package indigo;

import java.util.List;

import org.apache.log4j.Logger;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.S2CPlayerMsg.GetPlayerInfoRspID;
import Message.S2CPlayerMsg.PromptType;
import Message.Inner.GRCrossIndigo.GRGetTop3Req;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;

/**
 * 获取前三的详细信息
 * <AUTHOR>
 *
 */
public class ReqGetTop3InfoScript implements IScript {
	private final Logger logger = Logger.getLogger(ReqGetTop3InfoScript.class);

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(ScriptArgs.Key.ARG1);
		GRGetTop3Req req = (GRGetTop3Req) args.get(ScriptArgs.Key.ARG2);
		IndigoService indigoService = IndigoService.getInstance();
		if (req.getRank() < 1 || req.getRank() > 50) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1250);// 只能请求排名前三的玩家信息
			return null;
		}
		if (null == indigoService.getTop3Catch().get(req.getRank() - 1)) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1250);// 暂无玩家信息
			return null;
		}
		getTop3Info(serverId, req);
		return null;
	}

	/**
	 * 获取前三的详细信息
	 * 
	 * @param player
	 * @return
	 */
	private void getTop3Info(int serverId, GRGetTop3Req req) {
		IndigoService indigoService = IndigoService.getInstance();
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		List<ByteString> list = indigoService.getTop3Catch().get(groupId);
		if (null == list) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1250);// 暂无玩家信息
			return;
		}
		ByteString byteString = list.get(req.getRank() - 1);
		if (null == byteString || byteString.isEmpty()) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1250);// 暂无玩家信息
			return;
		}
		GetPlayerInfoRsp rsp = null;
		try {
			rsp = GetPlayerInfoRsp.parseFrom(byteString);
		} catch (InvalidProtocolBufferException e) {
			logger.error(e);
		}
		if (null == rsp) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1250);// 暂无玩家信息
			return;
		}
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(req.getPlayerId(), GetPlayerInfoRspID.GetPlayerInfoRspMsgID_VALUE, rsp.toByteString()));
	}
}
