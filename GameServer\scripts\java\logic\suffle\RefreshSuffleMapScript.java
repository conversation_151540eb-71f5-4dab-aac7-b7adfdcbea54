package logic.suffle;

import Message.S2CArenaMsg.EnemyMsg;
import Message.S2CCrossArenaMsg.CrossEnemyMsg;
import Message.S2CCrossArenaMsg;

import Message.S2CCrossSuffleMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import game.core.pub.script.IScript;
import game.server.logic.player.Player;
import game.server.logic.suffle.SuffleService;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossSuffleMsg.GRSuffleMap;
import Message.S2CCrossSuffleMsg.SuffleMapRsp;

public class RefreshSuffleMapScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRSuffleMap getData = (GRSuffleMap) args.get(ScriptArgs.Key.ARG1);

		// 封装协议准备下发数据
		SuffleMapRsp.Builder builder = SuffleMapRsp.newBuilder();
		builder.addAllMapPlayer(getData.getPlayersList());
		SuffleService.getInstance().SendMapPlayers(S2CCrossSuffleMsg.SuffleMapRspID.SuffleMapRspMsgID_VALUE,builder.build().toByteArray());
		return null;
	}

}
