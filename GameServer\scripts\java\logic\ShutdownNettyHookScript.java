package logic;

import game.server.logic.crossArena.CrossArenaService;
import game.server.logic.rank.RankProcessor;
import game.server.logic.suffle.SuffleService;
import org.apache.log4j.Logger;

import game.core.pub.script.IScript;
import game.core.pub.timer.SimpleTimerProcessor;
import game.core.pub.util.ExceptionEx;
import game.server.NettyGameServer;
import game.server.http.netty.GameHttpNettyNewServerImpl;
import game.server.http.netty.GameHttpNettyServerImpl;
import game.server.logic.activity.ActivityService;
import game.server.logic.crossIndigo.CrossIndigoService;
import game.server.logic.global.GameGlobalService;
import game.server.logic.guild.GuildService;
import game.server.logic.guildwar.GuildwarService;
import game.server.logic.herodispatch.HerodispatchService;
import game.server.logic.indigo.IndigoService;
import game.server.logic.line.GameLineManager;
import game.server.logic.mail.MailService;
import game.server.logic.operateActivity.OperateActivityService;
import game.server.logic.server.ServerStatusService;
import game.server.logic.snatchTerritory.SnatchTerritoryService;
import game.server.logic.teamHunt.TeamHuntService;
import game.server.thread.BackLogProcessor;
import game.server.thread.DispatchProcessor;
import game.server.thread.LogicProcessor;
import game.server.thread.LogicRestoreProcessor;
import game.server.thread.PlayerRestoreProcessor;
import game.server.thread.RechargeProcessor;
import game.server.thread.delay.DelayTaskProcessor;

/**
 *
 * <AUTHOR>
 * @date 2017-10-17
 */
public class ShutdownNettyHookScript implements IScript, Runnable {
	private static final Logger logger = Logger.getLogger(ShutdownNettyHookScript.class);

	@Override
	public void init() {
	}

	@Override
	public void destroy() {
	}

	@Override
	public Object call(String scriptName, Object arg) {
		if ("stop".equals(arg)) {
			run();// stop 表示外部在调用停服钩子了
		} else if ("reset".equals(arg)) {// 此处是方便解决未正常停服时，可以重新再启动停服钩子
			NettyGameServer.getInstance().removeShutdownHook();// 移除之前的hook
			NettyGameServer.getInstance().addShutdownHook(new Thread(this));// 添加新的hook
		}
		return null;
	}

	@Override
	public void run() {
		logger.info("ShutdownHookScript---Script dừng server-----begin-------------");
		logger.info("JVM exit, call GameServer.stop");
		NettyGameServer.getInstance().setStopping(true);
		// Trước tiên thay đổi trạng thái server thành trạng thái bảo trì và kick người chơi offline, ngăn người chơi đăng nhập lại
		ServerStatusService.getInstance().updateServerStatus(ServerStatusService.MAINTAIN, true);
		try {
			long begin = System.currentTimeMillis();
			logger.info("JVM exit, call GameServer.stop  threadSleepStart,now:" + begin);
			{
				// Dừng tất cả timer
				logger.info("Đóng timer!");
				SimpleTimerProcessor.getInstance().stop();
				// Dừng http server
				//logger.info("stop and await GameHttpServerImpl...");
				//GameHttpNettyServerImpl.getInstance().stop(2);
				// Dừng http server
				logger.info("stop and await GameHttpNewServerImpl...");
				GameHttpNettyNewServerImpl.getInstance().stop(2);
				// Dừng luồng phân phối tin nhắn
				logger.info("stop and await DispatchProcessor...");
				DispatchProcessor.getInstance().stopAndAwaitStop(false);

				// Luồng guild
				logger.info("GuildService save...");
				GuildService.getInstance().stopAndAwaitStop();

				// Điều phái tinh linh
				logger.info("HerodispatchService save...");
				HerodispatchService.getInstance().stopAndAwaitStop();

				// Luồng tổ đội
				logger.info("TeamHuntService save...");
				TeamHuntService.getInstance().stopAndAwaitStop();
				// Luồng Tam Quốc tranh bá
				logger.info("IndigoService save...");
				IndigoService.getInstance().stopAndAwaitStop();
				// Luồng Tam Quốc tranh bá
				logger.info("CrossIndigoService save...");
				CrossIndigoService.getInstance().stopAndAwaitStop();
				// Lưu trữ dữ liệu đạo quán
				logger.info("SnatchTerritoryService save...");
				SnatchTerritoryService.getInstance().stopAndAwaitStop();
				// Luồng hoạt động vận hành
				logger.info("OperateActivityService save...");
				OperateActivityService.getInstance().stopAndAwaitStop();
				// Luồng hoạt động
				logger.info("ActivityService save...");
				ActivityService.getInstance().stopAndAwaitStop();
				//Cuộc thi mở server
				logger.info("CompetitionRank save...");
				RankProcessor.getInstance().stopAndAwaitStop();
				// Luồng hoạt động chiến tranh liên minh
				logger.info("GuildwarService save...");
				GuildwarService.getInstance().stopAndAwaitStop();
				// Luồng nạp tiền
				logger.info("stop and await RechargeProcessor...");
				RechargeProcessor.getInstance().stopAndAwaitStop(false);
				// Luồng logic
				logger.info("stop and await LogicProcessor...");
				LogicProcessor.getInstance().stopAndAwaitStop();
				CrossArenaService.getInstance().stopAndAwaitStop();
				SuffleService.getInstance().stopAndAwaitStop();
				// Lưu trữ cấu hình toàn cục
				logger.info("GameGlobalService save...");
				GameGlobalService.getInstance().save();
				// Dừng luồng GameLine
				logger.info("stop and await GameLineManager...");
				GameLineManager.getInstance().stopAndAwaitStop();
				// Dừng thông báo toàn mạng, do là thông báo nên có thể dừng ngay lập tức
				DelayTaskProcessor.getInstance().stop(true);
				logger.info("stop immediately DelayTaskProcessor...");
			}
		} catch (Exception ex) {
			logger.error(ExceptionEx.e2s(ex));
		} finally {
			// Đóng luồng email
			MailService.getInstance().stopAndAwaitStop();
			// Đóng luồng lưu trữ dữ liệu người chơi
			logger.info("stop and await PlayerRestoreProcessor ...");
			PlayerRestoreProcessor.getInstance().stopAndAwaitStop();
			// Đóng luồng lưu trữ logic
			logger.info("stop and await LogicCacheProcessor ...");
			LogicRestoreProcessor.getInstance().stopAndAwaitStop();

		}

		{ // Lưu ý: Luồng xử lý log cần đóng cuối cùng
			logger.info("stop and await BackLogProcessor...");
			BackLogProcessor.getInstance().stopAndAwaitStop(false);
		}
		// Logic script vận hành có phụ thuộc vào việc in này, nghiêm cấm sửa đổi
		logger.info("Đóng server!");
		logger.info("ShutdownHookScript---------end------------");
		return;
	}
}
