package indigo;

import org.apache.commons.lang.StringUtils;

import Message.S2CBackpackMsg.PropInfo;
import Message.S2CPlayerMsg.PromptType;
import Message.Inner.GRCrossIndigo.GRGetPourIndigoReq;
import Message.Inner.GRCrossIndigo.GRPourSuccessIndigoRsp;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.BePouredInfo;
import game.route.indigo.bean.PourInfo;
import game.route.indigo.bean.PourOrder;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
import game.util.BeanTemplet;
/**
 * 下注成功
 * <AUTHOR>
 *
 */
public class PourSuccessIndigoScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		long playerId = (long) args.get(Key.ARG1);
		GameServer server = (GameServer) args.get(Key.ARG2);
		String orderId = (String) args.get(Key.ARG3);
		long pourId = (long) args.get(Key.ARG4);
		int type = (int) args.get(Key.ARG5);
		pourSuccessIndigo(playerId, orderId, pourId, type, server);
		return null;
	}

	/**
	 * 下注
	 * 
	 * @param player
	 * @param pourId
	 * @param type
	 *            1：普通 2： 高级
	 */
	public void pourSuccessIndigo(long playerId, String orderId, long pourId, int type, GameServer server) {
		IndigoService indigoService = IndigoService.getInstance();
		Integer groupId = ServerService.getInstance().getGroupIdByServer(server.getId());
		// 重复下注
		if (indigoService.getPourMap().containsKey(playerId)) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 292);
			sendResult(server, playerId, type, false);
			return;
		}
		// 找到订单
		PourOrder order = indigoService.getPourOrderMap().get(playerId);
		long pourPlayerId;
		int pourType;
		if (null == order || !order.getOrderId().equals(orderId)) {
			pourPlayerId = pourId;
			pourType = type;
		} else {
			pourPlayerId = order.getPouredId();
			pourType = order.getType();
		}
		// 找到下注目标并更新下注信息------------------
		// 下注目标
		BePouredInfo pouredInfo = indigoService.getPouredInfo(pourPlayerId, groupId);
		if (pouredInfo == null) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 293);
			sendResult(server, playerId, type, false);
			return;
		}
		// 更新标识
		PourInfo pourInf = new PourInfo();
		pourInf.setPlayerId(playerId);
		pourInf.setPouredId(pourId);
		pourInf.setType(type);
		pourInf.setGroupId(groupId);
		indigoService.getPourMap().put(playerId, pourInf);
		// 下注消耗
		type = pourType > 2 ? 2 : pourType;
		type = pourType < 1 ? 1 : pourType;
		int num = Integer.parseInt(StringUtils.split(BeanTemplet.getGlobalBean(210).getStr_value(), ",")[type - 1]);
				
		pouredInfo.setNum(pouredInfo.getNum() + 1);
		pouredInfo.setPourNum(pouredInfo.getPourNum() + num);
		// 计算赔率
		String pourInfo = StringUtils.split(BeanTemplet.getGlobalBean(209).getStr_value(), ";")[pouredInfo.getRank() - 1];
		int s1 = Integer.parseInt(StringUtils.split(pourInfo, ",")[0]);// 初始赔率
		int s2 = Integer.parseInt(StringUtils.split(pourInfo, ",")[1]);// 每一万金币减少多少赔率
		long t1 = pouredInfo.getPourNum();// 总金额
		long odds = Math.max(s1 - (t1 / 10000) * (s2), BeanTemplet.getGlobalBean(214).getInt_value());
		pouredInfo.setOdds(odds);
		// 推送结果
		sendResult(server, playerId, pourType, true);
		// 推送下注信息
		GRGetPourIndigoReq.Builder builder = GRGetPourIndigoReq.newBuilder();
		builder.setPlayerId(playerId);
		indigoService.getPourIndigo(server.getId(), builder.build());
		return;
	}
	
	private void sendResult(GameServer server, long playerId, int type, boolean success) {
		IndigoService indigoService = IndigoService.getInstance();
		GRPourSuccessIndigoRsp.Builder builder = GRPourSuccessIndigoRsp.newBuilder();
		builder.setPlayerId(playerId);
		builder.setResult(success);
		// 下注消耗
		type = type > 2 ? 2 : type;
		type = type < 1 ? 1 : type;
		int num = Integer.parseInt(StringUtils.split(BeanTemplet.getGlobalBean(210).getStr_value(), ",")[type - 1]);
		PropInfo.Builder p = PropInfo.newBuilder();
		p.setId(indigoService.getPourItemType());
		p.setNum(num);
		builder.addItems(p);
		GRMessageUtils.sendMsg2GameServer(server.getId(),
				indigoService.genGRIndigoRsp(playerId, GRPourSuccessIndigoRsp.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
