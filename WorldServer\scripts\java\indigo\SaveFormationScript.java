package indigo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import Message.C2SIndigoMsg.IndigoFormation;
import Message.S2CIndigoMsg.IndigoStage;
import Message.S2CIndigoMsg.RaceStatus;
import Message.S2CIndigoMsg.SaveHeroesRsp;
import Message.S2CIndigoMsg.SaveHeroesRspID;
import Message.S2CPlayerMsg.PromptType;
import game.core.pub.script.IScript;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.HeroPos;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SingleRace;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
import game.util.BeanTemplet;

/**
 * 保存布阵
 * 
 * <AUTHOR>
 *
 *         2018年10月9日
 */
public class SaveFormationScript implements IScript {
	private final Logger logger = Logger.getLogger(SaveFormationScript.class);

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		long playerId = (long) args.get(Key.ARG1);
		GameServer server = (GameServer) args.get(Key.ARG2);
		@SuppressWarnings("unchecked")
		List<IndigoFormation> formations = (List<IndigoFormation>) args.get(Key.ARG3);
		saveFormation(server, playerId, formations);
		return null;
	}

	/**
	 * 保存布阵
	 * 
	 * @param player
	 */
	public void saveFormation(GameServer server, long playerId, List<IndigoFormation> formations) {
		IndigoService indigoService = IndigoService.getInstance();
		if (indigoService.getIndigoStage() == IndigoStage.INDIGO_IDLE_VALUE) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 294);// 当前阶段无法改变阵型
			return;
		}
		if (indigoService.getIndigoStage() == IndigoStage.INDIGO_READY_VALUE) {
			if (indigoService.getIndigoStageEndTime() - System.currentTimeMillis() < 60 * 1000) {
				GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 294);// 当前阶段无法改变阵型
				return;
			}
		}
		if (indigoService.getIndigoStage() == IndigoStage.INDIGO_FIGHT_VALUE
				&& indigoService.getRaceStatus() == RaceStatus.RACE_READY_VALUE) {
			if (indigoService.getFightStageEndTime() - System.currentTimeMillis() < 60 * 1000) {
				GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 294);// 当前阶段无法改变阵型
				return;
			}
		}
		if (!checkValid(formations)) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 48);// 参数异常
			return;
		}
		Participant participant = indigoService.getParticipantByPlayerId(playerId);
		if (participant == null) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 296);// 未报名参赛
			return;
		}
		participant.getFormations().clear();
		// 更新阵型
		for (int i = 0; i < formations.size(); i++) {
			IndigoFormation indigoFormation = formations.get(i);
			Formation formation = parseFormation(participant, indigoFormation);
			participant.getFormations().add(formation);
			sycFormationToRace(participant);
		}
		// 推送
		SaveHeroesRsp.Builder builder = SaveHeroesRsp.newBuilder();
		for (Formation formation : participant.getFormations()) {
			builder.addFormation(formation.genBuilder());
		}
		for (Hero hero : participant.getHeroList()) {
			builder.addHero(hero.genHeroPos());
		}

		GRMessageUtils.sendMsg2GameServer(server.getId(),
				indigoService.genGRIndigoRsp(playerId, SaveHeroesRspID.SaveHeroesRspMsgID_VALUE, builder.build().toByteString()));
	}

	/**
	 * 同步阵型到当前轮次准备的比赛
	 * 
	 * @param participant
	 * @param formation
	 * @param fSession
	 *            初赛决赛一起算： 1-9
	 */
	private void sycFormationToRace(Participant participant) {
		if (IndigoService.getInstance().getIndigoStage() == IndigoStage.INDIGO_FIGHT_VALUE
				&& IndigoService.getInstance().getRaceStatus() == RaceStatus.RACE_READY_VALUE) {
			for (SingleRace singleRace : participant.getMyRaces()) {
				if (singleRace.getStatus() == RaceStatus.RACE_READY_VALUE) {
					if (singleRace.getRedId() == participant.getPlayerId()) {
						singleRace.setRedFormation(participant.getFormations());
					} else {
						singleRace.setBlueFormation(participant.getFormations());
					}
					// 比赛推送双方
					IndigoService.getInstance().myRaceNotify(singleRace);
				}
			}
		}
	}

	/**
	 * 验证布阵合法性
	 * 
	 * @param formations
	 * @return
	 */
	private boolean checkValid(List<IndigoFormation> formations) {
		Map<Integer, Integer> map = new HashMap<>();// <英雄ID,上阵次数>
		List<Integer> sizeList = new ArrayList<>();// 每个阵型的上阵人数
//		if (formations.size() != 6 && formations.size() != 9) {// 阵型数量有误
//			logger.error("三国霸主阵型验证失败：=======阵型数量有误");
//			return false;
//		}
		List<Integer> heroes = new ArrayList<>();
		// 英雄上阵是否重复并记录上阵次数
		for (IndigoFormation indigoFormation : formations) {
			int size = 0;
			heroes.clear();
			for (int heroId : indigoFormation.getHeroIdList()) {
				if (heroId == 0) {
					continue;
				}
				if (heroes.contains(heroId)) {
					logger.error("三国霸主阵型验证失败：=======阵型中英雄重复");
					return false;
				} else {
					heroes.add(heroId);
				}
				Integer num = map.get(heroId);
				if (num == null) {
					num = 1;
				} else {
					num += 1;
				}
				map.put(heroId, num);
				size++;
			}
			sizeList.add(size);
		}
		// 英雄上阵次数限制
		// for (Integer heroNum : map.values()) {
		// if (formations.size() == 6 && heroNum != 2) {// 初赛 只能上阵2次
		// logger.error("三国霸主阵型验证失败：=======初赛 只能上阵2次");
		// return false;
		// } else if (formations.size() == 9 && heroNum != 3) {// 初赛+决赛 只能上阵3次
		// logger.error("三国霸主阵型验证失败：=======初赛+决赛 只能上阵3次");
		// return false;
		// }
		// }
		// 每轮阵型人数限制
		// t_quartzBean quartzBean = BeanTemplet.getQuartzBean(1);
		// int index = 1;
		// for (Integer curNum : sizeList) {
		// int needSize = 0;
		// if (index == 1) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getPre_1_num(),
		// ",")[0]);
		// } else if (index == 2) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getPre_2_num(),
		// ",")[0]);
		// } else if (index == 3) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getPre_3_num(),
		// ",")[0]);
		// } else if (index == 4) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getPre_4_num(),
		// ",")[0]);
		// } else if (index == 5) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getPre_5_num(),
		// ",")[0]);
		// } else if (index == 6) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getPre_6_num(),
		// ",")[0]);
		// } else if (index == 7) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getFour_num(),
		// ",")[0]);
		// } else if (index == 8) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getTwo_num(),
		// ",")[0]);
		// } else if (index == 9) {
		// needSize = Integer.parseInt(StringUtils.split(quartzBean.getOne_num(),
		// ",")[0]);
		// }
		// if (needSize != curNum) {
		// logger.error("三国霸主阵型验证失败：=======第 " + index + " 轮数量不足");
		// return false;
		// }
		// index++;
		// }
		return true;
	}

	/**
	 * 解析阵容
	 * 
	 * @param participant
	 * @param indigoFormation
	 * @return
	 */
	private Formation parseFormation(Participant participant, IndigoFormation indigoFormation) {
		Formation formation = new Formation();
		formation.setPlayerId(participant.getPlayerId());
		int needSize = 0;
		for (int j = 0; j < indigoFormation.getHeroIdCount(); j++) {
			int heroId = indigoFormation.getHeroId(j);
			if (heroId == 0) {
				continue;
			}
			Hero hero = participant.getHero(heroId);
			if (hero == null) {
				continue;
			}
			HeroPos heroPos = new HeroPos();
			heroPos.setHeroId(hero.getId());
			heroPos.setLvl(hero.getLevel());
			heroPos.setStar(hero.getStar());
			heroPos.setPower(hero.getPower());
			heroPos.setPosition(j);
			formation.getInfoList().add(heroPos);
			formation.setPower(formation.getPower() + hero.getPower());
			needSize++;
		}
		int offensive = (int) Math
				.ceil(formation.getPower() * ((float) BeanTemplet.getGlobalBean(66).getInt_value() / 10000)
						+ (float) participant.getOffensiveAdd() * needSize / 6);
		formation.setOffensive(offensive);
		return formation;
	}

}
