package logic.guild;

import game.core.pub.script.IScript;
import game.server.logic.constant.ModelMsg;
import game.server.logic.guild.bean.Guild;
import game.server.logic.guild.bean.GuildMember;
import game.server.logic.guild.bean.Position;
import game.server.logic.guild.bean.history.GuildHistory;
import game.server.logic.player.RoleView;
import game.server.logic.player.RoleViewService;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
import game.server.logic.util.ScriptArgs.Key;

/**
 * Kiểm tra tình hình đăng nhập hội trưởng liên minh
 *
 * Không đăng nhập lâu dài sẽ thiền nhượng vị trí hội trưởng
 *
 * <AUTHOR>
 * @date 2018年10月10日
 */
public class CheckGuildChairManScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Guild guild = (Guild) args.get(Key.ARG1);

		long oldChairManId = guild.getChairman().getPlayerId();
		RoleView roleView = RoleViewService.getRoleById(oldChairManId);
		int day = BeanTemplet.getGlobalBean(311).getInt_value();// Bao nhiêu ngày không online hệ thống thử thiền nhượng minh chủ
		if (System.currentTimeMillis() - roleView.getLastLoginTime() > day * 24 * 60 * 60 * 1000L) {//
			// Thử thiền nhượng minh chủ
			GuildMember oldMember = guild.getMemberByPlayerId(oldChairManId);
			GuildMember member = guild.getAllMembers().stream().filter(m -> checkLoginTime(m, oldChairManId))
					.sorted((m, n) -> m.compareTo(n)).findFirst().orElse(null);
			if (member != null) {
				// Loại bỏ minh chủ mới khỏi tập hợp cũ
				if (member.getPosition() == Position.VICE_CHAIRMAN.value()) {
					guild.getViceChairmen().remove(member);
				} else {
					guild.getNormalMembers().remove(member);
				}
				// Thiết lập minh chủ mới
				member.setPosition(Position.CHAIRMAN.value());
				guild.setChairman(member);
				// Minh chủ thiền nhượng được thiết lập thành thành viên thường
				oldMember.setPosition(Position.NORMAL.value());
				guild.getNormalMembers().add(oldMember);
				// Tạo nhật ký liên minh
				RoleView newRoleView = RoleViewService.getRoleById(member.getPlayerId());
				long leftDay = (System.currentTimeMillis() - roleView.getLastLoginTime()) / (24 * 60 * 60 * 1000L);
				GuildHistory his = new GuildHistory(ModelMsg.GUILD_CHAIRMAN_CHANGE.value(), roleView.getName(),
						leftDay + "", newRoleView.getName());
				guild.addGuildHistory(his);
			}
		}

		return null;
	}

	/**
	 * Đánh giá tình hình online
	 *
	 * @param guildMember
	 * @param oldChairManId
	 * @return
	 */
	private boolean checkLoginTime(GuildMember guildMember, long oldChairManId) {
		if (guildMember.getPlayerId() == oldChairManId) {
			return false;
		}
		RoleView roleView = RoleViewService.getRoleById(guildMember.getPlayerId());
		int day = BeanTemplet.getGlobalBean(312).getInt_value();// Giới hạn số ngày chưa online
		if (System.currentTimeMillis() - roleView.getLastLoginTime() > day * 24 * 60 * 60 * 1000L) {
			return false;
		} else {
			return true;
		}
	}
}
