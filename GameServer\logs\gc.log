Java HotSpot(TM) 64-Bit Server VM (25.181-b13) for windows-amd64 JRE (1.8.0_181-b13), built on Jul  7 2018 04:01:33 by "java_re" with MS VC++ 10.0 (VS2010)
Memory: 4k page, physical 16710568k(1336176k free), swap 33419236k(11215224k free)
CommandLine flags: -XX:GCPauseIntervalMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./oom.hprof -XX:InitialHeapSize=536870912 -XX:MaxGCPauseMillis=50 -XX:MaxHeapSize=4277905408 -XX:MaxNewSize=536870912 -XX:NewSize=536870912 -XX:+PrintGC -XX:+PrintGCTimeStamps -XX:SurvivorRatio=6 -XX:ThreadStackSize=256 -XX:+UnlockExperimentalVMOptions -XX:+UseCompressedClassPointers -XX:+UseCompressedOops -XX:+UseG1GC -XX:-UseLargePagesIndividualAllocation 
2.225: [GC pause (Metadata GC Threshold) (young) (initial-mark) 104M->13M(512M), 0.0104929 secs]
2.236: [GC concurrent-root-region-scan-start]
2.238: [GC concurrent-root-region-scan-end, 0.0022879 secs]
2.238: [GC concurrent-mark-start]
2.384: [GC concurrent-mark-end, 0.0003317 secs]
2.384: [GC remark, 0.0018176 secs]
2.535: [GC cleanup 51M->51M(512M), 0.0005380 secs]
3.712: [GC pause (G1 Evacuation Pause) (young) (initial-mark) 511M->23M(536M), 0.0181143 secs]
3.730: [GC concurrent-root-region-scan-start]
3.738: [GC concurrent-root-region-scan-end, 0.0077603 secs]
3.738: [GC concurrent-mark-start]
3.879: [GC concurrent-mark-end, 0.0003728 secs]
3.879: [GC remark, 0.0025391 secs]
4.030: [GC cleanup 258M->258M(536M), 0.0005367 secs]
5.626: [GC pause (G1 Evacuation Pause) (young) 511M->36M(549M), 0.0183010 secs]
6.182: [GC pause (G1 Evacuation Pause) (young) 515M->47M(565M), 0.0254088 secs]
6.360: [GC pause (G1 Evacuation Pause) (young) 511M->46M(565M), 0.0188463 secs]
8.848: [GC pause (G1 Evacuation Pause) (young) 515M->69M(582M), 0.0455005 secs]
10.655: [GC pause (G1 Evacuation Pause) (young) 516M->62M(582M), 0.0358279 secs]
12.680: [GC pause (G1 Evacuation Pause) (young) 516M->64M(582M), 0.0372618 secs]
14.657: [GC pause (G1 Evacuation Pause) (young) 516M->63M(582M), 0.0361086 secs]
16.224: [GC pause (G1 Evacuation Pause) (young) 516M->62M(582M), 0.0309473 secs]
17.900: [GC pause (G1 Evacuation Pause) (young) 516M->64M(582M), 0.0321221 secs]
19.570: [GC pause (G1 Evacuation Pause) (young) 516M->62M(582M), 0.0305444 secs]
21.553: [GC pause (G1 Evacuation Pause) (young) 516M->67M(582M), 0.0437236 secs]
23.554: [GC pause (G1 Evacuation Pause) (young) 516M->71M(584M), 0.0413481 secs]
25.643: [GC pause (G1 Evacuation Pause) (young) 525M->69M(584M), 0.0389428 secs]
27.427: [GC pause (G1 Evacuation Pause) (young) 525M->67M(584M), 0.0286960 secs]
29.204: [GC pause (G1 Evacuation Pause) (young) 525M->65M(584M), 0.0282786 secs]
30.690: [GC pause (G1 Evacuation Pause) (young) 525M->64M(584M), 0.0324188 secs]
35.854: [GC pause (Metadata GC Threshold) (young) (initial-mark) 364M->84M(584M), 0.0162909 secs]
35.870: [GC concurrent-root-region-scan-start]
35.876: [GC concurrent-root-region-scan-end, 0.0060685 secs]
35.877: [GC concurrent-mark-start]
36.020: [GC concurrent-mark-end, 0.0307661 secs]
36.020: [GC remark, 0.0058619 secs]
36.175: [GC cleanup 136M->136M(584M), 0.0005591 secs]
37.613: [GC pause (G1 Evacuation Pause) (young) (initial-mark) 566M->126M(665M), 0.0426852 secs]
37.655: [GC concurrent-root-region-scan-start]
37.678: [GC concurrent-root-region-scan-end, 0.0228190 secs]
37.678: [GC concurrent-mark-start]
37.804: [GC concurrent-mark-end, 0.0381963 secs]
37.804: [GC remark, 0.0052482 secs]
37.958: [GC cleanup 205M->205M(665M), 0.0004838 secs]
39.075: [GC pause (G1 Evacuation Pause) (young) 598M->165M(732M), 0.0565095 secs]
40.345: [GC pause (G1 Evacuation Pause) (young) 623M->208M(769M), 0.0516953 secs]
456.482: [GC pause (G1 Evacuation Pause) (young) 663M->265M(817M), 0.0528114 secs]
2754.706: [GC pause (G1 Evacuation Pause) (young) 709M->270M(817M), 0.0300671 secs]
4533.645: [Full GC (System.gc())  677M->218M(729M), 0.6314298 secs]
5431.817: [GC pause (G1 Evacuation Pause) (young) 704M->224M(735M), 0.0033513 secs]
