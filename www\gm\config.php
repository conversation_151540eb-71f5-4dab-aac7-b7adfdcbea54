<?php 
header("Content-type: text/html; charset=utf8");
error_reporting(0);
session_start();
date_default_timezone_set("PRC");
$conn=@mysql_connect("127.0.0.1","root","123456") or die("<PERSON><PERSON><PERSON> nối cơ sở dữ liệu thất bại, vui lòng liên hệ quản trị viên!");
mysql_select_db('h_sanguo_test_user_center',$conn);
mysql_query("set names UTF8"); 

function post_curl($url, $data_string) {
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_POST, 1);	
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
	curl_setopt($ch, CURLOPT_POSTFIELDS, $data_string);
	$result = curl_exec($ch);
	if(curl_errno($ch)){
		print_r(curl_error($ch));
	}
	curl_close($ch);
	return $result;	
}
$serverlist=[
	1=>[
		"url"=>"http://20.214.240.223:19001/hgame/background_api",
		"table"=>"h_sanguo_test_game1"
	]
];

?>