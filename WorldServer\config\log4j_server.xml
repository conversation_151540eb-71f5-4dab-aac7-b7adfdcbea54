<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/" debug="false">
    <appender name="FileAppender" class="org.apache.log4j.DailyRollingFileAppender">
		<param name="File" value="logs/GameServer.log" />
		<param name="Threshold" value="ALL" />
		<!-- <param name="MaxFileSize" value="10240KB" /> -->
		<!--<param name="MaxBackupIndex" value="100" /> -->
		<param name="DatePattern" value="'.'yyyyMMdd" />
		<layout class="org.apache.log4j.PatternLayout">
			<param name="ConversionPattern" value="[%d{yyyy-MM-dd HH:mm:ss:SSS} %p][%l]%m%n" />
		</layout>
    </appender>
    <!-- Xuất file sử dụng bất đồng bộ -->
    <appender name="AsyncFileAppender" class="org.apache.log4j.AsyncAppender">
		<param name="BufferSize" value="2048" />
		<appender-ref ref="FileAppender" />
    </appender>
	
	<appender name="RollingFileAppender" class="org.apache.log4j.RollingFileAppender">
		<appender-ref ref="FileAppender" />
    </appender>
	
	<root>
		<level value="info" />
		<appender-ref ref="AsyncFileAppender" />
    </root>
</log4j:configuration>