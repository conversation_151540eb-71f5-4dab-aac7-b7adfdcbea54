[2024-09-17 19:10:06:229 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-17 19:10:07:112 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-17 19:10:07:143 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-17 19:10:07:144 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-17 19:10:08:845 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-17 19:10:10:496 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-17 19:10:10:498 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-17 19:10:10:745 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-17 19:10:10:747 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-17 19:10:10:766 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-17 19:10:11:027 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-17 19:10:11:485 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-17 19:10:12:502 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-17 19:10:20:846 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/*************:39001
[2024-09-17 19:10:20:847 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-17 19:10:20:853 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-17 19:10:20:863 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
[2024-09-17 19:10:30:915 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-17 19:10:30:917 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-17 19:10:40:921 INFO](TcpClient.java:130)sessionCreate, sessionId:3,ip:/*************:39001
[2024-09-17 19:10:40:921 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-17 19:10:40:922 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-17 19:10:40:928 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
[2024-09-17 19:10:50:939 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-17 19:10:50:940 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-17 19:11:00:944 INFO](TcpClient.java:130)sessionCreate, sessionId:4,ip:/*************:39001
[2024-09-17 19:11:00:944 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-17 19:11:00:947 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-17 19:11:00:952 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
[2024-09-17 19:11:10:963 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-17 19:11:10:965 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-17 19:11:20:969 INFO](TcpClient.java:130)sessionCreate, sessionId:5,ip:/*************:39001
[2024-09-17 19:11:20:970 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-17 19:11:20:970 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-17 19:11:20:975 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
[2024-09-17 19:11:52:888 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-17 19:11:53:494 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-17 19:11:53:523 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-17 19:11:53:524 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-17 19:11:55:227 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-17 19:11:56:704 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-17 19:11:56:706 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-17 19:11:56:886 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-17 19:11:56:888 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-17 19:11:56:904 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-17 19:11:57:164 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-17 19:11:57:599 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-17 19:13:05:254 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:**************,cmd:2
[2024-09-17 19:13:07:056 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/**************:39001
[2024-09-17 19:13:07:057 INFO](CommunicationC.java:198)connect success, ip: [**************], port = 39001
[2024-09-17 19:13:07:070 INFO](TcpClient.java:86)[游戏服][主公一区][**************:39001]connect succ
[2024-09-17 19:13:07:112 INFO](AuthenticationHandler.java:35)connect serverIp:**************---主公一区,authErrorCode:0
