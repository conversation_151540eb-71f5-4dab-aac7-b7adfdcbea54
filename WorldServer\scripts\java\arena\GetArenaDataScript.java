package arena;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import Message.Inner.GRCrossArena.GRAGetDataReq;
import Message.Inner.GRCrossArena.GRAGetDataRsp;
import com.google.protobuf.ByteString;
import game.core.pub.script.IScript;
import game.route.arena.ArenaService;
import game.route.arena.bean.ArenaInfo;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SingleRace;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
/**
 * 获取三国霸主信息
 * <AUTHOR>
 *
 * 2018年10月9日
 */
public class GetArenaDataScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		GRAGetDataReq req = (GRAGetDataReq) args.get(Key.ARG2);
		getData(serverId, req);
		return null;
	}

	/** 获取信息 */
	public void getData(int serverId, GRAGetDataReq req) {
		ArenaService arenaService = ArenaService.getInstance();
		GRAGetDataRsp.Builder builder = arenaService.genGRADataBuilder(req.getPlayerId());
		// 推送
		GRMessageUtils.sendMsg2GameServer(serverId,
				arenaService.genGRArenaRsp(req.getPlayerId(), GRAGetDataRsp.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
