package arena;

import Message.Inner.GRCrossArena.GRAPlayerInfo;
import game.core.pub.script.IScript;
import game.route.arena.ArenaService;
import game.route.indigo.IndigoService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;

/**
 * 向gameServer获取玩家详细信息
 * <AUTHOR>
 *
 */
public class ReqPlayerInfoScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(ScriptArgs.Key.ARG1);
		long playerId = (long) args.get(ScriptArgs.Key.ARG2);
		getPlayerInfo(serverId, playerId);
		return null;
	}
	
	private void getPlayerInfo(int serverId, long playerId) {
		ArenaService arenaService = ArenaService.getInstance();
		GRAPlayerInfo.Builder builder = GRAPlayerInfo.newBuilder();
		builder.setPlayerId(playerId);
		GRMessageUtils.sendMsg2GameServer(serverId,
				arenaService.genGRArenaRsp(0, GRAPlayerInfo.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
