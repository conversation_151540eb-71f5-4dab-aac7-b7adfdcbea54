package indigo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import org.apache.log4j.Logger;

import game.core.pub.script.IScript;
import game.route.hero.HeroService;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SingleRace;
import game.route.util.IdGenerator;
import game.route.util.ScriptArgs;

/**
 * 分组并创建比赛
 * 
 * <AUTHOR>
 *
 *         2018年9月28日
 */
public class CreateRacesScript implements IScript {
	private final Logger logger = Logger.getLogger(CreateRacesScript.class);

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {

		ScriptArgs args = (ScriptArgs) arg;
		int session = (Integer) args.get(ScriptArgs.Key.ARG1);

		matchRaces(session);

		return null;
	}

	/**
	 * 随机分组
	 */
	public void matchRaces(int session) {
		IndigoService indigoService = IndigoService.getInstance();
		if (session <= 5) {
			Map<Integer, List<Participant>> parList = new HashMap<>();
			for (Entry<Integer, Map<Long, Participant>> en : indigoService.getParticipantMap().entrySet()) {
				Integer groupId = en.getKey();
				Map<Long, Participant> list = en.getValue();
				List<Participant> l = parList.get(groupId);
				if (null == l) {
					l = new ArrayList<>();
					parList.put(groupId, l);
				}
				for (Participant p : list.values()) {
					if (!p.isWeed())
						l.add(p);
				}
				Collections.shuffle(l);
				if (parList.size() % 2 != 0) {
					logger.info("====逻辑错误=========参与分组的人数为单数:" + parList.size());
				}
				// 两两匹配
				Participant red = null;
				Participant blue = null;
				for (Participant participant : l) {
					if (red == null) {
						red = participant;
					} else if (blue == null) {
						blue = participant;
					}
					if (red != null && blue != null) {
						createRace(session, red, blue, groupId);
						red = null;
						blue = null;
					}
				}
			}
		} else {
			if (session == 6) {
				Map<Integer, Map<Integer, Map<String, SingleRace>>> raceMap = indigoService.getRaceMap();
				Map<Integer, Map<String, SingleRace>> sMap = raceMap.get(session - 1);
				for (Entry<Integer, Map<String, SingleRace>> en : sMap.entrySet()) {
					Integer groupId = en.getKey();
					Participant red = null;
					Participant blue = null;
					Map<String, SingleRace> map = en.getValue();
					// 两两匹配
					for (SingleRace race : map.values()) {
						if (red == null) {
							red = indigoService.getParticipantByPlayerId(race.getWinner());
						} else if (blue == null) {
							blue = indigoService.getParticipantByPlayerId(race.getWinner());
						}
						if (red != null && blue != null) {
							createRace(session, red, blue, groupId);
							red = null;
							blue = null;
						}
					}
				}
			} else {
				Map<Integer, Map<Integer, List<SingleRace>>> finalRaceMap = indigoService.getFinalRaceMap();
				Map<Integer, List<SingleRace>> map = finalRaceMap.get(session - 1);
				for (Entry<Integer, List<SingleRace>> en : map.entrySet()) {
					Integer groupId = en.getKey();
					List<SingleRace> list = en.getValue();
					// 两两匹配
					Participant red = null;
					Participant blue = null;
					for (SingleRace race : list) {
						if (red == null) {
							red = indigoService.getParticipantByPlayerId(race.getWinner());
						} else if (blue == null) {
							blue = indigoService.getParticipantByPlayerId(race.getWinner());
						}
						if (red != null && blue != null) {
							createRace(session, red, blue, groupId);
							red = null;
							blue = null;
						}
					}
				}
			}
		}
		logger.info("=============本地比赛第" + session + "轮分组完成");
	}

	/**
	 * 创建比赛并加入比赛列表
	 * 
	 * @param session
	 * @param red
	 * @param blue
	 */
	private void createRace(int session, Participant red, Participant blue, int groudId) {
		SingleRace race = new SingleRace();
		race.setId(IdGenerator.genUUID());
		race.setSession(session);
		// 红方
		race.setRedId(red.getPlayerId());
		race.setRedName(red.getName());
		race.setRedSex(red.getSex());
		race.setRedLvl(red.getLevel());
		race.setRedShowHeroId(HeroService.getInstance().genHeroInfoStr(red.getShowHeroId(), red.getStar()));
		race.setRedFormation(red.getFormations());
		red.getMyRaceList().add(race.getId());// 添加我的比赛
		// 蓝方
		race.setBlueId(blue.getPlayerId());
		race.setBlueName(blue.getName());
		race.setBlueSex(blue.getSex());
		race.setBlueLvl(blue.getLevel());
		race.setBlueShowHeroId(HeroService.getInstance().genHeroInfoStr(blue.getShowHeroId(), blue.getStar()));
		race.setBlueFormation(blue.getFormations());
		blue.getMyRaceList().add(race.getId());// 添加我的比赛
		addRace(race, groudId);
		// 比赛推送双方
		IndigoService.getInstance().myRaceNotify(race);
		// 决赛推送
		if (session > 5) {// 单场比赛战斗状态
			IndigoService.getInstance().finalRaceNotify(session);
		}
	}

	/**
	 * 追加比赛
	 * 
	 * @param race
	 */
	private void addRace(SingleRace race, int groupId) {
		IndigoService indigoService = IndigoService.getInstance();
		Map<Integer, Map<Integer, Map<String, SingleRace>>> raceMap = indigoService.getRaceMap();
		Map<Integer, Map<String, SingleRace>> groupMap = raceMap.get(race.getSession());
		if (null == groupMap) {
			groupMap = new ConcurrentHashMap<>();
			raceMap.put(race.getSession(), groupMap);
		}
		Map<String, SingleRace> map = groupMap.get(groupId);
		if (map == null) {
			map = new ConcurrentHashMap<>();
			groupMap.put(groupId, map);
		}
		map.put(race.getId(), race);
		if (race.getSession() > 5) {
			Map<Integer, List<SingleRace>> map2 = indigoService.getFinalRaceMap().get(race.getSession());
			if (map2 == null) {
				map2 = new HashMap<>();
				indigoService.getFinalRaceMap().put(race.getSession(), map2);
			}
			List<SingleRace> list = map2.get(groupId);
			if (null == list) {
				list = new ArrayList<>();
				map2.put(groupId, list);
			}
			list.add(race);
		}
	}

}
