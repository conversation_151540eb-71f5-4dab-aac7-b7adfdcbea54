package arena;

import java.util.ArrayList;
import java.util.List;

import Message.S2CArenaMsg;
import Message.S2CCrossArenaMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import Message.S2CIndigoMsg.SingleRankMsg;
import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.Inner.GRCrossArena.GRAPlayerInfoReq;
import game.core.pub.script.IScript;
import game.route.arena.ArenaService;
import game.route.server.ServerService;
import game.route.util.ScriptArgs;

/**
 * 向gameServer获取玩家详细信息
 * <AUTHOR>
 *
 */
public class PlayerInfoScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRAPlayerInfoReq req = (GRAPlayerInfoReq) args.get(ScriptArgs.Key.ARG1);
		int serverId = (int) args.get(ScriptArgs.Key.ARG2);
		ByteString rsp = req.getRsp();
		S2CCrossArenaMsg.CrossEnemyMsg info = null;
		try {
			info = S2CCrossArenaMsg.CrossEnemyMsg.parseFrom(rsp);
		} catch (InvalidProtocolBufferException e) {
			e.printStackTrace();
		}
		ArenaService.getInstance().ResigePlayer(serverId,info.getPlayerId(),info);
		return null;
	}
}
