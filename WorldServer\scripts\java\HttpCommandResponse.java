
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import com.sun.net.httpserver.HttpExchange;

import data.GameDataManager;
import game.core.pub.script.IScript;
import game.core.pub.script.ScriptJavaLoader;
import game.core.pub.script.ScriptManager;
import game.route.RouteServer;
import game.route.http.RouteHttpServerImpl;
import game.route.server.ServerService;

/**
 * Description of HttpCommandResponse
 *
 * <AUTHOR>
 * @date 2015-5-21
 */
public class HttpCommandResponse implements IScript {

	private final Logger log = Logger.getLogger(HttpCommandResponse.class);
	private final long TIMEOUT = 5 * 60 * 1000;
	private final String VERIFIEDSEQUENCE = "aogxAL7\"~>RoqalJ[GN{nd,.vd)Acp:4se5";

	public int getId() {
		return 1000;
	}

	@Override
	public void init() {
	}

	@Override
	public void destroy() {

	}

	@SuppressWarnings("unchecked")
	@Override
	public Object call(String scriptName, Object arg) {
		HttpExchange he = (HttpExchange) arg;
		String remoteAddress = he.getRemoteAddress().toString();
		HashMap<String, String> parameters = (HashMap<String, String>) he.getAttribute("parameters");
		String command = parameters.get("command");
		if (command == null || command.equalsIgnoreCase("")) {
			log.error("无效的Http命令");
			return RouteHttpServerImpl.HttpRet.INVALID.desc();
		} else {
			log.info("收到Http请求: [" + command + "] ----------------------------------------");
			Iterator<Map.Entry<String, String>> it = parameters.entrySet().iterator();
			while (it.hasNext()) {
				Map.Entry<String, String> entry = it.next();
				log.info("Key: [" + entry.getKey() + "] Value: [" + entry.getValue() + "]");
			}
			log.info("-----------------------------------------------------------------------");
		}

		// if (!isLegalClient(parameters) && !ServerConfig.getInstance().isTest()) {
		// return RouteHttpServerImpl.HttpRet.ILLEGAL.desc();
		// }

		String ret = RouteHttpServerImpl.HttpRet.INVALID.desc();
		switch (command.toLowerCase()) {
		case "stop":
			int time = 5;
			new Thread(new DelayedStopServer(time)).start();
    		ret = time + " giây sau bắt đầu đóng server";
    		log.info(ret);
            break;
		case "reloadjavascript":
			ret = reloadJavaScript(remoteAddress, parameters);
			break;
		case "calljavascript":
			ret = callJavaScript(remoteAddress, parameters);
			break;
		case "exitserver":
			ret = exitServer(remoteAddress, parameters);
			break;
		case "currentserverstate":
			ret = getCurrentServerState();
			break;
		case "test":
			ret = test(parameters);
			break;
		case "reload":
			GameDataManager.getInstance().loadAll();
			ret = test(parameters);
			break;
		default:
			return RouteHttpServerImpl.HttpRet.INVALID.desc();
		}
		log.info("ret: [" + ret + "]");
		return ret;
	}

	/**
	 * 验证http请求的合法性 算法： 1.首先检查发送请求的时间是否在TIMEOUT以内，防止重放攻击 2.做md5验证
	 *
	 * @param parameters
	 * @return
	 */
	private boolean isLegalClient(Map<String, String> parameters) {
		String timestamp = parameters.get("timestamp");
		String sign = parameters.get("sign");

		if (!StringUtils.isEmpty(timestamp) && !StringUtils.isEmpty(sign)) {
			if (Math.abs(System.currentTimeMillis() - Long.parseLong(timestamp)) < TIMEOUT) {
				String text = timestamp + VERIFIEDSEQUENCE;
				String md5 = getMd5(text);
				if (sign.equalsIgnoreCase(md5)) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 重新加载全部java脚本，例如: http://ip:port/?command=reloadjavascript
	 *
	 * @param remoteAddress
	 * @param parameters
	 * @return
	 */
	public String reloadJavaScript(String remoteAddress, HashMap<String, String> parameters) {
		try {
			ScriptJavaLoader.getInstance().loadScript(parameters);
			RouteServer.reloadJavaScriptTime = System.currentTimeMillis();
			return RouteHttpServerImpl.HttpRet.OK.desc();
		} catch (Exception ex) {
			log.error("reloadJavaScript: ", ex);
			return RouteHttpServerImpl.HttpRet.FAILED.desc();
		}
	}
	public String callJavaScript(String remoteAddress, HashMap<String, String> parameters) {
		try {
			ScriptJavaLoader.getInstance().loadScript(parameters);
			RouteServer.reloadJavaScriptTime = System.currentTimeMillis();
			String scriptName = parameters.get("file");
			HashMap<String, String> params = new HashMap<String, String>();
			params.put("file", scriptName);
			log.info("callJavaScript---->>file=" + scriptName);
			ScriptJavaLoader.getInstance().loadScript(params);// 先重载一次
			ScriptManager.getInstance().call(scriptName, parameters);// 再调用
			
			return RouteHttpServerImpl.HttpRet.OK.desc();
		} catch (Exception ex) {
			log.error("reloadJavaScript: ", ex);
			return RouteHttpServerImpl.HttpRet.FAILED.desc();
		}
	}

	private String getCurrentServerState() {
		return ServerService.getInstance().getInfo();
	}

	private String test(HashMap<String, String> params) {
		log.info("test params=" + params.toString());
		GameDataManager.getInstance().loadAll();
		log.info("test params=" + params.toString());
		return RouteHttpServerImpl.HttpRet.OK.desc();
	}

	public static String getMd5(String input) {
		try {
			MessageDigest md5 = MessageDigest.getInstance("MD5");
			byte[] bytes = md5.digest(input.getBytes("UTF-8"));
			StringBuilder ret = new StringBuilder(bytes.length << 1);
			for (int i = 0; i < bytes.length; i++) {
				ret.append(Character.forDigit((bytes[i] >> 4) & 0xf, 16));
				ret.append(Character.forDigit(bytes[i] & 0xf, 16));
			}
			return ret.toString();

		} catch (NoSuchAlgorithmException | UnsupportedEncodingException ex) {
			return "";
		}
	}

	private String exitServer(String remoteAddress, HashMap<String, String> parameters) {
		System.exit(0);
		return RouteHttpServerImpl.HttpRet.OK.desc();
	}

}
