package logic.recharge;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.security.KeyFactory;
import java.security.MessageDigest;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.ParseException;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.MessageLite.Builder;

import Message.S2CRechargeMsg.RechargeGiftOrderNotify;
import Message.S2CRechargeMsg.RechargeGiftOrderNotifyID;
import Message.S2CRechargeMsg.RechargeOrderRsp;
import Message.S2CRechargeMsg.RechargeOrderRspID;
import data.bean.t_rechargeBean;
import game.core.pub.script.IScript;
import game.server.config.ServerConfig;
import game.server.info.ChannelInfoProvider;
import game.server.logic.player.Player;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
import game.server.logic.util.TimeUtils;
import game.server.util.MessageUtils;

/**
 * 支付下单
 *
 * <AUTHOR>
 */
public class CallRechargeParmsScript implements IScript {

    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void init() {

    }

    @Override
    public void destroy() {

    }

    @Override
    public Object call(String scriptName, Object arg) {
        ScriptArgs args = (ScriptArgs) arg;
        Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
        com.google.protobuf.MessageLite.Builder msg = (Builder) args.get(ScriptArgs.Key.ARG3);
        RechargeGiftOrderNotify.Builder builder2 = (Message.S2CRechargeMsg.RechargeGiftOrderNotify.Builder) msg;
        MessageUtils.send(player, player.getFactory().fetchSMessage(
                RechargeGiftOrderNotifyID.RechargeGiftOrderNotifyMsgID_VALUE, builder2.build().toByteArray()));
        logger.info(String.format("玩家[roleId:%s]充值订单生成成功，已发送客户端", player.getPlayerId()));
        return null;
    }
}
