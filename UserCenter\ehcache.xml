﻿<?xml version="1.0" encoding="UTF-8"?>

<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="ehcache.xsd"
         updateCheck="false" monitoring="autodetect"
         dynamicConfig="true">
         
    <!--
    DiskStore configuration
    =======================

    The diskStore element is optional. To turn off disk store path creation, comment out the diskStore
    element below.

    Configure it if you have disk persistence enabled for any cache or if you use
    unclustered indexed search.

    If it is not configured, and a cache is created which requires a disk store, a warning will be
     issued and java.io.tmpdir will automatically be used.

    diskStore has only one attribute - "path". It is the path to the directory where
    any required disk files will be created.

    If the path is one of the following Java System Property it is replaced by its value in the
    running VM. For backward compatibility these should be specified without being enclosed in the ${token}
    replacement syntax.

    The following properties are translated:
    * user.home - User's home directory
    * user.dir - User's current working directory
    * java.io.tmpdir - Default temp file path
    * ehcache.disk.store.dir - A system property you would normally specify on the command line
      e.g. java -Dehcache.disk.store.dir=/u01/myapp/diskdir ...

    Subdirectories can be specified below the property e.g. java.io.tmpdir/one

    -->
    <diskStore path="java.io.tmpdir"/>

    <defaultCache
            maxEntriesLocalHeap="10000"
            eternal="false"
            timeToIdleSeconds="600"
            timeToLiveSeconds="0"
            diskSpoolBufferSizeMB="128"
            maxEntriesLocalDisk="1000000"
            diskExpiryThreadIntervalSeconds="120"
            memoryStoreEvictionPolicy="LRU">
        <persistence strategy="localTempSwap"/>
    </defaultCache>
   
    <!-- 角色列表 -->
    <cache name="roleListCache"
            maxEntriesLocalHeap="20000"
            eternal="false"
            timeToIdleSeconds="1800"
            timeToLiveSeconds="120"
            diskSpoolBufferSizeMB="128"
            maxEntriesLocalDisk="100000"
            diskExpiryThreadIntervalSeconds="120"
            memoryStoreEvictionPolicy="LRU">
    </cache>
    
    <!-- 角色列表 -->
    <cache name="banCache"
            maxEntriesLocalHeap="20000"
            eternal="false"
            timeToIdleSeconds="1800"
            timeToLiveSeconds="0"
            diskSpoolBufferSizeMB="128"
            maxEntriesLocalDisk="100000"
            diskExpiryThreadIntervalSeconds="120"
            memoryStoreEvictionPolicy="LRU">
    </cache>
    
    <!-- 角色列表 -->
    <cache name="gagCache"
            maxEntriesLocalHeap="20000"
            eternal="false"
            timeToIdleSeconds="1800"
            timeToLiveSeconds="0"
            diskSpoolBufferSizeMB="128"
            maxEntriesLocalDisk="100000"
            diskExpiryThreadIntervalSeconds="120"
            memoryStoreEvictionPolicy="LRU">
    </cache>
    
     <!-- 发送活动奖励邮件 -->
    <cache name="sendMailCache"
            maxEntriesLocalHeap="20000"
            eternal="false"
            timeToIdleSeconds="1800"
            timeToLiveSeconds="0"
            diskSpoolBufferSizeMB="128"
            maxEntriesLocalDisk="100000"
            diskExpiryThreadIntervalSeconds="120"
            memoryStoreEvictionPolicy="LRU">
    </cache>
    
</ehcache>
