2024-09-23 16:04:05.0356 [Jetty-QTP-13] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功123456
2024-09-23 16:06:59.0613 [main] INFO  o.s.c.s.ClassPathXmlApplicationContext-[Line:510]>Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@ae45eb6: startup date [Mon Sep 23 16:06:59 CST 2024]; root of context hierarchy
2024-09-23 16:06:59.0660 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [applicationContext.xml]
2024-09-23 16:06:59.0760 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [cache_conf.xml]
2024-09-23 16:06:59.0822 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [datasource.xml]
2024-09-23 16:06:59.0858 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [annotation.xml]
2024-09-23 16:06:59.0913 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [quartz.xml]
2024-09-23 16:06:59.0970 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [servlet-context.xml]
2024-09-23 16:07:00.0457 [main] INFO  o.s.b.f.c.PropertyPlaceholderConfigurer-[Line:172]>Loading properties file from class path resource [config.properties]
2024-09-23 16:07:00.0562 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c: defining beans [propertyConfigurer,jettyRunner,CIDRSetting,org.springframework.aop.config.internalAutoProxyCreator,org.springframework.cache.annotation.AnnotationCacheOperationSource#0,org.springframework.cache.interceptor.CacheInterceptor#0,org.springframework.cache.config.internalCacheAdvisor,cacheManagerFactory,cacheManager,dataSource,daoTemplate,serverInfoDao,serverStatusDao,serverInfoManager,rebateInfoDao,rebateInfoManager,activationCreateInfoDao,activationCreateManager,activationInfoDao,activationManager,dailyDao,dailyInfoUpdateQueue,serverConfigureDao,serverConfigureManager,serverZoneDao,serverZoneManager,serverLoginWhiteDao,serverLoginWhiteManager,serverSwitchDao,serverSwitchManager,channelInfoDao,channelInfoManager,channelSwitchDao,channelSwitchManager,ServerWhiteIpManager,serverWhiteIpDao,accountDao,accountProvider,roleDao,accessTokenManager,roleInfoUpdateQueue,banDao,banManager,gagDao,gagManager,activityMailDao,sendMailRecordDao,activityMailQueue,sendMailDBQueue,activityMailLoader,equipmentCodeDao,rechargeDao,roleRegisterDao,openServerDao,openServerDaily,openIPDao,ChatDao,YYPlatformRebateDao,YYPlatformRebateManager,YYPlatformGiftDao,org.springframework.context.annotation.internalConfigurationAnnotationProcessor,org.springframework.context.annotation.internalAutowiredAnnotationProcessor,org.springframework.context.annotation.internalRequiredAnnotationProcessor,org.springframework.context.annotation.internalCommonAnnotationProcessor,quartzScheduler,accountDBDetail,accountDBTrigger,serverWhiteIpDBDetail,serverWhiteIpDBTrigger,accountReleaseDetail,accountReleaseTrigger,serverUpdateDetail,serverUpdateTrigger,serverConfigureUpdateDetail,serverConfigureUpdateTrigger,serverZoneUpdateDetail,serverZoneUpdateTrigger,serverLoginWhiteUpdateDetail,serverLoginWhiteUpdateTrigger,serverSwitchUpdateDetail,serverSwitchUpdateTrigger,channelSwitchUpdateDetail,channelSwitchUpdateTrigger,accessTokenDetail,accessTokenTrigger,roleDBDetail,roleDBTrigger,banDBDetail,banDBTrigger,gagDBDetail,gagDBTrigger,dailyDBDetail,dailyDBTrigger,openServerDBDetail,openServerDBTrigger,accountCache,sendMailCache,banCache,gagCache,roleListCache,activationCreateHandler,banHandler,batchCreateRoleHandler,channelListHandler,channelSwitchHandler,createRoleHandler,debanHandler,degagHandler,gagHandler,getAllRebateListHandler,getAllServerListHandler,getAllYYPlatformRebateListHandler,getServerConfigInfoHandler,getServerConfigureHandler,getServerListHandler,getStrideServerHandler,loginYGSDKHandle,logonWithoutSDKHandler,queryBanHandler,queryRoleInfoHandler,queryX7IOSRoleInfoHandler,queryX7RoleInfoHandler,rechargeCallBackHandler,rechargeHandler,roleCreateLogHandler,sendMailHandler,serverLoginWhiteHandler,serverSwitchHandler,serverZoneHandler,YMNotifyHandler,gameCloseServlet,org.springframework.context.annotation.ConfigurationClassPostProcessor.importAwareProcessor]; root of factory hierarchy
2024-09-23 16:07:00.0588 [main] INFO  org.eclipse.jetty.util.log-[Line:186]>Logging initialized @1421ms
2024-09-23 16:07:00.0717 [main] INFO  o.s.w.c.s.XmlWebApplicationContext-[Line:510]>Refreshing Root WebApplicationContext: startup date [Mon Sep 23 16:07:00 CST 2024]; parent: org.springframework.context.support.ClassPathXmlApplicationContext@ae45eb6
2024-09-23 16:07:00.0730 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@7cbd9d24: defining beans []; parent: org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c
2024-09-23 16:07:00.0737 [main] INFO  o.s.w.c.s.XmlWebApplicationContext-[Line:510]>Refreshing Root WebApplicationContext: startup date [Mon Sep 23 16:07:00 CST 2024]; parent: org.springframework.context.support.ClassPathXmlApplicationContext@ae45eb6
2024-09-23 16:07:00.0738 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:444]>Destroying singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@7cbd9d24: defining beans []; parent: org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c
2024-09-23 16:07:00.0741 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@6d07a63d: defining beans []; parent: org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c
2024-09-23 16:07:00.0767 [Jetty-Server] INFO  org.eclipse.jetty.server.Server-[Line:327]>jetty-9.2.10.v20150310
2024-09-23 16:07:00.0796 [Jetty-Server] INFO  /-[Line:2052]>Initializing Spring FrameworkServlet 'baseServlet'
2024-09-23 16:07:00.0796 [Jetty-Server] INFO  o.s.web.servlet.DispatcherServlet-[Line:454]>FrameworkServlet 'baseServlet': initialization started
2024-09-23 16:07:00.0798 [Jetty-Server] INFO  o.s.w.c.s.XmlWebApplicationContext-[Line:510]>Refreshing WebApplicationContext for namespace 'baseServlet-servlet': startup date [Mon Sep 23 16:07:00 CST 2024]; root of context hierarchy
2024-09-23 16:07:00.0799 [Jetty-Server] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [servlet-context.xml]
2024-09-23 16:07:00.0900 [main] INFO  o.s.c.e.EhCacheManagerFactoryBean-[Line:110]>Initializing EhCache CacheManager
2024-09-23 16:07:00.0958 [Jetty-Server] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@7439476d: defining beans [org.springframework.context.annotation.internalConfigurationAnnotationProcessor,org.springframework.context.annotation.internalAutowiredAnnotationProcessor,org.springframework.context.annotation.internalRequiredAnnotationProcessor,org.springframework.context.annotation.internalCommonAnnotationProcessor,accountCache,sendMailCache,banCache,gagCache,roleListCache,activationCreateHandler,banHandler,batchCreateRoleHandler,channelListHandler,channelSwitchHandler,createRoleHandler,debanHandler,degagHandler,gagHandler,getAllRebateListHandler,getAllServerListHandler,getAllYYPlatformRebateListHandler,getServerConfigInfoHandler,getServerConfigureHandler,getServerListHandler,getStrideServerHandler,loginYGSDKHandle,logonWithoutSDKHandler,queryBanHandler,queryRoleInfoHandler,queryX7IOSRoleInfoHandler,queryX7RoleInfoHandler,rechargeCallBackHandler,rechargeHandler,roleCreateLogHandler,sendMailHandler,serverLoginWhiteHandler,serverSwitchHandler,serverZoneHandler,YMNotifyHandler,gameCloseServlet,org.springframework.context.annotation.ConfigurationClassPostProcessor.importAwareProcessor]; root of factory hierarchy
2024-09-23 16:07:01.0134 [Jetty-Server] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ChannelInfoManager into StaticDataManager
2024-09-23 16:07:01.0172 [Jetty-Server] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerInfoManagerImpl into StaticDataManager
2024-09-23 16:07:01.0174 [Jetty-Server] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerZoneManager into StaticDataManager
2024-09-23 16:07:01.0293 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addActivationCreate] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0294 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addActivationCreate.*] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0294 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addActivationCreate/] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0294 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteActivationCreate] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0295 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteActivationCreate.*] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0295 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteActivationCreate/] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0295 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getActivationCreate] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0295 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getActivationCreate.*] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0296 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getActivationCreate/] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0296 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/RefreshActivation] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0296 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/RefreshActivation.*] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0296 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/RefreshActivation/] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0297 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/useCode] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0297 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/useCode.*] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0297 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/useCode/] onto handler 'activationCreateHandler'
2024-09-23 16:07:01.0298 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/banAdd] onto handler 'banHandler'
2024-09-23 16:07:01.0298 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/banAdd.*] onto handler 'banHandler'
2024-09-23 16:07:01.0298 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/banAdd/] onto handler 'banHandler'
2024-09-23 16:07:01.0299 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/query.do] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0311 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchCreateRole] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0312 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchCreateRole.*] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0312 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchCreateRole/] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0313 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/transpond.do] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0313 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/share.do] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0313 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchUpdata.do] onto handler 'batchCreateRoleHandler'
2024-09-23 16:07:01.0313 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/add.do] onto handler 'channelListHandler'
2024-09-23 16:07:01.0314 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/delete.do] onto handler 'channelListHandler'
2024-09-23 16:07:01.0314 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/getserverlist] onto handler 'channelListHandler'
2024-09-23 16:07:01.0314 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/getserverlist.*] onto handler 'channelListHandler'
2024-09-23 16:07:01.0314 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/getserverlist/] onto handler 'channelListHandler'
2024-09-23 16:07:01.0315 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/update.do] onto handler 'channelSwitchHandler'
2024-09-23 16:07:01.0315 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/getserverlist] onto handler 'channelSwitchHandler'
2024-09-23 16:07:01.0315 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/getserverlist.*] onto handler 'channelSwitchHandler'
2024-09-23 16:07:01.0316 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/getserverlist/] onto handler 'channelSwitchHandler'
2024-09-23 16:07:01.0316 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/createRole] onto handler 'createRoleHandler'
2024-09-23 16:07:01.0316 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/createRole.*] onto handler 'createRoleHandler'
2024-09-23 16:07:01.0316 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/createRole/] onto handler 'createRoleHandler'
2024-09-23 16:07:01.0317 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/deletban] onto handler 'debanHandler'
2024-09-23 16:07:01.0317 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/deletban.*] onto handler 'debanHandler'
2024-09-23 16:07:01.0317 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/deletban/] onto handler 'debanHandler'
2024-09-23 16:07:01.0318 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/DelGAG] onto handler 'degagHandler'
2024-09-23 16:07:01.0318 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/DelGAG.*] onto handler 'degagHandler'
2024-09-23 16:07:01.0318 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/DelGAG/] onto handler 'degagHandler'
2024-09-23 16:07:01.0318 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/GAG] onto handler 'gagHandler'
2024-09-23 16:07:01.0319 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/GAG.*] onto handler 'gagHandler'
2024-09-23 16:07:01.0319 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/GAG/] onto handler 'gagHandler'
2024-09-23 16:07:01.0319 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/getGAG] onto handler 'gagHandler'
2024-09-23 16:07:01.0319 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/getGAG.*] onto handler 'gagHandler'
2024-09-23 16:07:01.0319 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/getGAG/] onto handler 'gagHandler'
2024-09-23 16:07:01.0320 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addRebate] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0320 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addRebate.*] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0320 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addRebate/] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0320 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteRebate] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0321 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteRebate.*] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0321 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteRebate/] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0321 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getRebatelist] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0321 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getRebatelist.*] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0322 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getRebatelist/] onto handler 'getAllRebateListHandler'
2024-09-23 16:07:01.0325 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addserverlist] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0326 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addserverlist.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0326 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addserverlist/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0326 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/update.do] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0326 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteserver] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0326 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteserver.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0327 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteserver/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0327 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reNameServer] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0327 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reNameServer.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0327 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reNameServer/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0327 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getserverlist] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0328 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getserverlist.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0328 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getserverlist/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0328 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reCombineServer] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0328 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reCombineServer.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0329 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reCombineServer/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0329 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/refres_server.do] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0329 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getopenserver] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0329 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getopenserver.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0329 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getopenserver/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0330 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addopenlist] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0330 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addopenlist.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0330 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addopenlist/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0330 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/chat/getlist] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0330 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/chat/getlist.*] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0331 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/chat/getlist/] onto handler 'getAllServerListHandler'
2024-09-23 16:07:01.0331 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addYYPlatformGiftRebate] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0331 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addYYPlatformGiftRebate.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0332 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addYYPlatformGiftRebate/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0332 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteYYPlatformGiftRebate] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0332 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteYYPlatformGiftRebate.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0332 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteYYPlatformGiftRebate/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0332 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatformGiftRebatelist] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0333 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatformGiftRebatelist.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0333 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatformGiftRebatelist/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0333 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/doYYGift] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0333 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/doYYGift.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0334 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/doYYGift/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0334 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatGift] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0334 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatGift.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0334 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatGift/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 16:07:01.0335 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/center/ServerInfo] onto handler 'getServerConfigInfoHandler'
2024-09-23 16:07:01.0338 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/center/ServerInfo.*] onto handler 'getServerConfigInfoHandler'
2024-09-23 16:07:01.0339 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/center/ServerInfo/] onto handler 'getServerConfigInfoHandler'
2024-09-23 16:07:01.0339 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/configure/add.do] onto handler 'getServerConfigureHandler'
2024-09-23 16:07:01.0339 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/configure/update.do] onto handler 'getServerConfigureHandler'
2024-09-23 16:07:01.0340 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/configure/list.do] onto handler 'getServerConfigureHandler'
2024-09-23 16:07:01.0340 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getServerInfoById] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0340 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getServerInfoById.*] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0340 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getServerInfoById/] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0341 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/updatestatus.do] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0341 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/serverlist] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0341 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/serverlist.*] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0341 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/serverlist/] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0342 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getstatus.do] onto handler 'getServerListHandler'
2024-09-23 16:07:01.0342 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/get_stride_server.do] onto handler 'getStrideServerHandler'
2024-09-23 16:07:01.0343 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginCheck] onto handler 'loginYGSDKHandle'
2024-09-23 16:07:01.0343 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginCheck.*] onto handler 'loginYGSDKHandle'
2024-09-23 16:07:01.0343 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginCheck/] onto handler 'loginYGSDKHandle'
2024-09-23 16:07:01.0343 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginOutCheck] onto handler 'loginYGSDKHandle'
2024-09-23 16:07:01.0344 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginOutCheck.*] onto handler 'loginYGSDKHandle'
2024-09-23 16:07:01.0344 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginOutCheck/] onto handler 'loginYGSDKHandle'
2024-09-23 16:07:01.0344 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginWithOutSDK] onto handler 'logonWithoutSDKHandler'
2024-09-23 16:07:01.0344 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginWithOutSDK.*] onto handler 'logonWithoutSDKHandler'
2024-09-23 16:07:01.0345 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginWithOutSDK/] onto handler 'logonWithoutSDKHandler'
2024-09-23 16:07:01.0345 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/query/account/getbanInfo] onto handler 'queryBanHandler'
2024-09-23 16:07:01.0345 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/query/account/getbanInfo.*] onto handler 'queryBanHandler'
2024-09-23 16:07:01.0345 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/query/account/getbanInfo/] onto handler 'queryBanHandler'
2024-09-23 16:07:01.0346 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/operate/query-role.do] onto handler 'queryRoleInfoHandler'
2024-09-23 16:07:01.0346 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/operate/query-role-x7ios.do] onto handler 'queryX7IOSRoleInfoHandler'
2024-09-23 16:07:01.0347 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/operate/query-role-x7.do] onto handler 'queryX7RoleInfoHandler'
2024-09-23 16:07:01.0347 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/qkPlatfromCallback] onto handler 'rechargeCallBackHandler'
2024-09-23 16:07:01.0347 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/qkPlatfromCallback.*] onto handler 'rechargeCallBackHandler'
2024-09-23 16:07:01.0348 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/qkPlatfromCallback/] onto handler 'rechargeCallBackHandler'
2024-09-23 16:07:01.0348 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeUpdate] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0348 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeUpdate.*] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0348 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeUpdate/] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0349 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeAdd] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0349 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeAdd.*] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0385 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeAdd/] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0385 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeFind] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0386 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeFind.*] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0386 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeFind/] onto handler 'rechargeHandler'
2024-09-23 16:07:01.0386 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleAdd] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0387 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleAdd.*] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0387 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleAdd/] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0387 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleLogin] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0387 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleLogin.*] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0387 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleLogin/] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0388 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/daily/getlist] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0388 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/daily/getlist.*] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0388 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/daily/getlist/] onto handler 'roleCreateLogHandler'
2024-09-23 16:07:01.0389 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/sendMail.do] onto handler 'sendMailHandler'
2024-09-23 16:07:01.0389 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteAdd] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0389 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteAdd.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0389 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteAdd/] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0390 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteDelete] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0390 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteDelete.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0390 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteDelete/] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0390 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/existsN] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0390 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/existsN.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0391 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/existsN/] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0391 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/getserverlist] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0391 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/getserverlist.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0391 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/getserverlist/] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0392 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteList] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0392 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteList.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0392 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteList/] onto handler 'serverLoginWhiteHandler'
2024-09-23 16:07:01.0392 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/updateserverstatus] onto handler 'serverSwitchHandler'
2024-09-23 16:07:01.0393 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/updateserverstatus.*] onto handler 'serverSwitchHandler'
2024-09-23 16:07:01.0393 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/updateserverstatus/] onto handler 'serverSwitchHandler'
2024-09-23 16:07:01.0393 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/getserverlist] onto handler 'serverSwitchHandler'
2024-09-23 16:07:01.0393 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/getserverlist.*] onto handler 'serverSwitchHandler'
2024-09-23 16:07:01.0393 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/getserverlist/] onto handler 'serverSwitchHandler'
2024-09-23 16:07:01.0394 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/add.do] onto handler 'serverZoneHandler'
2024-09-23 16:07:01.0394 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/update.do] onto handler 'serverZoneHandler'
2024-09-23 16:07:01.0394 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/getserverlist] onto handler 'serverZoneHandler'
2024-09-23 16:07:01.0394 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/getserverlist.*] onto handler 'serverZoneHandler'
2024-09-23 16:07:01.0395 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/getserverlist/] onto handler 'serverZoneHandler'
2024-09-23 16:07:01.0403 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/notify_ym_sdk.do] onto handler 'YMNotifyHandler'
2024-09-23 16:07:01.0404 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/closeGame] onto handler 'gameCloseServlet'
2024-09-23 16:07:01.0404 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/closeGame.*] onto handler 'gameCloseServlet'
2024-09-23 16:07:01.0404 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/closeGame/] onto handler 'gameCloseServlet'
2024-09-23 16:07:01.0815 [Jetty-Server] INFO  o.s.web.servlet.DispatcherServlet-[Line:473]>FrameworkServlet 'baseServlet': initialization completed in 1019 ms
2024-09-23 16:07:01.0815 [Jetty-Server] INFO  o.e.j.server.handler.ContextHandler-[Line:744]>Started o.e.j.s.ServletContextHandler@1207b30a{/,null,AVAILABLE}
2024-09-23 16:07:01.0828 [Jetty-Server] INFO  o.e.jetty.server.ServerConnector-[Line:266]>Started ServerConnector@182a7aa6{HTTP/1.1}{0.0.0.0:8501}
2024-09-23 16:07:01.0829 [Jetty-Server] INFO  org.eclipse.jetty.server.Server-[Line:379]>Started @2663ms
2024-09-23 16:07:02.0481 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.RebateInfoManagerImpl into StaticDataManager
2024-09-23 16:07:02.0489 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ActivationCreateManagerImpl into StaticDataManager
2024-09-23 16:07:02.0497 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ActivationInfoManagerImpl into StaticDataManager
2024-09-23 16:07:02.0511 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.DailyInfoUpdateQueue
2024-09-23 16:07:02.0519 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerConfigureManager into StaticDataManager
2024-09-23 16:07:02.0605 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerWhiteIpManager into StaticDataManager
2024-09-23 16:07:02.0617 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.AccountDBQueue
2024-09-23 16:07:02.0630 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.RoleInfoUpdateQueue
2024-09-23 16:07:02.0646 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mbean.BanDBQueue
2024-09-23 16:07:02.0660 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mbean.GagDBQueue
2024-09-23 16:07:02.0678 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mail.ActivityMailQueue
2024-09-23 16:07:02.0692 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mail.SendMailDBQueue
2024-09-23 16:07:02.0747 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.auth.yyplatform.YYPlatformRebateManagerImpl into StaticDataManager
2024-09-23 16:07:02.0821 [main] INFO  o.s.s.quartz.SchedulerFactoryBean-[Line:552]>Loading Quartz config from [class path resource [quartz.properties]]
2024-09-23 16:07:02.0835 [main] INFO  org.quartz.impl.StdSchedulerFactory-[Line:1184]>Using default implementation for ThreadExecutor
2024-09-23 16:07:02.0847 [main] INFO  o.quartz.core.SchedulerSignalerImpl-[Line:61]>Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-09-23 16:07:02.0848 [main] INFO  org.quartz.core.QuartzScheduler-[Line:240]>Quartz Scheduler v.2.2.1 created.
2024-09-23 16:07:02.0849 [main] INFO  org.quartz.simpl.RAMJobStore-[Line:155]>RAMJobStore initialized.
2024-09-23 16:07:02.0850 [main] INFO  org.quartz.core.QuartzScheduler-[Line:305]>Scheduler meta-data: Quartz Scheduler (v2.2.1) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 2 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2024-09-23 16:07:02.0851 [main] INFO  org.quartz.impl.StdSchedulerFactory-[Line:1339]>Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2024-09-23 16:07:02.0851 [main] INFO  org.quartz.impl.StdSchedulerFactory-[Line:1343]>Quartz scheduler version: 2.2.1
2024-09-23 16:07:02.0852 [main] INFO  org.quartz.core.QuartzScheduler-[Line:2311]>JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5aa360ea
2024-09-23 16:07:02.0893 [main] INFO  o.s.c.s.DefaultLifecycleProcessor-[Line:334]>Starting beans in phase 2147483647
2024-09-23 16:07:02.0894 [main] INFO  o.s.s.quartz.SchedulerFactoryBean-[Line:648]>Starting Quartz Scheduler now
2024-09-23 16:07:02.0894 [main] INFO  org.quartz.core.QuartzScheduler-[Line:575]>Scheduler quartzScheduler_$_NON_CLUSTERED started.
2024-09-23 16:07:02.0894 [main] INFO  c.p.http.jetty.SpringContextLoader-[Line:70]>SERVER START COMPLETE..... 
2024-09-23 16:09:02.0419 [Jetty-QTP-25] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功123456
