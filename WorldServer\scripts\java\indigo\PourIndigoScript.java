package indigo;

import org.apache.commons.lang.StringUtils;

import Message.S2CBackpackMsg.PropInfo;
import Message.S2CPlayerMsg.PromptType;
import Message.Inner.GRCrossIndigo.GRPourIndigoRsp;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.BePouredInfo;
import game.route.indigo.bean.PourOrder;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
import game.util.BeanTemplet;
/**
 * 申请下注
 * <AUTHOR>
 *
 */
public class PourIndigoScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		long playerId = (long) args.get(Key.ARG1);
		GameServer server = (GameServer) args.get(Key.ARG2);
		long pourId = (long) args.get(Key.ARG3);
		int type = (int) args.get(Key.ARG4);
		pourIndigo(playerId, pourId, type, server);
		return null;
	}

	/**
	 * 下注
	 * 
	 * @param player
	 * @param pourId
	 * @param type
	 *            1：普通 2： 高级
	 */
	private void pourIndigo(long playerId, long pourId, int type, GameServer server) {
		IndigoService indigoService = IndigoService.getInstance();
		if (indigoService.getPourMap().containsKey(playerId)) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 292);// 重复下注
			return;
		}
		Integer groupId = ServerService.getInstance().getGroupIdByServer(server.getId());
		BePouredInfo pouredInfo = indigoService.getPouredInfo(pourId, groupId);
		if (pouredInfo == null) {
			GRMessageUtils.sendPrompt(server.getId(), playerId, PromptType.WARNING, 293);// 下注目标未找到
			return;
		}
		// 可以下注
		// 生成订单
		PourOrder order = new PourOrder(playerId, pourId, type);
		indigoService.getPourOrderMap().put(playerId, order);
		// 通知GameServer
		GRPourIndigoRsp.Builder builder = GRPourIndigoRsp.newBuilder();
		builder.setPourOrderId(order.getOrderId());
		builder.setPlayerId(playerId);
		builder.setType(type);
		builder.setPouredId(pourId);
		// 下注消耗
		int pourType = type > 2 ? 2 : type;
		pourType = pourType < 1 ? 1 : pourType;
		int num = Integer.parseInt(StringUtils.split(BeanTemplet.getGlobalBean(210).getStr_value(), ",")[pourType - 1]);
		PropInfo.Builder b = PropInfo.newBuilder();
		b.setId(-1);
		b.setNum(num);
		builder.addItems(b);
		GRMessageUtils.sendMsg2GameServer(server.getId(),
				indigoService.genGRIndigoRsp(playerId, GRPourIndigoRsp.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
