package logic.crossArena;

import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.CrossArenaReportsRsp;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;

import data.bean.t_arena_integralBean;
import data.bean.t_cross_arena_integralBean;
import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.mail.MailService;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import Message.S2CPlayerMsg.PromptType;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRACrossDayScoreRsp;
import Message.Inner.GRCrossArena.GRAScoreRewardMsg;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CrossDayScoreRspScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRACrossDayScoreRsp getData = (GRACrossDayScoreRsp) args.get(ScriptArgs.Key.ARG1);
		for(GRAScoreRewardMsg scoreRewardMsg:getData.getScoreRewardList())
		{
			sendUnReceiveScoreReward(scoreRewardMsg.getPlayerId(),scoreRewardMsg.getScoreRewardList(),scoreRewardMsg.getArenaScore());
		}
		return null;
	}

	private void sendUnReceiveScoreReward(long playerId, List<Integer> scoreReward, int arenaScore) {
		int id = 1;
		List<Integer> gscoreReward = new ArrayList<>();
		gscoreReward.addAll(scoreReward);
		while (true) {
			if (gscoreReward.contains(id)) {// 已领取
				id++;
				continue;
			}
			t_cross_arena_integralBean integralBean = BeanTemplet.getCrossArenaIntegralBean(id);
			if (integralBean == null || integralBean.getIntegral() > arenaScore) {
				break;
			}
			gscoreReward.add(id);
			id++;
			// 获取奖励
			Map<Integer, Integer> adjunctMap = new HashMap<>();// 附件(奖励列表)
			String[] drops = StringUtils.split(integralBean.getDrop(),";");
			String[] drop;
			for (String d : drops) {
				drop = StringUtils.split(d,",");
				adjunctMap.put(Integer.parseInt(drop[0]), Integer.parseInt(drop[1]));
			}
			// 发送给玩家
			if (!adjunctMap.isEmpty()) {
				MailService.getInstance().sendSysMail2Player(playerId, 130, null,
						Reason.ARENA_SCORE_REWARD, "", null, System.currentTimeMillis(), adjunctMap);
			}
		}
	}
}
