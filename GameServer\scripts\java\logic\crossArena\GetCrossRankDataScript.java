package logic.crossArena;

import Message.Inner.GRCrossArena;
import Message.S2CArenaMsg;
import Message.S2CArenaMsg.RankingInfo;
import Message.S2CCrossArenaMsg.CrossEnemyMsg;
import Message.S2CCrossArenaMsg;
import Message.S2CHeroMsg;
import Message.S2CPlayerMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import game.core.pub.script.IScript;
import game.server.logic.player.Player;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRAGetCrossRankRsp;
import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.S2CCrossArenaMsg.GetCrossArenaRankRsp;

public class GetCrossRankDataScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRAGetCrossRankRsp getData = (GRAGetCrossRankRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		GetCrossArenaRankRsp.Builder builder = GetCrossArenaRankRsp.newBuilder();
		for(ByteString byteString:getData.getEnemyListList()) {
			try {
				CrossEnemyMsg enemyMsg = CrossEnemyMsg.parseFrom(byteString);
				RankingInfo.Builder rankingInfo = RankingInfo.newBuilder();
				rankingInfo.setRank(enemyMsg.getRank());
				rankingInfo.setPlayerId(enemyMsg.getPlayerId());
				rankingInfo.setName(enemyMsg.getName());
				rankingInfo.setImg(enemyMsg.getImg());
				rankingInfo.setLevel(enemyMsg.getLevel());
				rankingInfo.setPower(enemyMsg.getPower());
				rankingInfo.setSectName(enemyMsg.getSectName());
				rankingInfo.setVip(enemyMsg.getVip());
				rankingInfo.addAllFormation(enemyMsg.getFormationList());
				rankingInfo.setRobot(enemyMsg.getRobot());
				rankingInfo.setViewCard(enemyMsg.getViewCard());
				rankingInfo.setTrainLvl(enemyMsg.getTrainLvl());
				builder.addRank(rankingInfo);

			} catch (Exception ex) {

			}
		}


		// 推送
		MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.GetCrossArenaRankRspID.GetCrossArenaRankRspMsgID_VALUE,
				builder.build().toByteArray()));
		return null;
	}

}
