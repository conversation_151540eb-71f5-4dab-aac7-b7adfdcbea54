<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration
        PUBLIC "-//ibatis.apache.org//DTD Config 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="defaultStatementTimeout" value="60" />
	</settings>

	<environments default="development">
		<environment id="development">
			<transactionManager type="JDBC" />
			<dataSource type="POOLED">
				<property name="driver" value="com.mysql.jdbc.Driver" />
				<property name="url" value="${url}" />
				<property name="username" value="${username}" />
				<property name="password" value="${password}" />
				<!-- sql select -->
				<property name="poolPingQuery" value="select 1" />
				<!-- is ping -->
				<property name="poolPingEnabled" value="true" />
				<!-- ping time millisecond -->
				<property name="poolPingConnectionsNotUsedFor" value="300000" />
				<property name="poolMaximumActiveConnections" value="40" />
				<property name="poolMaximumIdleConnections" value="20" />
				<property name="poolMaximumCheckoutTime" value="60000" />
			</dataSource>
		</environment>
	</environments>
	<mappers>
		<mapper resource="game/route/server/db/InnerServer.xml" />
		<mapper resource="game/route/global/db/Global.xml" />
		<mapper resource="game/route/db/sqlmap/FightReportMap.xml" />
		<mapper resource="game/route/db/sqlmap/IndigoDataMap.xml" />
		<mapper resource="game/route/db/sqlmap/IndigoPartiMap.xml" />
		<mapper resource="game/route/db/sqlmap/IndigoRankMap.xml" />	
		<mapper resource="game/route/db/sqlmap/PourInfoMap.xml" />		
		<mapper resource="game/route/db/sqlmap/IndigoGuildRankMap.xml" />	
		<mapper resource="game/route/db/sqlmap/ArenaDataMap.xml" />	
	</mappers>

</configuration> 