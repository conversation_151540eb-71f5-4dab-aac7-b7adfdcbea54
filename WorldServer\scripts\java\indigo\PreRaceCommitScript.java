package indigo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.google.protobuf.InvalidProtocolBufferException;

import Message.S2CIndigoMsg.SingleRankMsg;
import Message.Inner.GRCrossIndigo.PreRaceCommit;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.server.ServerService;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;

/**
 * 提交初赛排行
 * <AUTHOR>
 *
 */
public class PreRaceCommitScript implements IScript {
	private final Logger logger = Logger.getLogger(PreRaceCommitScript.class);

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		IndigoService indigoService = IndigoService.getInstance();
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		PreRaceCommit req = (PreRaceCommit) args.get(Key.ARG2);

		if (!indigoService.isFinalQualificationFlag()) {
			logger.error("----------------GameServer提交跨服三国霸主初赛排行时间错误----------------- : " + serverId);
			logger.error("当前时间为:" + System.currentTimeMillis());
			return null;
		}

        List<SingleRankMsg> l = new ArrayList<>();
        SingleRankMsg msg;
        try {
	        for (int i = 0; i < req.getRanksCount(); i++) {
				msg = SingleRankMsg.parseFrom(req.getRanks(i).toByteArray());
				l.add(msg);
			}
	        Map<Integer, List<SingleRankMsg>> map = indigoService.getPreRaceRank().get(groupId);
	        if (null == map) {
	        	map = new HashMap<>();
	        	indigoService.getPreRaceRank().put(groupId, map);
	        }
	        map.put(serverId, l);
        } catch (InvalidProtocolBufferException e) {
        	logger.error("----------------GameServer提交跨服三国霸主初赛排行异常-----------------");
        	logger.error(e);
        	return null;
        }
		return null;
	}

}
