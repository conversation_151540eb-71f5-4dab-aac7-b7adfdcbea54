package logic.hefu;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import game.server.util.IdGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import Message.S2CPlayerMsg.PlayerType;
import game.core.pub.script.IScript;
import game.server.db.game.bean.ArenaRankingBean;
import game.server.db.game.bean.PlayerBean;
import game.server.db.game.dao.ArenaRankingDao;
import game.server.logic.arena.ArenaProcessorManager;
import game.server.logic.guild.GuildService;
import game.server.logic.guild.bean.Guild;
import game.server.logic.line.handler.PlayerUpdateBean;
import game.server.logic.line.handler.ReqUpdateRoleBatchHandler;
import game.server.logic.player.Player;
import game.server.logic.player.PlayerManager;
import game.server.logic.player.RoleView;
import game.server.logic.player.RoleViewService;
import game.server.thread.PlayerRestoreProcessor;

/**
 * 合服 检查重名脚本
 */
public class CheckDuplicationScript implements IScript {

	private Logger logger = LoggerFactory.getLogger(CheckDuplicationScript.class);

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {

		logger.info("-------[ Bắt đầu kiểm tra trùng tên khi hợp server  ] -------");
		List<RoleView> roles = RoleViewService.getAllRoleView();
		Map<String, Long> nameIds = new HashMap<>(); // Tên và ID người chơi
		logger.info("-------[ Số lượng người chơi ：" + roles.size() + "  ] -------");
		List<PlayerUpdateBean> list = new ArrayList<>();
		for (RoleView roleView : roles) {
			if (null == roleView)
				continue;
			// logger.info("-------[ roleView.isRobot() ：" + roleView.isRobot()
			// + "] -------");
			// if (roleView.isRobot())
			// continue;
			String name = roleView.getName();
			long playerId = roleView.getPlayerId();
			//if (playerId < Integer.MAX_VALUE)
			//	continue;
			Player player = PlayerManager.getOffLinePlayerByPlayerId(playerId);
			if (null == player || player.getPlayerType() != PlayerType.PLAYER_VALUE) {
				continue;
			}

			logger.info("-------[ id : " + playerId + " , Người chơi ：" + name + " bắt đầu dọn dẹp dữ liệu xếp hạng võ đường và nhiệm vụ đạo quán ] -------");
			player.getArenaManager().setCurRank(0);
			player.getArenaManager().getChallengeList().clear();
			player.getTaskManager().getPublicTaskFinished().clear();// Xóa nhiệm vụ

			// --------  Reset con đường vô địch - Bí cảnh thần linh
			//player.getChampionManager().setTodayFloor(0);
			//player.getChampionManager().setTodayFightCount(0);
			//player.getChampionManager().setTodayBuyCount(0);
			//player.getChampionManager().setHasOneKeyFight(false);
			//player.getChampionManager().setHasPassReward(false);
			//player.getChampionManager().setHasFirstPassReward(false);
			//player.getChampionManager().setLastFightTime(0);
			//player.getChampionManager().setMaxFloor(0);
			// --------  Reset con đường vô địch
			

			if (nameIds.containsKey(name)) {
				long checkPlayerId = nameIds.get(name);
				logger.info("-------[ Tên người chơi : " + name + " bị trùng  , ID người chơi 1：" + checkPlayerId + " , ID người chơi 2: " + playerId
						+ " ] -------");
				Player checkPlayer = PlayerManager.getOffLinePlayerByPlayerId(checkPlayerId);
				if (null != checkPlayer && null != player) {
					// Người chơi có cấp độ thấp hơn sẽ được thêm hậu tố server vào tên
					// Nếu cấp độ người chơi bằng nhau, so sánh kinh nghiệm hiện tại, nếu kinh nghiệm cũng bằng nhau thì nhân vật mở server muộn hơn sẽ bị đổi tên
					// Người chơi bị đổi tên sẽ tự động nhận được một thẻ đổi tên
					boolean change = false;// false đổi tên người chơi 1, true đổi tên người chơi 2
					if (checkPlayer.getLevel() > player.getLevel()) {
						change = true;
						logger.info("-------[  Người chơi 2 id: " + playerId + " , cấp độ thấp hơn nên bị đổi tên ！] -------");
					} else if (checkPlayer.getLevel() == player.getLevel()) {
						if (checkPlayer.getHeroManager().getLeaderExp() > player.getHeroManager().getLeaderExp()) {
							change = true;
							logger.info("-------[  Người chơi 2 id: " + playerId + " , kinh nghiệm ít hơn nên bị đổi tên ！] -------");
						} else if (checkPlayer.getHeroManager().getLeaderExp() == player.getHeroManager()
								.getLeaderExp()) {
							if (checkPlayer.getCurrentServer() > player.getCreateServer()) {
								change = true;
								logger.info("-------[  Người chơi 2 id: " + playerId + " , server mở muộn hơn nên bị đổi tên ！] -------");
							} else {
								change = false;
								logger.info("-------[  Người chơi 1 id: " + checkPlayerId + " , server mở muộn hơn nên bị đổi tên ！] -------");
							}
						} else {
							change = false;
							logger.info("-------[  Người chơi 1 id: " + checkPlayerId + " , kinh nghiệm ít hơn nên bị đổi tên ！] -------");
						}
					} else {
						change = false;
						logger.info("-------[  Người chơi 1 id: " + checkPlayerId + " , cấp độ thấp hơn nên bị đổi tên ！] -------");
					}
					if (change) {
						logger.info("-------[  Người chơi 2 id: " + playerId + " , bắt đầu đổi tên ！] -------");
						String newName = name + "S" + (player.getCreateServer() % 1000);
						logger.info("-------[  Tên mới của người chơi : " + newName + " ! ] -------");
						player.setPlayerName(newName);
						player.setModifyNameCount(0); // Reset số lần đổi tên
						nameIds.put(newName, playerId);
					} else {
						logger.info("-------[  Người chơi 1 id: " + checkPlayerId + " , bắt đầu đổi tên  ！] -------");
						String newName = name + "S" + (checkPlayer.getCreateServer() % 1000);
						logger.info("-------[  Tên mới của người chơi : " + newName + " ! ] -------");
						checkPlayer.setPlayerName(newName);
						checkPlayer.setModifyNameCount(0); // Reset số lần đổi tên
						nameIds.put(newName, playerId);
					}
				} else {
					logger.info("-------[ Lỗi đổi tên  Người chơi 1 null：" + (null != checkPlayer) + " , Người chơi 2 null: " + (null != player)
							+ " ] -------");
				}
			} else {
				nameIds.put(name, playerId);
			}
			player.addAllChangePropertyKey();
			PlayerBean bean = player.toPlayerBean();
			bean.updateChangeProperty(player.getChangeProperty());
			// player.offLineSave();// 回存
			list.add(new PlayerUpdateBean(player.toAccountBean(), player.toPlayerBean(), false));
		}
		PlayerRestoreProcessor.getInstance().submitRequest(new ReqUpdateRoleBatchHandler(-1, list));
		logger.info("-------[   Xác minh trùng tên người chơi hoàn tất！  ] -------");
		logger.info("-------[   Bắt đầu xác minh trùng tên liên minh！  ] -------");
		Map<Long, Guild> guilds = GuildService.getInstance().getGuilds();
		nameIds.clear();
		StringBuilder sb;
		int i;
		for (Guild guild : guilds.values()) {
			if (null == guild)
				continue;
			String name = guild.getName();
			long guildId = guild.getId();
			if (nameIds.containsKey(name)) {
				logger.info("-------[ Tên liên minh : " + name + " bị trùng   ] -------");
				int serverId = IdGenerator.getServerByPlayerId(guild.getChairman().getPlayerId());
				String NewName = name+"S"+serverId;
				guild.setName(NewName);
				/*sb = new StringBuilder().append("-TEMP");
				i = 2;
				while (true) {
					if (nameIds.containsKey(sb.toString() + i)) {
						i++;
					} else {
						sb.append(i);
						guild.setName(sb.toString());
						break;
					}
					if (i > 100) {
						sb.append(i + 1000);
						guild.setName(sb.toString());
						break;
					}
				}*/
			}
			nameIds.put(guild.getName(), guildId);
		}
		GuildService.getInstance().saveAll();
		logger.info("-------[   Xác minh trùng tên liên minh hoàn tất！  ] -------");

		logger.info("-------[Dọn dẹp dữ liệu võ đường ]------");
		ArenaRankingDao.deleteAll();
		try {
			// Xóa cache võ đường
			List<ArenaRankingBean> cache = new ArrayList<>();
			Field cahceField = ArenaProcessorManager.getInstance().getClass().getDeclaredField("cache");
			cahceField.setAccessible(true);
			cahceField.set(ArenaProcessorManager.getInstance(), cache);
			cahceField.setAccessible(false);// Khôi phục kiểm soát quyền truy cập

			// Xóa cache bảng ánh xạ idMap võ đường
			Map<Long, Integer> idMap = new ConcurrentHashMap<>();
			Field idMapField = ArenaProcessorManager.getInstance().getClass().getDeclaredField("idMap");
			idMapField.setAccessible(true);
			idMapField.set(ArenaProcessorManager.getInstance(), idMap);
			idMapField.setAccessible(false);// Khôi phục kiểm soát quyền truy cập

			Method method = ArenaProcessorManager.getInstance().getClass().getDeclaredMethod("initializeRanking");
			method.setAccessible(true);// Hủy kiểm soát quyền truy cập
			method.invoke(ArenaProcessorManager.getInstance());
			method.setAccessible(false);// Khôi phục kiểm soát quyền truy cập
		} catch (NoSuchMethodException | SecurityException | IllegalAccessException | IllegalArgumentException
				| InvocationTargetException | NoSuchFieldException e) {
			e.printStackTrace();
		}
		logger.info("-------[ Khởi tạo võ đường hoàn tất  ]------");
		// Dọn dẹp dữ liệu võ đường online, sau khi dọn dẹp xong sẽ làm mới đối thủ của người chơi online, cần comment code dưới đây khi hợp server
		// for (RoleView roleView : roles) {
		// if (null == roleView)
		// continue;
		// long playerId = roleView.getPlayerId();
		// if (playerId < Integer.MAX_VALUE)
		// continue;
		// Player player = PlayerManager.getPlayerByPlayerId(playerId);
		// if (null == player || player.getPlayerType() !=
		// PlayerType.PLAYER_VALUE) {
		// continue;
		// }
		// player.getArenaManager().loginOver();
		// }
		logger.info("-------[ Sửa chữa lỗi xếp hạng võ đường hoàn tất  ]------");
		return null;
	}
}
