package logic.suffle;


import Message.S2CCrossSuffleMsg;
import game.core.pub.script.IScript;
import game.server.logic.suffle.SuffleService;
import game.server.logic.util.ScriptArgs;
import Message.Inner.GRCrossSuffleMsg.GRSuffleState;
import Message.S2CCrossSuffleMsg.SuffleStateRsp;

public class RefreshSuffleStateScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRSuffleState getData = (GRSuffleState) args.get(ScriptArgs.Key.ARG1);

		// 封装协议准备下发数据
		SuffleStateRsp.Builder builder = SuffleStateRsp.newBuilder();
		builder.addAllPlayerStates(getData.getPlayerStatesList());
		SuffleService.getInstance().SendMapPlayers(S2CCrossSuffleMsg.SuffleStateRspID.SuffleStateRspMsgID_VALUE,builder.build().toByteArray());
		return null;
	}

}
