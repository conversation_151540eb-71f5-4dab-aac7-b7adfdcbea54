package logic.crossArena;

import Message.Inner.GRCrossArena;
import Message.Inner.GRUtil;
import Message.Inner.InnerServer;
import Message.S2CArenaMsg;
import Message.S2CArenaMsg.RankingInfo;
import Message.S2CArenaMsg.EnemyMsg;
import Message.S2CCrossArenaMsg.CrossEnemyMsg;
import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.CrossChallengeRsp;
import Message.S2CHeroMsg;
import Message.S2CPlayerMsg;
import com.google.protobuf.ByteString;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;
import Message.S2CPlayerMsg.PromptType;
import com.google.protobuf.InvalidProtocolBufferException;

import data.bean.t_arena_frequencyBean;
import data.bean.t_dropBean;
import game.core.mina.message.SMessage;
import game.core.pub.script.IScript;
import game.server.cross.CrossServer;
import game.server.logic.backpack.bean.GridType;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.crossArena.CrossArenaService;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.S2CFightMsg.FightDataRsp;
import Message.S2CFightMsg.FightDataRspID;
import Message.Inner.GRCrossArena.GRAConNeedRsp;
import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.S2CCrossArenaMsg.GetCrossArenaRankRsp;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CrossConChallengeNeedScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRAConNeedRsp getData = (GRAConNeedRsp) args.get(ScriptArgs.Key.ARG1);

		int needCard = getData.getNeedNum();
		int buyDayChallengeNum =getData.getBuyDayChallengeNum();
		int needDiamond = 0; // 需要的钻石
		int diamondTime = 0; // 本次钻石购买次数
		if(needCard>0)
		{
			List<Item> needItems = new ArrayList<>();
			List<Item> consumeItems = new ArrayList<>();


			consumeItems.addAll(BeanFactory.createProps(11000, needCard));
			if (player.getBackpackManager().isItemNumEnough(consumeItems)) {
				needItems.addAll(consumeItems);
			} else {
				needDiamond += BeanTemplet.getGlobalBean(93).getInt_value();
				// 已有道具数量
				int amount = (int) player.getBackpackManager().getAmount(GridType.ITEM, 11000);
				diamondTime = needCard - amount;
				// 道具不足使用钻石
				int i = 0;
				while (i++ < diamondTime) {
					t_arena_frequencyBean frequencyBean = BeanTemplet.getArenaFrequencyBean(buyDayChallengeNum + i);
					if (frequencyBean != null) {
						needDiamond += frequencyBean.getCost();
					} else {
						MessageUtils.sendPrompt(player, PromptType.ERROR, 38);// TODO替换语言包id
						return null;
					}
					if (player.getDiamond() < needDiamond) {
						MessageUtils.sendPrompt(player, PromptType.ERROR, 10);// TODO替换语言包id
						return null;
					}
				}
				needItems.addAll(BeanFactory.createProps(11000, needCard - diamondTime));
			}
			needItems.addAll(BeanFactory.createProps(ItemType.DIAMOND.value(), needDiamond));
			// 检查道具数量
			if (!needItems.isEmpty() && !player.getBackpackManager().isItemNumEnough(needItems)) {
				MessageUtils.sendPrompt(player, PromptType.WARNING, 36);
				return null;
			}
			// 扣除道具
			player.getBackpackManager().removeItems(needItems, true, Reason.ARENA_BUY_NUM, "");
		}
		//发送连续挑战
		GRCrossArena.GRAConCrossChallegeReq.Builder builder = GRCrossArena.GRAConCrossChallegeReq.newBuilder();
		builder.setPlayerId(getData.getPlayerId());
		builder.setRank(getData.getRank());
		builder.setBuyTimes(diamondTime);
		SMessage sendMsg = new SMessage(GRCrossArena.GRAConCrossChallegeReq.MsgID.eMsgID_VALUE, builder.build().toByteArray());
		CrossServer.getInstance().send(InnerServer.ServerType.ROUTE_SERVER_VALUE, sendMsg);
		return null;
	}
	/**
	 * 胜利后奖励
	 *
	 * @param player
	 * @param builder
	 */
	private void rewardAfterWin(Player player, CrossChallengeRsp.Builder builder) {
		// 翻牌方式
		int dropId = BeanTemplet.getGlobalBean(92).getInt_value();
		List<Item> dropItem = DropService.getDropItems(player.getDropManager(), dropId);
		t_dropBean dropBean = BeanTemplet.getDropBean(dropId);
		String[] drops = StringUtils.split(dropBean.getItem(),";");
		for (int i = 0; i < drops.length; i++) {
			if (drops[i].contains(dropItem.get(0).getId() + "," + dropItem.get(0).getNum())) {
				builder.setDropIndex(i);
				break;
			}
		}
		// 奖励道具
		List<Item> items = new ArrayList<>();
		items.addAll(BeanFactory.createProps(-1, player.getLevel() * 24));// 金币
		items.addAll(BeanFactory.createProps(-6, 0));// 精灵经验
		items.addAll(BeanFactory.createProps(-7, 0));// 队伍经验
		for (Item item : items) {
			RewardItemMsg.Builder riBuilder = RewardItemMsg.newBuilder();
			riBuilder.setItemId(item.getId());
			riBuilder.setItemNum(item.getNum());
			builder.addItemList(riBuilder);
		}
		items.add(dropItem.get(0));
		player.getBackpackManager().addItems(items, true, true, Reason.ARENA_CHALLENGE, "");
	}
}
