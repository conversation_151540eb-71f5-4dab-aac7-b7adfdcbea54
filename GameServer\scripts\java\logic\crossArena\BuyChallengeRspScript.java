package logic.crossArena;

import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.BuyCrossDayChallengeRsp;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;

import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import Message.S2CPlayerMsg.PromptType;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRABuyCrossChallegeRsp;

import java.util.ArrayList;
import java.util.List;

public class BuyChallengeRspScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRABuyCrossChallegeRsp getData = (GRABuyCrossChallegeRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		try {
			int consumeDiamond = getData.getCostDiamond();
			List<Item> needItems = new ArrayList<>();
			if (player.getDiamond() < consumeDiamond) {
				MessageUtils.sendPrompt(player, PromptType.ERROR, 10);// TODO替换语言包id
				return null;
			}
			needItems.addAll(BeanFactory.createProps(ItemType.DIAMOND.value(), consumeDiamond));
			player.getBackpackManager().removeItems(needItems, true, Reason.ARENA_REFRESH, "");
			BuyCrossDayChallengeRsp.Builder builder = BuyCrossDayChallengeRsp.newBuilder();
			builder.setBuyDayChallengeNum(getData.getBuyDayChallengeNum());
			builder.setDayChallengeNum(getData.getDayChallengeNum());
			// 推送结果
			MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.BuyCrossDayChallengeRspID.BuyCrossDayChallengeRspMsgID_VALUE,
					builder.build().toByteArray()));

		}
		catch (Exception ex)
		{
			System.out.printf("BuyChallengeRspScript",ex.toString());
		}


		return null;
	}
}
