package indigo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import Message.S2CIndigoMsg.ApplyIndigoRsp;
import Message.S2CIndigoMsg.ApplyIndigoRspID;
import Message.S2CIndigoMsg.IndigoStage;
import Message.S2CPlayerMsg.PromptType;
import Message.Inner.GRCrossIndigo.GRApplyReq;
import Message.Inner.GRCrossIndigo.GRHeroMsg;
import Message.Inner.GRCrossIndigo.GRROBOTApplyReq;
import game.core.pub.script.IScript;
import game.core.pub.script.ScriptManager;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.HeroPos;
import game.route.indigo.bean.Participant;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;

/**
 * 请求报名
 * 
 * <AUTHOR>
 *
 */
public class ReqApplyIndigoScript implements IScript {
	private final Logger logger = Logger.getLogger(ReqApplyIndigoScript.class);

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(ScriptArgs.Key.ARG1);
		boolean isRobot = (boolean) args.get(ScriptArgs.Key.ARG3);
		if (!isRobot) {
			GRApplyReq req = (GRApplyReq) args.get(ScriptArgs.Key.ARG2);
			IndigoService indigoService = IndigoService.getInstance();
			if (!isRobot && indigoService.getIndigoStage() != IndigoStage.INDIGO_APPLAY_VALUE) {
				GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 287);// 报名已结束
				return null;
			}
			if (indigoService.getParticipantMap().containsKey(req.getPlayerId())) {
				GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 288);// 重复报名
				return null;
			}
			if (req.getHeroListCount() < 12 || req.getHeroListCount() > 18) {
				GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1532);// 上阵精灵不足
				return null;
			}
			// if
			// (!indigoService.getFinalList().containsKey(Long.valueOf(req.getPlayerId())))
			// {
			// GRMessageUtils.sendPrompt(serverId, req.getPlayerId(),
			// PromptType.WARNING, 1530);// 没有报名资格
			// return null;
			// }
			applyPlayer(serverId, req, isRobot);
		} else {
			GRROBOTApplyReq req = (GRROBOTApplyReq) args.get(ScriptArgs.Key.ARG2);
			IndigoService indigoService = IndigoService.getInstance();
			if (!isRobot && indigoService.getIndigoStage() != IndigoStage.INDIGO_APPLAY_VALUE) {
				GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 287);// 报名已结束
				return null;
			}
			if (indigoService.getParticipantMap().containsKey(req.getPlayerId())) {
				GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 288);// 重复报名
				return null;
			}
			if (req.getHeroListCount() < 12 || req.getHeroListCount() > 18) {
				GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 1532);// 上阵精灵不足
				return null;
			}
			applyRobot(serverId, req, isRobot);
		}
		return null;
	}

	/**
	 * 玩家报名
	 * 
	 * @param player
	 * @return
	 */
	private void applyPlayer(int serverId, GRApplyReq req, boolean isRobot) {
		IndigoService indigoService = IndigoService.getInstance();
		Participant participant = new Participant();
		participant.setPlayerId(req.getPlayerId());
		long power = 0;
		List<GRHeroMsg> heroListList = req.getHeroListList();
		GRHeroMsg grHeroMsg;
		Hero hero = null;
		Hero temp;
		for (int i = 0; i < heroListList.size(); i++) {
			grHeroMsg = heroListList.get(i);
			temp = new Hero();
			temp.fromGRHeroMsg(grHeroMsg);
			power += temp.getPower();
			participant.getHeroList().add(temp);
			if (null == hero || hero.getPower() < temp.getPower()) {
				hero = temp;
			}
		}
		participant.setName(req.getPlayerName());
		participant.setSex(req.getSex());
		participant.setRobot(isRobot);
		participant.setOffensiveAdd(req.getOffensiveAdd());
		participant.setPower(power);
		participant.setShowHeroId(hero.getId());
		participant.setStar(hero.getStar());
		participant.setLevel(req.getLevel());
		participant.setLvl(hero.getLevel());
		participant.setGuildId(req.getGuildId());
		participant.setServerId(serverId);
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		Map<Long, Participant> map = indigoService.getParticipantMap().get(groupId);
		if (null == map) {
			map = new HashMap<>();
			indigoService.getParticipantMap().put(groupId, map);
		}
		map.put(participant.getPlayerId(), participant);
		indigoService.getPlayerServerGroup().put(participant.getPlayerId(), groupId);
		addEnterList(participant.getPlayerId(), groupId);
		indigoService.addApplyNum(groupId);
		if (!isRobot && req.getGuildId() != 0) {
			// 更新缓存的公会名称
			indigoService.updateGuildInfo(req.getGuildId(), req.getGuildName(), req.getChairManName(), req.getFlag());
		}
		// 推送报名人数
		indigoService.applyNumNotify(groupId);
		// 自动编队
		ScriptArgs args = new ScriptArgs();
		args.put(Key.ARG1, participant);
		boolean call = (boolean) ScriptManager.getInstance().call("indigo.AutoFormationScript", args);
		if (!call) {
			return;
		}
		// 推送
		ApplyIndigoRsp.Builder builder = ApplyIndigoRsp.newBuilder();
		for (Formation formation : participant.getFormations()) {
			builder.addFormation(formation.genBuilder());
			for (HeroPos pos : formation.getInfoList()) {
				builder.addHero(pos.genBuilder());
			}
		}
		GRMessageUtils.sendMsg2GameServer(serverId, indigoService.genGRIndigoRsp(req.getPlayerId(),
				ApplyIndigoRspID.ApplyIndigoRspMsgID_VALUE, builder.build().toByteString()));
		logger.info("分组: " + groupId + ", 玩家:" + req.getPlayerName() + " ================报名成功，当前报名人数："
				+ indigoService.getApplyNum(groupId));
	}

	private void addEnterList(long playerId, int groupId) {
		IndigoService indigoService = IndigoService.getInstance();
		Map<Integer, List<Long>> enterList = indigoService.getEnterList();
		List<Long> l = null;
		if (enterList.containsKey(groupId)) {
			l = enterList.get(groupId);
		} else {
			l = new ArrayList<>();
			enterList.put(groupId, l);
		}
		l.add(playerId);
	}

	/**
	 * 机器人报名
	 * 
	 * @param player
	 * @return
	 */
	private void applyRobot(int serverId, GRROBOTApplyReq req, boolean isRobot) {
		IndigoService indigoService = IndigoService.getInstance();
		Participant participant = new Participant();
		participant.setPlayerId(req.getPlayerId());
		long power = 0;
		List<GRHeroMsg> heroListList = req.getHeroListList();
		GRHeroMsg grHeroMsg;
		Hero hero = null;
		Hero temp;
		for (int i = 0; i < heroListList.size(); i++) {
			grHeroMsg = heroListList.get(i);
			temp = new Hero();
			temp.fromGRHeroMsg(grHeroMsg);
			power += temp.getPower();
			participant.getHeroList().add(temp);
			if (null == hero || hero.getPower() < temp.getPower()) {
				hero = temp;
			}
		}
		participant.setName(req.getPlayerName());
		participant.setSex(req.getSex());
		participant.setRobot(isRobot);
		participant.setOffensiveAdd(req.getOffensiveAdd());
		participant.setPower(power);
		participant.setShowHeroId(hero.getId());
		participant.setStar(hero.getStar());
		participant.setLevel(req.getLevel());
		participant.setLvl(hero.getLevel());
		participant.setGuildId(req.getGuildId());
		participant.setServerId(serverId);
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		Map<Long, Participant> map = indigoService.getParticipantMap().get(groupId);
		if (null == map) {
			map = new HashMap<>();
			indigoService.getParticipantMap().put(groupId, map);
		}
		map.put(participant.getPlayerId(), participant);
		indigoService.getPlayerServerGroup().put(participant.getPlayerId(), groupId);
		addEnterList(participant.getPlayerId(), groupId);
		indigoService.addApplyNum(groupId);
		if (!isRobot && req.getGuildId() != 0) {
			// 更新缓存的公会名称
			IndigoService.getInstance().updateGuildInfo(req.getGuildId(), req.getGuildName(), req.getChairManName(),
					req.getFlag());
		}
		// 推送报名人数
		indigoService.applyNumNotify(groupId);
		// 自动编队
		ScriptArgs args = new ScriptArgs();
		args.put(Key.ARG1, participant);
		ScriptManager.getInstance().call("indigo.AutoFormationScript", args);
		// 推送
		ApplyIndigoRsp.Builder builder = ApplyIndigoRsp.newBuilder();
		for (Formation formation : participant.getFormations()) {
			builder.addFormation(formation.genBuilder());
		}
		logger.info("分组: " + groupId + ", 机器人:" + req.getPlayerName() + " ================报名成功，当前报名人数："
				+ indigoService.getApplyNum(groupId));
	}
}
