package indigo;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import Message.S2CIndigoMsg.SingleRankMsg;
import Message.Inner.GRCrossIndigo.GRFinalQualifyNotify;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;

/**
 * 推送决赛资格到各个服务器
 * <AUTHOR>
 *
 */
public class NotifyFinalQualifyScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
//		if (null == arg) {
			notifyFinalQualify(null);
//		} else {
//			ScriptArgs args = (ScriptArgs) arg;
//			int serverId = (int) args.get(ScriptArgs.Key.ARG1);
//			notifyFinalQualify(serverId);
//		}
		return null;
	}

	private void notifyFinalQualify(Integer serverId) {
		IndigoService indigoService = IndigoService.getInstance();
		Map<Integer, List<SingleRankMsg>> preRaceRankListAllAll = indigoService.getPreRaceRankListAll();
		for (Entry<Integer, List<SingleRankMsg>> en : preRaceRankListAllAll.entrySet()) {
			Integer groupId = en.getKey();
			List<SingleRankMsg> preRaceRankListAll = en.getValue();
			GRFinalQualifyNotify.Builder builder = GRFinalQualifyNotify.newBuilder();
			int num = 0;
			for (SingleRankMsg singleRankMsg : preRaceRankListAll) {
				builder.addPlayerId(singleRankMsg.getPlayerId());
				builder.addPreFinalRank(singleRankMsg);
				if (++num >= 256)
					break;
			}
			if (null == serverId) {
				GRMessageUtils.sendMsg2GroupAllGameServer(indigoService.genGRIndigoRsp(0, GRFinalQualifyNotify.MsgID.eMsgID_VALUE, builder.build().toByteString()), groupId);
			} else {
				Integer groupIdByServer = ServerService.getInstance().getGroupIdByServer(serverId);
				if (groupId == groupIdByServer) {
					GRMessageUtils.sendMsg2GameServer(serverId, indigoService.genGRIndigoRsp(0, GRFinalQualifyNotify.MsgID.eMsgID_VALUE, builder.build().toByteString()));
				}
			}
		}
	}
}
