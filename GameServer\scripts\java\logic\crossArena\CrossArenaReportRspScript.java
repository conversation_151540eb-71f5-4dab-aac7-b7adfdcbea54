package logic.crossArena;

import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.CrossArenaReportsRsp;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;

import data.bean.t_arena_integralBean;
import data.bean.t_cross_arena_integralBean;
import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import Message.S2CPlayerMsg.PromptType;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRACrossArenaReportRsp;

import java.util.ArrayList;
import java.util.List;

public class CrossArenaReportRspScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRACrossArenaReportRsp getData = (GRACrossArenaReportRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		try {
			CrossArenaReportsRsp.Builder builder = CrossArenaReportsRsp.newBuilder();
			builder.setReports(getData.getReports());
			// 推送结果
			MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.CrossArenaReportsRspID.CrossArenaReportsRspMsgID_VALUE,
					builder.build().toByteArray()));

		}
		catch (Exception ex)
		{
			System.out.printf("CrossArenaReportRspScript",ex.toString());
		}


		return null;
	}
}
