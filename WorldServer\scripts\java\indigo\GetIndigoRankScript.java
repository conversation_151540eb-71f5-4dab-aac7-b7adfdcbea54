package indigo;

import Message.S2CIndigoMsg.GetIndigoRankRsp;
import Message.S2CIndigoMsg.GetIndigoRankRspID;
import Message.S2CIndigoMsg.IndigoGuildRankMsg;
import Message.S2CIndigoMsg.SingleRankMsg;
import Message.Inner.GRCrossIndigo.GRGetIndigoRankReq;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
/**
 * 获取排行榜
 * <AUTHOR>
 *
 */
public class GetIndigoRankScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		IndigoService indigoService = IndigoService.getInstance();
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		GRGetIndigoRankReq req = (GRGetIndigoRankReq) args.get(Key.ARG2);
		GetIndigoRankRsp.Builder builder = GetIndigoRankRsp.newBuilder();
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		if (null != groupId && indigoService.getRankList().containsKey(groupId)) {
			for (SingleRankMsg singleRankMsg : indigoService.getRankList().get(groupId)) {
				if (builder.getRankListCount() < 50) {
					builder.addRankList(singleRankMsg);
				}
				if (singleRankMsg.getPlayerId() == req.getPlayerId()) {
					builder.setSelfRank(singleRankMsg);
				}
			}
		}
		if (null != groupId && indigoService.getGuildRankList().containsKey(groupId)) {
			for (IndigoGuildRankMsg msg : indigoService.getGuildRankList().get(groupId)) {
				builder.addGuildRankList(msg);
				if (msg.getGuildId() == req.getGuildId()) {
					builder.setSelfguildRank(msg);
				}
			}
		}
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(req.getPlayerId(), GetIndigoRankRspID.GetIndigoRankRspMsgID_VALUE, builder.build().toByteString()));
		return null;
	}

}
