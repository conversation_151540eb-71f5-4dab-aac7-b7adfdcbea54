<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE configuration PUBLIC "-//ibatis.apache.org//DTD Config 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="defaultStatementTimeout" value="600" /> <!--wait driver time senconds -->
		<setting name="logPrefix" value="org.apache.ibatis.logPrefix" /> <!-- lgnore log -->
	</settings>
    <environments default="development">
	<environment id="development">
	    <transactionManager type="JDBC"/>
	    <dataSource type="POOLED">
		<property name="driver" value="com.mysql.jdbc.Driver"/>
		<property name="url" value="${url}"/>
		<property name="username" value="${username}"/>
		<property name="password" value="${password}"/>
		<!-- sql select -->
		<property name="poolPingQuery" value="select 1"/>
		<!-- is ping -->
		<property name="poolPingEnabled" value="true"/>
		<!-- ping time millisecond -->
		<property name="poolPingConnectionsNotUsedFor" value="300000"/>
		<property name="poolMaximumActiveConnections" value = "10" />
		<property name="poolMaximumIdleConnections" value = "5" />
		<property name="poolMaximumCheckoutTime" value = "60000" />
	    </dataSource> 
	</environment> 
    </environments> 
    <mappers>
        <mapper resource="data/sqlmap/t_loading.xml" />
        <mapper resource="data/sqlmap/t_betaConfig.xml" />
        <mapper resource="data/sqlmap/t_funciton.xml" />
        <mapper resource="data/sqlmap/t_commend_language.xml" />
        <mapper resource="data/sqlmap/t_commend_team.xml" />
        <mapper resource="data/sqlmap/t_rule.xml" />
        <mapper resource="data/sqlmap/t_jump.xml" />
        <mapper resource="data/sqlmap/t_name.xml" />
        <mapper resource="data/sqlmap/t_vip_discribe.xml" />
        <mapper resource="data/sqlmap/t_vip.xml" />
        <mapper resource="data/sqlmap/t_aptspecailcard.xml" />
        <mapper resource="data/sqlmap/t_online_reward.xml" />
        <mapper resource="data/sqlmap/t_drop.xml" />
        <mapper resource="data/sqlmap/t_advertisement.xml" />
        <mapper resource="data/sqlmap/t_push.xml" />
        <mapper resource="data/sqlmap/t_level_gift.xml" />
        <mapper resource="data/sqlmap/t_login_gift.xml" />
        <mapper resource="data/sqlmap/t_skill.xml" />
        <mapper resource="data/sqlmap/t_skill_effect.xml" />
        <mapper resource="data/sqlmap/t_effect_function.xml" />
        <mapper resource="data/sqlmap/t_item.xml" />
        <mapper resource="data/sqlmap/t_language.xml" />
        <mapper resource="data/sqlmap/t_language_model.xml" />
        <mapper resource="data/sqlmap/t_monster.xml" />
        <mapper resource="data/sqlmap/t_mission_game.xml" />
        <mapper resource="data/sqlmap/t_section.xml" />
        <mapper resource="data/sqlmap/t_nether_swap.xml" />
        <mapper resource="data/sqlmap/t_answer.xml" />
        <mapper resource="data/sqlmap/t_exam.xml" />
        <mapper resource="data/sqlmap/t_surprise.xml" />
        <mapper resource="data/sqlmap/t_section_box.xml" />
        <mapper resource="data/sqlmap/t_section_chapter.xml" />
        <mapper resource="data/sqlmap/t_max_layer.xml" />
        <mapper resource="data/sqlmap/t_champion_cost.xml" />
        <mapper resource="data/sqlmap/t_champion_road.xml" />
        <mapper resource="data/sqlmap/t_rodeo.xml" />
        <mapper resource="data/sqlmap/t_crossrealm.xml" />
        <mapper resource="data/sqlmap/t_hero.xml" />
        <mapper resource="data/sqlmap/t_hero_step.xml" />
        <mapper resource="data/sqlmap/t_hero_star.xml" />
        <mapper resource="data/sqlmap/t_skin.xml" />
        <mapper resource="data/sqlmap/t_hero_character.xml" />
        <mapper resource="data/sqlmap/t_uniquemodel.xml" />
        <mapper resource="data/sqlmap/t_unique.xml" />
        <mapper resource="data/sqlmap/t_hero_relate.xml" />
        <mapper resource="data/sqlmap/t_level.xml" />
        <mapper resource="data/sqlmap/t_instruction.xml" />
        <mapper resource="data/sqlmap/t_equip.xml" />
        <mapper resource="data/sqlmap/t_magic_weapon.xml" />
        <mapper resource="data/sqlmap/t_research.xml" />
        <mapper resource="data/sqlmap/t_unionstore.xml" />
        <mapper resource="data/sqlmap/t_construction.xml" />
        <mapper resource="data/sqlmap/t_union_rankaward.xml" />
        <mapper resource="data/sqlmap/t_union_dailyaward.xml" />
        <mapper resource="data/sqlmap/t_flag.xml" />
        <mapper resource="data/sqlmap/t_dispatch.xml" />
        <mapper resource="data/sqlmap/t_dispatch_quantity.xml" />
        <mapper resource="data/sqlmap/t_red_envelope.xml" />
        <mapper resource="data/sqlmap/t_training_drop.xml" />
        <mapper resource="data/sqlmap/t_training.xml" />
        <mapper resource="data/sqlmap/t_hero_collect.xml" />
        <mapper resource="data/sqlmap/t_level_template.xml" />
        <mapper resource="data/sqlmap/t_equip_refine.xml" />
        <mapper resource="data/sqlmap/t_experience_box.xml" />
        <mapper resource="data/sqlmap/t_gem.xml" />
        <mapper resource="data/sqlmap/t_equip_up.xml" />
        <mapper resource="data/sqlmap/t_evolve.xml" />
        <mapper resource="data/sqlmap/t_task.xml" />
        <mapper resource="data/sqlmap/t_suits.xml" />
        <mapper resource="data/sqlmap/t_exclusive.xml" />
        <mapper resource="data/sqlmap/t_strengthen.xml" />
        <mapper resource="data/sqlmap/t_arena_number.xml" />
        <mapper resource="data/sqlmap/t_arena_find.xml" />
        <mapper resource="data/sqlmap/t_arena_rank.xml" />
        <mapper resource="data/sqlmap/t_arena_aitemplet.xml" />
        <mapper resource="data/sqlmap/t_arena_airank.xml" />
        <mapper resource="data/sqlmap/t_arena_integral.xml" />
        <mapper resource="data/sqlmap/t_arena_frequency.xml" />
        <mapper resource="data/sqlmap/t_z_skill.xml" />
        <mapper resource="data/sqlmap/t_leader_molecule.xml" />
        <mapper resource="data/sqlmap/t_leader_gene.xml" />
        <mapper resource="data/sqlmap/t_indiana.xml" />
        <mapper resource="data/sqlmap/t_elves_training.xml" />
        <mapper resource="data/sqlmap/t_forge_type.xml" />
        <mapper resource="data/sqlmap/t_forge.xml" />
        <mapper resource="data/sqlmap/t_special_training.xml" />
        <mapper resource="data/sqlmap/t_shop_grid.xml" />
        <mapper resource="data/sqlmap/t_shop_table.xml" />
        <mapper resource="data/sqlmap/t_shop.xml" />
        <mapper resource="data/sqlmap/t_res_jump.xml" />
        <mapper resource="data/sqlmap/t_shop_goods.xml" />
        <mapper resource="data/sqlmap/t_gacha_consume.xml" />
        <mapper resource="data/sqlmap/t_baowugacha.xml" />
        <mapper resource="data/sqlmap/t_gacha.xml" />
        <mapper resource="data/sqlmap/t_gacha_reward.xml" />
        <mapper resource="data/sqlmap/t_boss_reward.xml" />
        <mapper resource="data/sqlmap/t_boss.xml" />
        <mapper resource="data/sqlmap/t_team_monster.xml" />
        <mapper resource="data/sqlmap/t_team_fight.xml" />
        <mapper resource="data/sqlmap/t_funcitonopen.xml" />
        <mapper resource="data/sqlmap/t_dimond_exchange.xml" />
        <mapper resource="data/sqlmap/t_mail.xml" />
        <mapper resource="data/sqlmap/t_scene.xml" />
        <mapper resource="data/sqlmap/t_repo.xml" />
        <mapper resource="data/sqlmap/t_attribute.xml" />
        <mapper resource="data/sqlmap/t_vs_tower.xml" />
        <mapper resource="data/sqlmap/t_vs_buff.xml" />
        <mapper resource="data/sqlmap/t_vs_condition.xml" />
        <mapper resource="data/sqlmap/t_vs_rank.xml" />
        <mapper resource="data/sqlmap/t_vs_reward.xml" />
        <mapper resource="data/sqlmap/t_reborn.xml" />
        <mapper resource="data/sqlmap/t_recycling.xml" />
        <mapper resource="data/sqlmap/t_insignia.xml" />
        <mapper resource="data/sqlmap/t_head_frame.xml" />
        <mapper resource="data/sqlmap/t_head_portrait.xml" />
        <mapper resource="data/sqlmap/t_herolevel.xml" />
        <mapper resource="data/sqlmap/t_challenge.xml" />
        <mapper resource="data/sqlmap/t_power.xml" />
        <mapper resource="data/sqlmap/t_artifact.xml" />
        <mapper resource="data/sqlmap/t_check.xml" />
        <mapper resource="data/sqlmap/t_check_add.xml" />
        <mapper resource="data/sqlmap/t_timelimitedpack.xml" />
        <mapper resource="data/sqlmap/t_seven.xml" />
        <mapper resource="data/sqlmap/t_revel.xml" />
        <mapper resource="data/sqlmap/t_day_reward.xml" />
        <mapper resource="data/sqlmap/t_final_reward.xml" />
        <mapper resource="data/sqlmap/t_quartz_shop.xml" />
        <mapper resource="data/sqlmap/t_quartz_section.xml" />
        <mapper resource="data/sqlmap/t_quartz_robot.xml" />
        <mapper resource="data/sqlmap/t_quartz.xml" />
        <mapper resource="data/sqlmap/t_voice.xml" />
        <mapper resource="data/sqlmap/t_pirateboss_kill.xml" />
        <mapper resource="data/sqlmap/t_pirateboss_rank.xml" />
        <mapper resource="data/sqlmap/t_pirateboss_data.xml" />
        <mapper resource="data/sqlmap/t_pirateboss_hurt.xml" />
        <mapper resource="data/sqlmap/t_sensitive_word.xml" />
        <mapper resource="data/sqlmap/t_worldTask_box.xml" />
        <mapper resource="data/sqlmap/t_roadPavilion_rank.xml" />
        <mapper resource="data/sqlmap/t_roadPavilion_task.xml" />
        <mapper resource="data/sqlmap/t_roadPavilion_shop.xml" />
        <mapper resource="data/sqlmap/t_roadPavilion.xml" />
        <mapper resource="data/sqlmap/t_wildMonster.xml" />
        <mapper resource="data/sqlmap/t_rune_update.xml" />
        <mapper resource="data/sqlmap/t_runesuit.xml" />
        <mapper resource="data/sqlmap/t_rune.xml" />
        <mapper resource="data/sqlmap/t_world_answer.xml" />
        <mapper resource="data/sqlmap/t_crosspvp_rank.xml" />
        <mapper resource="data/sqlmap/t_crosspvp_robot.xml" />
        <mapper resource="data/sqlmap/t_crosspvp.xml" />
        <mapper resource="data/sqlmap/t_journey.xml" />
        <mapper resource="data/sqlmap/t_recharge_lines.xml" />
        <mapper resource="data/sqlmap/t_recharge.xml" />
        <mapper resource="data/sqlmap/t_recharge_first.xml" />
        <mapper resource="data/sqlmap/t_recharge_single.xml" />
        <mapper resource="data/sqlmap/t_single_buy.xml" />
        <mapper resource="data/sqlmap/t_recharge_accumulates_true.xml" />
        <mapper resource="data/sqlmap/t_recharge_accumulates.xml" />
        <mapper resource="data/sqlmap/t_cross_arena_number.xml" />
        <mapper resource="data/sqlmap/t_cross_arena_integral.xml" />
        <mapper resource="data/sqlmap/t_wushuang_shop.xml" />
        <mapper resource="data/sqlmap/t_res_shop.xml" />
        <mapper resource="data/sqlmap/t_special_card.xml" />
        <mapper resource="data/sqlmap/t_king_card.xml" />
        <mapper resource="data/sqlmap/t_daily_recharge.xml" />
        <mapper resource="data/sqlmap/t_month_fund.xml" />
        <mapper resource="data/sqlmap/t_year_fund.xml" />
        <mapper resource="data/sqlmap/t_privilage_new_shop.xml" />
        <mapper resource="data/sqlmap/t_privilage_open_shop.xml" />
        <mapper resource="data/sqlmap/t_jewellery_shop.xml" />
        <mapper resource="data/sqlmap/t_privilage_shop.xml" />
        <mapper resource="data/sqlmap/t_privilage_active.xml" />
        <mapper resource="data/sqlmap/t_special_shop.xml" />
        <mapper resource="data/sqlmap/t_recharge_activity.xml" />
        <mapper resource="data/sqlmap/t_competition.xml" />
        <mapper resource="data/sqlmap/t_competition_rank_reward.xml" />
        <mapper resource="data/sqlmap/t_competition_self_reward.xml" />
        <mapper resource="data/sqlmap/t_fund_chapter.xml" />
        <mapper resource="data/sqlmap/t_fund_power.xml" />
        <mapper resource="data/sqlmap/t_fund_login.xml" />
        <mapper resource="data/sqlmap/t_benefits_open.xml" />
        <mapper resource="data/sqlmap/t_suffle_rank_reward.xml" />
        <mapper resource="data/sqlmap/t_suffle_score_reward.xml" />
        <mapper resource="data/sqlmap/t_limit_tip_gift.xml" />
        <mapper resource="data/sqlmap/t_drop_change.xml" />
        <mapper resource="data/sqlmap/t_ext_delete.xml" />
        <mapper resource="data/sqlmap/t_ext_drop.xml" />
        <mapper resource="data/sqlmap/t_player_filter.xml" />
        <mapper resource="data/sqlmap/t_global.xml" />
    </mappers>
</configuration>
