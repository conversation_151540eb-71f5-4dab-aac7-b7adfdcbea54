package logic.crossArena;

import Message.S2CArenaMsg.EnemyMsg;
import Message.S2CCrossArenaMsg.CrossEnemyMsg;
import Message.S2CCrossArenaMsg;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import game.core.pub.script.IScript;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.S2CCrossArenaMsg.GetCrossArenaRsp;
import Message.S2CCrossArenaMsg.CrossArenaMsg;
import Message.S2CCrossArenaMsg.RefreshCrossEnemyRsp;
import Message.Inner.GRCrossArena.GRARefreshEnemyRsp;
import Message.S2CPlayerMsg.PromptType;

import java.util.ArrayList;
import java.util.List;

public class RefreshCrossArenaEnemyScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);

		GRARefreshEnemyRsp getData = (GRARefreshEnemyRsp) args.get(ScriptArgs.Key.ARG1);
		List<Item> needItems = new ArrayList<>();
		int consumeDiamond = getData.getCostDiamond();
		if (player.getDiamond() < consumeDiamond) {
			MessageUtils.sendPrompt(player, PromptType.ERROR, 10);// TODO替换语言包id
			return null;
		}
		needItems.addAll(BeanFactory.createProps(ItemType.DIAMOND.value(), consumeDiamond));
		player.getBackpackManager().removeItems(needItems, true, Reason.ARENA_REFRESH, "");

		// 封装协议准备下发数据
		RefreshCrossEnemyRsp.Builder builder = RefreshCrossEnemyRsp.newBuilder();
		builder.setCurRank(getData.getCurRank());
		builder.setDayrefreshEnemyNum(getData.getDayrefreshEnemyNum());
		for(ByteString byteString:getData.getEnemyListList()) {
			try {
				CrossEnemyMsg cenemyMsg = CrossEnemyMsg.parseFrom(byteString);
				EnemyMsg.Builder enemyMsg = EnemyMsg.newBuilder();
				enemyMsg.setPlayerId(cenemyMsg.getPlayerId());
				enemyMsg.setRank(cenemyMsg.getRank());
				enemyMsg.setPower(cenemyMsg.getPower());
				enemyMsg.setName(cenemyMsg.getName());
				enemyMsg.setHeroId(cenemyMsg.getHeroId());
				enemyMsg.setRobot(cenemyMsg.getRobot());
				enemyMsg.setViewCard(cenemyMsg.getViewCard());
				enemyMsg.setManifesto(cenemyMsg.getManifesto());
				builder.addEnemyList(enemyMsg);
			} catch (InvalidProtocolBufferException e) {
				e.printStackTrace();
			}
		}
		// 推送
		MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.RefreshCrossEnemyRspID.RefreshCrossEnemyMsgID_VALUE,
				builder.build().toByteArray()));
		return null;
	}

}
