package indigo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.log4j.Logger;

import Message.S2CIndigoMsg.FightStage;
import Message.S2CIndigoMsg.RaceInfoMsg;
import Message.S2CIndigoMsg.RaceStatus;
import data.bean.t_quartzBean;
import game.core.pub.script.IScript;
import game.core.pub.util.ExceptionEx;
import game.route.fight.FightService;
import game.route.fight.fightRoom.IndigoFightRoom;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.HeroPos;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SingleRace;
import game.route.server.ServerService;
import game.route.util.ScriptArgs;
import game.util.BeanTemplet;

/**
 * 开始指定轮次比赛
 * 
 * <AUTHOR>
 *
 *         2018年9月28日
 */
public class StartRacesScript implements IScript {
	private final Logger logger = Logger.getLogger(StartRacesScript.class);

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	// 比赛文本子索引
	private int subIndex = 1;

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int session = (Integer) args.get(ScriptArgs.Key.ARG1);
		Set<Integer> groupIds = ServerService.getInstance().getGameServerGroup().keySet();
		for (Integer groupId : groupIds) {
			List<SingleRace> races = new ArrayList<>();
			Map<String, SingleRace> map = IndigoService.getInstance().getRaceMap().get(session).get(groupId);
			if (map != null) {
				for (SingleRace singleRace : map.values()) {
					singleRace.setStatus(RaceStatus.RACE_FIGHT_VALUE);
					races.add(singleRace);
				}
			}
			// 决赛推送
			if (session > 5) {// 单场比赛战斗状态
				IndigoService.getInstance().finalRaceNotify(session);
			}
			// 战斗；测试性能，尝试启动多线程任务
			for (SingleRace singleRace : races) {
				// 比赛推送双方
				IndigoService.getInstance().myRaceNotify(singleRace);// 战斗开始
				// 处理比赛
				race(singleRace, groupId);
				// 比赛推送双方
				IndigoService.getInstance().myRaceNotify(singleRace);// 战斗结束
			}
		}

		// 决赛推送
		if (session > 5) {// 单场比赛结束状态
			IndigoService.getInstance().finalRaceNotify(session);
		}
		// 比赛文字信息推送
		IndigoService.getInstance().flushRaceInfoNotify(null);

		return null;
	}

	/**
	 * 比赛
	 * 
	 * @param singleRace
	 */
	public void race(SingleRace singleRace, int groupId) {
		IndigoService service = IndigoService.getInstance();
		Participant red = service.getParticipantByPlayerId(singleRace.getRedId());
		Participant blue = service.getParticipantByPlayerId(singleRace.getBlueId());
		try {
			List<Hero> redHroList = red.getHeroList();
			List<Hero> blueHroList = blue.getHeroList();
			int bluePower = 0;
			int redPower = 0;
			for (Formation f : singleRace.getBlueFormation()) {
				for (HeroPos h : f.getInfoList()) {
					bluePower += blue.getHero(h.getHeroId()).getPower();
				}
			}
			for (Formation f : singleRace.getRedFormation()) {
				for (HeroPos h : f.getInfoList()) {
					redPower += red.getHero(h.getHeroId()).getPower();
				}
			}
			singleRace.setBluePower(bluePower);
			singleRace.setRedPower(redPower);
			IndigoFightRoom indigoFightRoom = FightService.getInstance().createIndigoFight(singleRace, redHroList,
					blueHroList);
			if (indigoFightRoom.getResult() == 1) {
				singleRace.setWinner(singleRace.getRedId());
			} else {
				singleRace.setWinner(singleRace.getBlueId());
			}
			singleRace.setReplayerId(indigoFightRoom.getFightRepot().getUid());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error(ExceptionEx.e2s(e));
			/**
			 * 战斗出现异常，随机生成结果，避免影响三国霸主整体流程，这时会没有比赛回放
			 */
//			SimpleRandom random = new SimpleRandom();
//			if (singleRace.getBlueId() == 0) {
//				singleRace.setWinner(singleRace.getRedId());
//			} else {
//				singleRace.setWinner(random.nextIncBound(0, 1) == 0 ? singleRace.getRedId() : singleRace.getBlueId());
//			}
			/**
			 * 战斗出现异常，不能随机生成结果，会出现高战力玩家被机器人打败的情况,
			 * 按策划要求 改为 机器人必输,战力低的玩家必输
			 * 跨服时无法判断机器人,按战力判断
			 */
			if (singleRace.getBluePower() >= singleRace.getRedPower()) {
				singleRace.setWinner(singleRace.getBlueId());
			} else {
				singleRace.setWinner(singleRace.getRedId());
			}
		}
		// 比赛结果处理
		singleRace.setStatus(RaceStatus.RACE_END_VALUE);
		t_quartzBean quartzBean = BeanTemplet.getQuartzBean(1);
		Participant winP = null;
		Participant loseP = null;
		if (red.getPlayerId() == singleRace.getWinner()) {
			winP = red;
			loseP = blue;
		} else {
			winP = blue;
			loseP = red;
		}
		int winInt = (int) Math.ceil((float) (loseP.getPower() + winP.getPower()) * quartzBean.getWin_point() / 10000);
		winP.setIntegral(winP.getIntegral() + winInt);
		winP.setWinNum(winP.getWinNum() + 1);
		int loseInt = (int) Math
				.ceil((float) (loseP.getPower() + winP.getPower()) * quartzBean.getFail_point() / 10000);
		loseP.setIntegral(loseP.getIntegral() + loseInt);
		loseP.setLoseNum(loseP.getLoseNum() + 1);
		loseP.setWeed(true);// 设置淘汰标志
		// 添加战斗文字信息
		addRaceInfo(singleRace, groupId);
		logger.info("=============单场比赛结束  " + singleRace.getRedName() + "  --VS--   " + singleRace.getBlueName());
		logger.info("=============胜利者  " + (singleRace.getWinner() == singleRace.getRedId() ? singleRace.getRedName()
				: singleRace.getBlueName()));
	}

	/**
	 * 添加战斗文字信息
	 * 
	 * @param singleRace
	 */
	private void addRaceInfo(SingleRace singleRace, int groupId) {
		if (IndigoService.getInstance().getSession() > 5) {
			return;
		}
		List<RaceInfoMsg> raceInfoList = IndigoService.getInstance().getRaceInfoList().get(groupId);
		if (null == raceInfoList) {
			raceInfoList = new ArrayList<>();
			IndigoService.getInstance().getRaceInfoList().put(groupId, raceInfoList);
		}
		RaceInfoMsg.Builder builder = RaceInfoMsg.newBuilder();
		int index = raceInfoList.size() + 1;
		if (IndigoService.getInstance().getRaceInfoSession() != singleRace.getSession()) {// 插入新session标识
			IndigoService.getInstance().setRaceInfoSession(singleRace.getSession());
			builder.setTitle(true);
			subIndex = 1;// 重置子索引
		} else {
			builder.setTitle(false);
			subIndex += 1;
		}
		builder.setSubIndex(subIndex);
		builder.setStage(IndigoService.getInstance().getSession() > 5 ? FightStage.Indigo_final_VALUE
				: FightStage.Indigo_preliminary_VALUE);
		builder.setSession(singleRace.getSession());
		if (singleRace.getRedId() == singleRace.getWinner()) {
			builder.setLoseName(singleRace.getBlueName());
			builder.setWinName(singleRace.getRedName());
		} else {
			builder.setWinName(singleRace.getBlueName());
			builder.setLoseName(singleRace.getRedName());
		}
		builder.setIndex(index);// index 从1开始
		RaceInfoMsg raceInfoMsg = builder.build();
		raceInfoList.add(raceInfoMsg);
		List<RaceInfoMsg> list = IndigoService.getInstance().getRaceInfoNotifyCache().get(groupId);
		if (null == list) {
			list = new ArrayList<>();
			IndigoService.getInstance().getRaceInfoNotifyCache().put(groupId, list);
		}
		list.add(raceInfoMsg);
		// 推送比赛文字信息
		if (IndigoService.getInstance().getRaceInfoNotifyCache().size() > 10) {
			IndigoService.getInstance().flushRaceInfoNotify(groupId);
		}
	}

}
