package indigo;

import java.util.Map;
import java.util.Set;

import Message.Inner.GRCrossIndigo.GRFinalRaceNotify;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.SingleRace;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;

/**
 * 推送决赛信息到gameServer
 * <AUTHOR>
 *
 */
public class FinalRaceNotifyScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		if (null == arg) {
			notifyFinalRace(null);
		} else {
			ScriptArgs args = (ScriptArgs) arg;
			int serverId = (int) args.get(ScriptArgs.Key.ARG1);
			notifyFinalRace(serverId);
		}
		return null;
	}
	
	private void notifyFinalRace(Integer serverId) {
		IndigoService indigoService = IndigoService.getInstance();
		// 决赛信息
		if (null == serverId) {
			// 推送各个服务器分组
			if (indigoService.getSession() > 5) {
				Map<Integer, Map<Integer, GameServer>> gameServerGroup = ServerService.getInstance().getGameServerGroup();
				Set<Integer> keySet = gameServerGroup.keySet();
				for (Integer groupId : keySet) {
					GRFinalRaceNotify.Builder builder = GRFinalRaceNotify.newBuilder();
					for (int i = 6; i <= indigoService.getSession(); i++) {// 最后三轮,总共八轮
						if (indigoService.getRaceMap().get(i) == null) {
							continue;
						}
						for (SingleRace singleRace : indigoService.getRaceMap().get(i).get(groupId).values()) {
							builder.addFinalRace(singleRace.genFinalBuilder());
						}
					}
					if (builder.getFinalRaceCount() != 0) {
						GRMessageUtils.sendMsg2GroupAllGameServer(
								indigoService.genGRIndigoRsp(0, GRFinalRaceNotify.MsgID.eMsgID_VALUE, builder.build().toByteString()),groupId);
					}
				}
			}
		} else {
			if (indigoService.getSession() > 5) {
				GRFinalRaceNotify.Builder builder = GRFinalRaceNotify.newBuilder();
				Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
				for (int i = 6; i <= indigoService.getSession(); i++) {// 最后三轮,总共八轮
					if (indigoService.getRaceMap().get(i) == null) {
						continue;
					}
					for (SingleRace singleRace : indigoService.getRaceMap().get(i).get(groupId).values()) {
						builder.addFinalRace(singleRace.genFinalBuilder());
					}
				}
				if (builder.getFinalRaceCount() != 0) {
					GRMessageUtils.sendMsg2GameServer(serverId,
							indigoService.genGRIndigoRsp(0, GRFinalRaceNotify.MsgID.eMsgID_VALUE, builder.build().toByteString()));
				}
			}
		}
	}
}
