package logic.crossArena;

import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.ConCrossChallengeRsp;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;

import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRAConCrossChallegeRsp;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CrossConChallengeRspScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRAConCrossChallegeRsp getData = (GRAConCrossChallegeRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		try {
			ConCrossChallengeRsp.Builder builder = ConCrossChallengeRsp.newBuilder();
			builder.setResult(getData.getResult());

			FighterInfo selfb = FighterInfo.parseFrom(getData.getSelf());
			builder.setSelf(selfb);
			FighterInfo enemyb = FighterInfo.parseFrom(getData.getEnemy());
			builder.setEnemy(enemyb);
			builder.setArenaScore(getData.getArenaScore());
			builder.setDayChallengeNum(getData.getDayChallengeNum());
			rewardAfterWin(player,builder);
			builder.setBuyDayChallengeNum(getData.getBuyDayChallengeNum());
			// Đẩy kết quả thách đấu
			MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.ConCrossChallengeRspID.ConCrossChallengeRspMsgID_VALUE,
					builder.build().toByteArray()));

		}
		catch (Exception ex)
		{
			System.out.printf("CrossConChallengeRspScript",ex.toString());
		}


		return null;
	}
	/**
	 * 胜利后奖励
	 *
	 * @param player
	 * @param builder
	 */
	private void rewardAfterWin(Player player, ConCrossChallengeRsp.Builder builder) {
		List<Item> items = new ArrayList<>();
		// 奖励道具
		items.addAll(BeanFactory.createProps(-1, player.getLevel() * 24 * 5));// 金币
		items.addAll(BeanFactory.createProps(-6, 0));// 精灵经验
		items.addAll(BeanFactory.createProps(-7, 0));// 队伍经验
		for (Item item : items) {
			RewardItemMsg.Builder riBuilder = RewardItemMsg.newBuilder();
			riBuilder.setItemId(item.getId());
			riBuilder.setItemNum(item.getNum());
			builder.addItemList(riBuilder);
		}
		// 翻牌方式
		int dropId = BeanTemplet.getGlobalBean(92).getInt_value();
		t_dropBean dropBean = BeanTemplet.getDropBean(dropId);
		for (int i = 0; i < 5; i++) {
			List<Item> dropItem = DropService.getDropItems(player.getDropManager(), dropId);
			items.add(dropItem.get(0));
			String[] drops = StringUtils.split(dropBean.getItem(),";");
			for (int j = 0; j < drops.length; j++) {
				if (drops[j].contains(dropItem.get(0).getId() + "," + dropItem.get(0).getNum())) {
					builder.addDropIndex(j);
					break;
				}
			}
		}
		// 发放
		player.getBackpackManager().addItems(items, true, true, Reason.ARENA_CHALLENGE, "");
	}
}
