package indigo;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import Message.S2CIndigoMsg.SingleFinalRaceMsg;
import Message.S2CIndigoMsg.SingleRankMsg;
import Message.Inner.GRCrossIndigo.GRGetDataReq;
import Message.Inner.GRCrossIndigo.GRGetDataRsp;
import game.core.pub.script.IScript;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SingleRace;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
/**
 * 获取三国霸主信息
 * <AUTHOR>
 *
 * 2018年10月9日
 */
public class GetIndigoDataScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		GRGetDataReq req = (GRGetDataReq) args.get(Key.ARG2);
		getData(serverId, req);
		return null;
	}

	/** 获取信息 */
	public void getData(int serverId, GRGetDataReq req) {
		IndigoService indigoService = IndigoService.getInstance();
		int groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		GRGetDataRsp.Builder builder = GRGetDataRsp.newBuilder();
		// 前3名
		int num = 0;
		List<SingleRankMsg> list = indigoService.getRankList().get(groupId);
		if (null != list) {
			for (SingleRankMsg singleRankMsg : list) {
				builder.addTop3(singleRankMsg);
				num++;
				if (num >= 3) {
					break;
				}
			}
		}
		// 上次决赛信息
		if (!indigoService.getLastfinalRaceList().isEmpty()) {
			if (null != indigoService.getLastfinalRaceList().get(groupId)) {
				for (SingleFinalRaceMsg race : indigoService.getLastfinalRaceList().get(groupId)) {
					builder.addFinalRace(race);
				}
			}
		}
		// 决赛信息
		if (indigoService.getSession() > 5) {
			builder.clearFinalRace();
			for (int i = 6; i <= indigoService.getSession(); i++) {// 最后三轮,总共八轮
				if (indigoService.getRaceMap().get(i) == null) {
					continue;
				}
				for (SingleRace singleRace : indigoService.getRaceMap().get(i).get(groupId).values()) {
					builder.addFinalRace(singleRace.genFinalBuilder());
				}
			}
		}
		// 玩家自己的阵容信息
		Map<Long, Participant> map = indigoService.getParticipantMap().get(groupId);
		if (null == map) {
			map = new HashMap<>();
			indigoService.getParticipantMap().put(groupId, map);
		}
		if (map.containsKey(req.getPlayerId())) {
			Participant participant = indigoService.getParticipantByPlayerId(req.getPlayerId());
			for (Formation f : participant.getFormations()) {
				builder.addFormation(f.genBuilder());
			}
			for (Formation f : participant.getRecomFor()) {
				builder.addRecommendFormation(f.genBuilder());
			}			
			for (Hero hero : participant.getHeroList()) {
				builder.addHeros(hero.genHeroPos());
			}
		}
		Map<Integer, GameServer> map2 = ServerService.getInstance().getGameServerGroup().get(groupId);
		for (GameServer gameServer : map2.values()) {
			builder.addServerIds(gameServer.getName());
		}
		// 推送
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(req.getPlayerId(), GRGetDataRsp.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
