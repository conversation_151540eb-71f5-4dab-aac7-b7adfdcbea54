package indigo;

import java.util.List;

import Message.Inner.GRCrossIndigo.GRGetGuildInfoRsp;
import Message.Inner.GRCrossIndigo.GRGuildMsg;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.util.ScriptArgs;

/**
 * 更新公会信息
 * <AUTHOR>
 *
 */
public class UpdateGuildInfoScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRGetGuildInfoRsp req = (GRGetGuildInfoRsp) args.get(ScriptArgs.Key.ARG1);
		List<GRGuildMsg> guildsList = req.getGuildsList();
		IndigoService service = IndigoService.getInstance();
		for (GRGuildMsg grGuildMsg : guildsList) {
			service.updateGuildInfo(grGuildMsg.getGuildId(), grGuildMsg.getGuildName(), grGuildMsg.getChairManName(), grGuildMsg.getFlag());
		}
		return null;
	}
}
