Java HotSpot(TM) 64-Bit Server VM (25.40-b25) for windows-amd64 JRE (1.8.0_40-b25), built on Feb 10 2015 21:53:57 by "java_re" with MS VC++ 10.0 (VS2010)
Memory: 4k page, physical 16710568k(884108k free), swap 33419236k(11993268k free)
CommandLine flags: -XX:GCPauseIntervalMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./oom.hprof -XX:InitialHeapSize=536870912 -XX:MaxGCPauseMillis=50 -XX:MaxHeapSize=4277905408 -XX:MaxNewSize=536870912 -XX:NewSize=536870912 -XX:+PrintGC -XX:+PrintGCTimeStamps -XX:SurvivorRatio=6 -XX:ThreadStackSize=256 -XX:+UnlockExperimentalVMOptions -XX:+UseCompressedClassPointers -XX:+UseCompressedOops -XX:+UseG1GC -XX:-UseLargePagesIndividualAllocation 
2.346: [GC pause (Metadata GC Threshold) (young) (initial-mark) 203M->20M(512M), 0.0203483 secs]
2.367: [GC concurrent-root-region-scan-start]
2.371: [GC concurrent-root-region-scan-end, 0.0045732 secs]
2.371: [GC concurrent-mark-start]
2.516: [GC concurrent-mark-end, 0.0003461 secs]
2.516: [GC remark, 0.0023806 secs]
2.667: [GC cleanup 44M->43M(512M), 0.0004443 secs]
