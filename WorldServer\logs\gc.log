Java HotSpot(TM) 64-Bit Server VM (25.181-b13) for windows-amd64 JRE (1.8.0_181-b13), built on Jul  7 2018 04:01:33 by "java_re" with MS VC++ 10.0 (VS2010)
Memory: 4k page, physical 16710568k(2975816k free), swap 33419236k(9165332k free)
CommandLine flags: -XX:GCPauseIntervalMillis=200 -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./oom.hprof -XX:InitialHeapSize=134217728 -XX:MaxGCPauseMillis=50 -XX:MaxHeapSize=4277905408 -XX:MaxNewSize=134217728 -XX:NewSize=134217728 -XX:+PrintGC -XX:+PrintGCTimeStamps -XX:SurvivorRatio=6 -XX:ThreadStackSize=256 -XX:+UnlockExperimentalVMOptions -XX:+UseCompressedClassPointers -XX:+UseCompressedOops -XX:+UseG1GC -XX:-UseLargePagesIndividualAllocation 
1.117: [GC pause (G1 Evacuation Pause) (young) 126M->12M(141M), 0.0105471 secs]
1.163: [GC pause (G1 Evacuation Pause) (young) 127M->14M(143M), 0.0063347 secs]
1.203: [GC pause (G1 Evacuation Pause) (young) 139M->14M(144M), 0.0011773 secs]
1.239: [GC pause (G1 Evacuation Pause) (young) 138M->12M(144M), 0.0022122 secs]
1.599: [GC pause (G1 Evacuation Pause) (young) 143M->20M(153M), 0.0190070 secs]
2.400: [GC pause (G1 Evacuation Pause) (young) 139M->23M(153M), 0.0112916 secs]
2.884: [GC pause (G1 Evacuation Pause) (young) 151M->25M(167M), 0.0110690 secs]
3.328: [GC pause (G1 Evacuation Pause) (young) 166M->25M(181M), 0.0135985 secs]
3.835: [GC pause (G1 Evacuation Pause) (young) 180M->22M(193M), 0.0092851 secs]
4.394: [GC pause (G1 Evacuation Pause) (young) 183M->21M(196M), 0.0058176 secs]
5.231: [GC pause (G1 Evacuation Pause) (young) 156M->27M(196M), 0.0091272 secs]
5.298: [GC pause (Metadata GC Threshold) (young) (initial-mark) 56M->25M(196M), 0.0061352 secs]
5.304: [GC concurrent-root-region-scan-start]
5.311: [GC concurrent-root-region-scan-end, 0.0069506 secs]
5.311: [GC concurrent-mark-start]
5.453: [GC concurrent-mark-end, 0.0081953 secs]
5.453: [GC remark, 0.0024351 secs]
5.500: [GC pause (G1 Evacuation Pause) (young) 139M->24M(196M), 0.0063844 secs]
5.605: [GC cleanup 103M->103M(196M), 0.0003230 secs]
5.644: [GC pause (G1 Evacuation Pause) (young) (initial-mark) 138M->25M(196M), 0.0048956 secs]
5.649: [GC concurrent-root-region-scan-start]
5.655: [GC concurrent-root-region-scan-end, 0.0062574 secs]
5.655: [GC concurrent-mark-start]
5.796: [GC pause (G1 Evacuation Pause) (young)5.799: [GC concurrent-mark-end, 0.0083621 secs]
 144M->25M(196M), 0.0063411 secs]
5.803: [GC remark, 0.0027944 secs]
5.955: [GC cleanup 85M->85M(196M), 0.0003445 secs]
6.734: [GC pause (G1 Evacuation Pause) (young) 149M->33M(196M), 0.0090458 secs]
7.794: [GC pause (G1 Evacuation Pause) (young) 138M->41M(196M), 0.0142137 secs]
