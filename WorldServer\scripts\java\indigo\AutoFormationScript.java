package indigo;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import data.bean.t_crosspvpBean;
import game.core.pub.script.IScript;
import game.route.hero.bean.Hero;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.HeroPos;
import game.route.indigo.bean.Participant;
import game.route.util.ScriptArgs;
import game.util.BeanTemplet;

/**
 * 自动编队
 * <AUTHOR>
 *
 */
public class AutoFormationScript implements IScript {
	private final Logger logger = Logger.getLogger(AutoFormationScript.class);

	
	@Override
	public void init() {
	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Participant participant = (Participant) args.get(ScriptArgs.Key.ARG1);
		return autoFormation(participant);
	}

	/**
	 * 自动编队
	 * 
	 * @param participant
	 */
	private boolean autoFormation(Participant participant) {

		int firstMin = 0;
		int firstMax = 0;
		int[] firstPos = null;
		int secondMin = 0;
		int secondMax = 0;
		int[] secondPos = null;
		int thirdMin = 0;
		int thirdMax = 0;
		int[] thirdPos = null;
		// 获取配置的人数上限与下限
		t_crosspvpBean crossBean = BeanTemplet.getCrossBean(3);
		String[] first = crossBean.getFirst().split("\\|");
		firstMin = Integer.valueOf(first[0].split("_")[0]);
		firstMax = Integer.valueOf(first[0].split("_")[1]);
		String[] tempArr = first[1].split(",");
		firstPos = new int[tempArr.length];
		for (int i = 0; i < tempArr.length; i++) {
			firstPos[i] = Integer.valueOf(tempArr[i]);
		}
		first = crossBean.getFirst().split("\\|");
		secondMin = Integer.valueOf(first[0].split("_")[0]);
		secondMax = Integer.valueOf(first[0].split("_")[1]);
		tempArr = first[1].split(",");
		secondPos = new int[tempArr.length];
		for (int i = 0; i < tempArr.length; i++) {
			secondPos[i] = Integer.valueOf(tempArr[i]);
		}
		first = crossBean.getFirst().split("\\|");
		thirdMin = Integer.valueOf(first[0].split("_")[0]);
		thirdMax = Integer.valueOf(first[0].split("_")[1]);
		tempArr = first[1].split(",");
		thirdPos = new int[tempArr.length];
		for (int i = 0; i < tempArr.length; i++) {
			thirdPos[i] = Integer.valueOf(tempArr[i]);
		}
		
		
		List<Hero> heros = new ArrayList<>(participant.getHeroList());
		participant.getFormations().clear();
		participant.getRecomFor().clear();
		Formation formation = null;
		int num = 0;
		for (int i = 0; i < 3; i++) {
			if (num == heros.size()) { // 已经分配完
				break;
			}
			int min = 0;
			int max = 0;
			int[] pos = null;
			if (i == 0) {
				min = firstMin;
				max = firstMax;
				pos = firstPos;
			} else if (i == 1) {
				min = secondMin;
				max = secondMax;
				pos = secondPos;
			} else if (i == 2) {
				min = thirdMin;
				max = thirdMax;
				pos = thirdPos;
			}
			formation = new Formation();
			formation.setPlayerId(participant.getPlayerId());
			for (int j = 0; j < max; j++) {
				if (num == heros.size()) { // 已经分配完
					break;
				}
				Hero hero = heros.get(num++);
				HeroPos heroPos = new HeroPos();
				heroPos.setHeroId(hero.getId());
				heroPos.setLvl(hero.getLevel());
				heroPos.setStar(hero.getStar());
				heroPos.setPower(hero.getPower());
				heroPos.setPosition(pos[j]);
				formation.getInfoList().add(heroPos);
			}
			if (formation.getInfoList().size() < min) {
				logger.error("---------------自动阵型错误,无法满足配置需求!-----------------");
				return false;
			}
			int offensive = (int) Math
					.ceil(formation.getPower() * ((float) BeanTemplet.getGlobalBean(66).getInt_value() / 10000)
							+ (float) participant.getOffensiveAdd() * formation.getInfoList().size() / 6);
			formation.setOffensive(offensive);
			participant.getFormations().add(formation);
			participant.getRecomFor().add(formation);
		}
		return true;
	}
}
