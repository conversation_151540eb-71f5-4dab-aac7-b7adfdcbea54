package suffle;

import Message.S2CCrossSuffleMsg.SufflePlayerMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import Message.Inner.GRCrossSuffleMsg.GRSufflePInfoRsp;
import game.core.pub.script.IScript;
import game.route.suffle.SuffleService;
import game.route.util.ScriptArgs;

/**
 * 向gameServer获取玩家详细信息
 * <AUTHOR>
 *
 */
public class PlayerInfoScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRSufflePInfoRsp grsp = (GRSufflePInfoRsp) args.get(ScriptArgs.Key.ARG1);
		//int serverId = (int) args.get(ScriptArgs.Key.ARG2);
		ByteString rsp = grsp.getRsp();
		SufflePlayerMsg info = null;
		try {
			info = SufflePlayerMsg.parseFrom(rsp);
		} catch (InvalidProtocolBufferException e) {
			e.printStackTrace();
		}
		SuffleService.getInstance().ResigePlayer(info.getServerId(),info.getPlayerId(),info);
		return null;
	}
}
