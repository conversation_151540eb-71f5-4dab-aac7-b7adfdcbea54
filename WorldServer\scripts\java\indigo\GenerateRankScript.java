package indigo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import Message.S2CIndigoMsg.IndigoGuildRankMsg;
import Message.S2CIndigoMsg.IndigoGuildRankMsg.Builder;
import Message.S2CIndigoMsg.SingleRankMsg;
import game.core.pub.script.IScript;
import game.route.db.DatabaseProcessor;
import game.route.hero.HeroService;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Participant;
import game.route.indigo.bean.SimpleGuildInfo;
import game.route.indigo.handler.InnerSaveGuildRankHandler;
import game.route.indigo.handler.InnerSaveRankHandler;
import game.route.server.ServerService;

/**
 * 生成排行榜
 * 
 * <AUTHOR>
 *
 *         2018年9月28日
 */
public class GenerateRankScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		IndigoService indigoService = IndigoService.getInstance();
		Map<Integer, List<SingleRankMsg>> rankList1 = new ConcurrentHashMap<>();
		Map<Integer, List<IndigoGuildRankMsg>> guildRankList1 = new ConcurrentHashMap<>();
		Map<Integer, Map<Long, Participant>> participantMap = indigoService.getParticipantMap();
		for (Entry<Integer, Map<Long, Participant>> en : participantMap.entrySet()) {
			Integer groupId = en.getKey();
			Map<Long, Participant> value = en.getValue();
			List<SingleRankMsg> rankList = new ArrayList<>();
			if (value.size() <= 0) {
				return null;
			}
			List<Participant> rank = new ArrayList<>(value.values());// 初赛参赛池
			// 排序 胜场数 > 积分
			rank.sort((Participant o1, Participant o2) -> {
				if (o1.getWinNum() > o2.getWinNum()) {
					return -1;
				} else if (o1.getWinNum() < o2.getWinNum()) {
					return 1;
				} else {
					if (o1.getIntegral() > o2.getIntegral()) {
						return -1;
					} else if (o1.getIntegral() < o2.getIntegral()) {
						return 1;
					} else {
						return 0;
					}
				}
			});
			Map<Long, IndigoGuildRankMsg.Builder> guildRankMap = new HashMap<>();
			for (int i = 0; i < rank.size(); i++) {
				Participant participant = rank.get(i);
				participant.setRank(i + 1);
				SingleRankMsg.Builder builder = SingleRankMsg.newBuilder();
				builder.setPlayerId(participant.getPlayerId());
				builder.setName(participant.getName());
				builder.setSex(participant.getSex());
				builder.setRank(i + 1);
				builder.setWinNum(participant.getWinNum());
				builder.setLoseNum(participant.getLoseNum());
				builder.setIntegral(participant.getIntegral());
				builder.setHeroId(HeroService.getInstance().genHeroIdInfo(participant.getShowHeroId(), participant.getStar()));
				if (participant.getGuildId() != 0) {
					SimpleGuildInfo guildInfo = indigoService.getGuildInfos().get(participant.getGuildId());
					if (null == guildInfo) {
						continue;
					}
					builder.setGuildName(guildInfo.getGuildName());
					IndigoGuildRankMsg.Builder indigoGuildRankMsg;
					if (!guildRankMap.containsKey(participant.getGuildId())) {
						IndigoGuildRankMsg.Builder b = IndigoGuildRankMsg.newBuilder();
						b.setGuildId(participant.getGuildId());
						b.setGuildName(builder.getGuildName());
						b.setName(guildInfo.getChairManName());
						b.setFlag(guildInfo.getFlag());
						b.setServerName(ServerService.getInstance().getGameServerMap().get(participant.getServerId()).getName());
						b.setRank(0);
						b.setGrade(0);
						b.setWinNum(0);
						guildRankMap.put(b.getGuildId(), b);
					}
					indigoGuildRankMsg = guildRankMap.get(participant.getGuildId());
					indigoGuildRankMsg.setGrade(indigoGuildRankMsg.getGrade() + participant.getIntegral());
					indigoGuildRankMsg.setWinNum(indigoGuildRankMsg.getWinNum() + participant.getWinNum());
				}
				builder.setStar(participant.getStar());
				builder.setLvl(participant.getLvl());
				builder.setServerId(participant.getServerId());
				builder.setServerName(ServerService.getInstance().getGameServerMap().get(builder.getServerId()).getName());
				rankList.add(builder.build());
			}
			// 联盟排行
			List<IndigoGuildRankMsg.Builder> guildRank = new ArrayList<>(guildRankMap.values());// 初赛参赛池
			// 排序 胜场数 > 积分
			guildRank.sort((IndigoGuildRankMsg.Builder o1, IndigoGuildRankMsg.Builder o2) -> {
				if (o1.getWinNum() > o2.getWinNum()) {
					return -1;
				} else if (o1.getWinNum() < o2.getWinNum()) {
					return 1;
				} else {
					if (o1.getGrade() > o2.getGrade()) {
						return -1;
					} else if (o1.getGrade() < o2.getGrade()) {
						return 1;
					} else {
						return 0;
					}
				}
			});
			// 设置联盟排名
			for (int j = 0; j < guildRank.size(); j++) {
				Builder builder = guildRank.get(j);
				builder.setRank(j + 1);
			}
			List<IndigoGuildRankMsg> guildRankList = new ArrayList<>();
			for (IndigoGuildRankMsg.Builder indigoGuildRankMsg : guildRank) {
				guildRankList.add(indigoGuildRankMsg.build());
			}
			// 保存数据库
			
			DatabaseProcessor.getInstance().submitRequest(new InnerSaveRankHandler(rankList, groupId));
			rankList1.put(groupId, rankList);
			DatabaseProcessor.getInstance().submitRequest(new InnerSaveGuildRankHandler(guildRankList, groupId));
			guildRankList1.put(groupId, guildRankList);
		}

		// 标记排行榜奖励发送情况
		indigoService.setSendRankReward(false);
		// 设置新排行榜
		indigoService.setRankList(rankList1);
		indigoService.setGuildRankList(guildRankList1);
		/**
		 * 前3名推送,并且发送跑马灯消息
		 */
		indigoService.top3NotifyAll();

		return null;
	}

}
