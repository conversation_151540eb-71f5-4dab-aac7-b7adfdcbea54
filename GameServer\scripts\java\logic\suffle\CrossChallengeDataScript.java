package logic.suffle;
import Message.S2CCrossArenaMsg;
import game.core.pub.script.IScript;
import game.server.logic.player.Player;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.S2CFightMsg.FightDataRsp;
import Message.S2CFightMsg.FightDataRspID;
import Message.S2CArenaMsg.FighterInfo;
import Message.Inner.GRCrossSuffleMsg.GRSuffleChallengeRsp;
import Message.S2CCrossSuffleMsg.SuffleChallengeResultRsp;
import Message.S2CCrossSuffleMsg.SuffleChallengeResultRspID;


public class CrossChallengeDataScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRSuffleChallengeRsp getData = (GRSuffleChallengeRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		try {
			FighterInfo selfFi = FighterInfo.parseFrom(getData.getSelf());
			FighterInfo enemyFi = FighterInfo.parseFrom(getData.getEnemy());

			SuffleChallengeResultRsp.Builder builder = SuffleChallengeResultRsp.newBuilder();
			builder.setResult(getData.getResult());
			builder.setSelf(selfFi);
			builder.setEnemy(enemyFi);
			builder.setGetScore(getData.getGetScore());
			builder.setNowScore(getData.getMyScore());
			builder.setWinNum(getData.getWinNum());
			builder.setLoseNum(getData.getLostNum());
			// 推送挑战结果
			MessageUtils.send(player, player.getFactory().fetchSMessage(SuffleChallengeResultRspID.SuffleChallengeResultRspMsgID_VALUE,
					builder.build().toByteArray()));

			// 战斗内容推送
			FightDataRsp.Builder fBuilder = FightDataRsp.parseFrom(getData.getFightData()).toBuilder();
			MessageUtils.send(player, player.getFactory().fetchSMessage(FightDataRspID.FightDataRspMsgID_VALUE,
					fBuilder.build().toByteArray()));

		}
		catch (Exception ex)
		{
			System.out.printf("CrossSuffleChallengeData",ex.toString());
		}



		return null;
	}
}
