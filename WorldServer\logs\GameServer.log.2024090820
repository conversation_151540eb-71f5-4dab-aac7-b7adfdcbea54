[2024-09-08 20:00:00:777 INFO](SuffleService.java:219)发放单兵排行奖励
[2024-09-08 20:00:00:777 INFO](GetAllGuildInfoScript.java:67)请求更新所有公会信息完毕-----
[2024-09-08 20:00:00:780 INFO](IndigoService.java:934)==========准备阶段结束》》》》》》》》》》》》》》比赛阶段开始==========================
[2024-09-08 20:00:00:782 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第1轮准备开始================
[2024-09-08 20:00:00:783 INFO](CreateRacesScript.java:140)=============本地比赛第1轮分组完成
[2024-09-08 20:03:00:785 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第1轮战斗结束================
[2024-09-08 20:03:00:787 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:04:00:786 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第2轮准备开始================
[2024-09-08 20:04:00:786 INFO](CreateRacesScript.java:140)=============本地比赛第2轮分组完成
[2024-09-08 20:07:00:786 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第2轮战斗结束================
[2024-09-08 20:07:00:787 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:08:00:787 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第3轮准备开始================
[2024-09-08 20:08:00:788 INFO](CreateRacesScript.java:140)=============本地比赛第3轮分组完成
[2024-09-08 20:11:00:787 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第3轮战斗结束================
[2024-09-08 20:11:00:788 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:12:00:788 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第4轮准备开始================
[2024-09-08 20:12:00:788 INFO](CreateRacesScript.java:140)=============本地比赛第4轮分组完成
[2024-09-08 20:15:00:789 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第4轮战斗结束================
[2024-09-08 20:15:00:797 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:16:00:790 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第5轮准备开始================
[2024-09-08 20:16:00:790 INFO](CreateRacesScript.java:140)=============本地比赛第5轮分组完成
[2024-09-08 20:19:00:790 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第5轮战斗结束================
[2024-09-08 20:19:00:791 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:20:00:792 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第6轮准备开始================
[2024-09-08 20:20:00:792 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.CreateRacesScript
java.lang.NullPointerException
at indigo.CreateRacesScript.matchRaces(CreateRacesScript.java:97)
at indigo.CreateRacesScript.call(CreateRacesScript.java:49)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerCreateRacesHandler.action(InnerCreateRacesHandler.java:27)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:23:00:792 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第6轮战斗结束================
[2024-09-08 20:23:00:793 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:24:00:793 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第7轮准备开始================
[2024-09-08 20:24:00:794 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.CreateRacesScript
java.lang.NullPointerException
at indigo.CreateRacesScript.matchRaces(CreateRacesScript.java:119)
at indigo.CreateRacesScript.call(CreateRacesScript.java:49)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerCreateRacesHandler.action(InnerCreateRacesHandler.java:27)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:27:00:793 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第7轮战斗结束================
[2024-09-08 20:27:00:794 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:28:00:794 INFO](LaunchFightThreadScript.java:61)>>>>>>=============比赛第8轮准备开始================
[2024-09-08 20:28:00:795 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.CreateRacesScript
java.lang.NullPointerException
at indigo.CreateRacesScript.matchRaces(CreateRacesScript.java:119)
at indigo.CreateRacesScript.call(CreateRacesScript.java:49)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerCreateRacesHandler.action(InnerCreateRacesHandler.java:27)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

[2024-09-08 20:31:00:796 INFO](LaunchFightThreadScript.java:72)>>>>>>=============比赛第8轮战斗结束================
[2024-09-08 20:31:00:797 ERROR](ScriptManager.java:70)call script error! scriptId = indigo.StartRacesScript
java.lang.NullPointerException
at indigo.StartRacesScript.call(StartRacesScript.java:60)
at game.core.pub.script.ScriptJavaLoader.call(ScriptJavaLoader.java:107)
at game.core.pub.script.ScriptManager.call(ScriptManager.java:68)
at game.route.indigo.handler.InnerStartRacesHandler.action(InnerStartRacesHandler.java:26)
at game.core.pub.command.CommandProcessor.doCommand(CommandProcessor.java:142)
at game.core.pub.command.CommandProcessor$1.run(CommandProcessor.java:124)
at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
at java.util.concurrent.FutureTask.run(FutureTask.java:266)
at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
at java.lang.Thread.run(Thread.java:748)

