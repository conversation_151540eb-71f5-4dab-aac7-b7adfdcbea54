#============================================================================
# Configure Main Scheduler Properties  
#============================================================================

#org.quartz.scheduler.instanceName: QuartzScheduler
#org.quartz.scheduler.rmi.export: true
#org.quartz.scheduler.rmi.registryHost: localhost
#org.quartz.scheduler.rmi.registryPort: 11099
#org.quartz.scheduler.rmi.createRegistry: false

#org.quartz.scheduler.skipUpdateCheck: true

#============================================================================
# Configure ThreadPool  
#============================================================================

org.quartz.threadPool.class: org.quartz.simpl.SimpleThreadPool
org.quartz.threadPool.threadCount: 2
org.quartz.threadPool.threadPriority: 5

#============================================================================
# Configure JobStore  
#============================================================================

org.quartz.jobStore.misfireThreshold: 60000

org.quartz.jobStore.class: org.quartz.simpl.RAMJobStore
