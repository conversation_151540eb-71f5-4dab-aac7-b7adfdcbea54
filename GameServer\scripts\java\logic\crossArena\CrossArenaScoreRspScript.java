package logic.crossArena;

import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.BuyCrossDayChallengeRsp;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;

import data.bean.t_arena_integralBean;
import data.bean.t_cross_arena_integralBean;
import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import Message.S2CPlayerMsg.PromptType;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRACrossArenaScoreRsp;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class CrossArenaScoreRspScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRACrossArenaScoreRsp getData = (GRACrossArenaScoreRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		try {
			S2CCrossArenaMsg.CrossArenaScoreRewardRsp.Builder builder= S2CCrossArenaMsg.CrossArenaScoreRewardRsp.newBuilder();
			List<Item> items = new ArrayList<>();
			for(Integer getId:getData.getGetidsList())
			{
				t_cross_arena_integralBean integralBean = BeanTemplet.getCrossArenaIntegralBean(getId);
				if (integralBean == null) {
					break;
				}
				// 奖励
				String[] drops =  StringUtils.split(integralBean.getDrop(),";");
				for (String d : drops) {
					String[] dlist= StringUtils.split(d,",");
					items.addAll(BeanFactory.createProps(Integer.parseInt(dlist[0]),
							Integer.parseInt(dlist[1])));
				}
			}
			// 发送
			player.getBackpackManager().addItems(items, true, true, Reason.ARENA_SCORE_REWARD, "");
			for (Item item : items) {
				RewardItemMsg.Builder riBuilder = RewardItemMsg.newBuilder();
				riBuilder.setItemId(item.getId());
				riBuilder.setItemNum(item.getNum());
				builder.addItemList(riBuilder);
			}
			builder.addAllScoreReward(getData.getScoreRewardList());
			// 推送结果
			MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.CrossArenaScoreRewardRspID.CrossArenaScoreRewardRspMsgID_VALUE,
					builder.build().toByteArray()));

		}
		catch (Exception ex)
		{
			System.out.printf("CrossArenaScoreRspScript",ex.toString());
		}


		return null;
	}
}
