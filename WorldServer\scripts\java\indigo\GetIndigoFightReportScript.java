package indigo;

import Message.Inner.GRCrossIndigo.GRGetIndigoFightReportReq;
import Message.Inner.GRCrossIndigo.GRGetIndigoFightReportRsp;
import game.core.pub.script.IScript;
import game.fight.bean.FightReport;
import game.route.fight.cache.FightReportCacheService;
import game.route.indigo.IndigoService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
/**
 * 获取跨服三国霸主战报
 * <AUTHOR>
 *
 */
public class GetIndigoFightReportScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		IndigoService indigoService = IndigoService.getInstance();
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		GRGetIndigoFightReportReq req = (GRGetIndigoFightReportReq) args.get(Key.ARG2);
		GRGetIndigoFightReportRsp.Builder builder = GRGetIndigoFightReportRsp.newBuilder();
		builder.setPlayerId(req.getPlayerId());
		builder.setRaceId(req.getRaceId());
		builder.setReplayerId(req.getReplayerId());
		builder.setSession(req.getSession());
		FightReport report = FightReportCacheService.getInstance().getFightReport(req.getReplayerId());
		builder.setReport(report.buildFullInfo());
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(req.getPlayerId(), 
						GRGetIndigoFightReportRsp.MsgID.eMsgID_VALUE, builder.build().toByteString()));
		return null;
	}

}
