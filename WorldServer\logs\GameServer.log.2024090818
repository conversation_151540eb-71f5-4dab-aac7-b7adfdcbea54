[2024-09-08 18:15:21:341 ERROR](DbService.java:122)关服公会数据回存!!!!!!!!!!
[2024-09-08 18:15:21:362 ERROR](ServerService.java:206)停服，关闭所有服务器!!!
[2024-09-08 18:15:21:365 INFO](CommandProcessor.java:91)begin awaitStop()
[2024-09-08 18:15:21:368 INFO](CommandProcessor.java:93)end awaitStop()
[2024-09-08 18:15:21:383 INFO](CommandProcessor.java:91)begin awaitStop()
[2024-09-08 18:15:21:384 INFO](CommandProcessor.java:93)end awaitStop()
[2024-09-08 18:15:21:385 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-08 18:15:21:443 ERROR](IndigoDataDao.java:31)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.io.EOFException
MESSAGE: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.

STACKTRACE:

java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:1997)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:2411)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:2916)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1631)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.IndigoDataDao.insert(IndigoDataDao.java:27)
	at game.route.indigo.IndigoService.saveIndigoData(IndigoService.java:1288)
	at game.route.indigo.IndigoService.saveAll(IndigoService.java:1214)
	at game.route.indigo.IndigoService.stopAndAwaitStop(IndigoService.java:1192)
	at game.route.RouteServer.stop(RouteServer.java:104)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 36 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.io.EOFException
MESSAGE: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.

STACKTRACE:

java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:1997)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:2411)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:2916)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1631)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.IndigoDataDao.insert(IndigoDataDao.java:27)
	at game.route.indigo.IndigoService.saveIndigoData(IndigoService.java:1288)
	at game.route.indigo.IndigoService.saveAll(IndigoService.java:1214)
	at game.route.indigo.IndigoService.stopAndAwaitStop(IndigoService.java:1192)
	at game.route.RouteServer.stop(RouteServer.java:104)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 36 ms ago.
[2024-09-08 18:15:21:446 ERROR](IndigoDataDao.java:31)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.io.EOFException
MESSAGE: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.

STACKTRACE:

java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:1997)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:2411)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:2916)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1631)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.IndigoDataDao.insert(IndigoDataDao.java:27)
	at game.route.indigo.IndigoService.saveFinalList(IndigoService.java:1315)
	at game.route.indigo.IndigoService.saveAll(IndigoService.java:1220)
	at game.route.indigo.IndigoService.stopAndAwaitStop(IndigoService.java:1192)
	at game.route.RouteServer.stop(RouteServer.java:104)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 1 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.io.EOFException
MESSAGE: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.

STACKTRACE:

java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.jdbc.MysqlIO.readFully(MysqlIO.java:1997)
	at com.mysql.jdbc.MysqlIO.reuseAndReadPacket(MysqlIO.java:2411)
	at com.mysql.jdbc.MysqlIO.checkErrorPacket(MysqlIO.java:2916)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1631)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.IndigoDataDao.insert(IndigoDataDao.java:27)
	at game.route.indigo.IndigoService.saveFinalList(IndigoService.java:1315)
	at game.route.indigo.IndigoService.saveAll(IndigoService.java:1220)
	at game.route.indigo.IndigoService.stopAndAwaitStop(IndigoService.java:1192)
	at game.route.RouteServer.stop(RouteServer.java:104)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 1 ms ago.
[2024-09-08 18:15:21:446 INFO](ArenaService.java:1239)存储跨服竞技场数据
[2024-09-08 18:15:21:446 INFO](CommandProcessor.java:91)begin awaitStop()
[2024-09-08 18:15:21:449 INFO](CommandProcessor.java:93)end awaitStop()
[2024-09-08 18:15:21:468 ERROR](ArenaDataDao.java:45)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.saveAplayers(ArenaService.java:276)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:435)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.saveAplayers(ArenaService.java:276)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:435)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
[2024-09-08 18:15:21:594 ERROR](ArenaDataDao.java:45)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.savePlayers(ArenaService.java:295)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:437)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.savePlayers(ArenaService.java:295)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:437)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
[2024-09-08 18:15:21:599 ERROR](ArenaDataDao.java:45)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.saveRanks(ArenaService.java:314)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:439)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 1 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.saveRanks(ArenaService.java:314)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:439)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 1 ms ago.
[2024-09-08 18:15:21:601 ERROR](ArenaDataDao.java:45)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.savePirateRanks(ArenaService.java:336)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:441)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.arena.ArenaService.savePirateRanks(ArenaService.java:336)
	at game.route.arena.ArenaService.saveAll(ArenaService.java:441)
	at game.route.arena.ArenaService.stopAndAwaitStop(ArenaService.java:1241)
	at game.route.RouteServer.stop(RouteServer.java:105)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
[2024-09-08 18:15:21:601 INFO](ArenaService.java:1242)存储跨服竞技场数据完成
[2024-09-08 18:15:21:601 INFO](SuffleService.java:909)存储单兵数据
[2024-09-08 18:15:21:601 INFO](CommandProcessor.java:91)begin awaitStop()
[2024-09-08 18:15:21:602 INFO](CommandProcessor.java:93)end awaitStop()
[2024-09-08 18:15:21:603 ERROR](ArenaDataDao.java:45)org.apache.ibatis.exceptions.PersistenceException: 
### Error updating database.  Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.suffle.SuffleService.saveAll(SuffleService.java:298)
	at game.route.suffle.SuffleService.stopAndAwaitStop(SuffleService.java:911)
	at game.route.RouteServer.stop(RouteServer.java:106)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
### Cause: org.apache.ibatis.transaction.TransactionException: Error configuring AutoCommit.  Your driver may not support getAutoCommit() or setAutoCommit(). Requested setting: false.  Cause: com.mysql.jdbc.CommunicationsException: Communications link failure due to underlying exception: 

** BEGIN NESTED EXCEPTION ** 

java.net.SocketException
MESSAGE: 断开的管道 (Write failed)

STACKTRACE:

java.net.SocketException: 断开的管道 (Write failed)
	at java.net.SocketOutputStream.socketWrite0(Native Method)
	at java.net.SocketOutputStream.socketWrite(SocketOutputStream.java:111)
	at java.net.SocketOutputStream.write(SocketOutputStream.java:155)
	at java.io.BufferedOutputStream.flushBuffer(BufferedOutputStream.java:82)
	at java.io.BufferedOutputStream.flush(BufferedOutputStream.java:140)
	at com.mysql.jdbc.MysqlIO.send(MysqlIO.java:2744)
	at com.mysql.jdbc.MysqlIO.sendCommand(MysqlIO.java:1612)
	at com.mysql.jdbc.MysqlIO.sqlQueryDirect(MysqlIO.java:1723)
	at com.mysql.jdbc.Connection.execSQL(Connection.java:3250)
	at com.mysql.jdbc.Connection.setAutoCommit(Connection.java:5395)
	at sun.reflect.GeneratedMethodAccessor21.invoke(Unknown Source)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.apache.ibatis.datasource.pooled.PooledConnection.invoke(PooledConnection.java:246)
	at com.sun.proxy.$Proxy5.setAutoCommit(Unknown Source)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.setDesiredAutoCommit(JdbcTransaction.java:100)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.openConnection(JdbcTransaction.java:138)
	at org.apache.ibatis.transaction.jdbc.JdbcTransaction.getConnection(JdbcTransaction.java:61)
	at org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:279)
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:72)
	at org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:47)
	at org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:105)
	at org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:71)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:152)
	at org.apache.ibatis.session.defaults.DefaultSqlSession.insert(DefaultSqlSession.java:141)
	at game.route.db.dao.ArenaDataDao.insertOrUpdateOne(ArenaDataDao.java:39)
	at game.route.suffle.SuffleService.saveAll(SuffleService.java:298)
	at game.route.suffle.SuffleService.stopAndAwaitStop(SuffleService.java:911)
	at game.route.RouteServer.stop(RouteServer.java:106)
	at game.route.RouteServer.access$000(RouteServer.java:39)
	at game.route.RouteServer$1.run(RouteServer.java:93)
	at java.lang.Thread.run(Thread.java:748)


** END NESTED EXCEPTION **



Last packet sent to the server was 0 ms ago.
[2024-09-08 18:15:21:603 INFO](SuffleService.java:912)存储单兵数据完成
[2024-09-08 18:16:33:846 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-08 18:16:35:965 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-08 18:16:36:110 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-08 18:16:36:111 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-08 18:16:39:992 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-08 18:16:43:109 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-08 18:16:43:110 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-08 18:16:43:576 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-08 18:16:43:579 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-08 18:16:43:742 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-08 18:16:44:689 INFO](IndigoService.java:812)今日  跨服三国霸主   决赛阶段------------------------比赛日
[2024-09-08 18:16:45:732 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-08 18:16:45:738 INFO](IndigoService.java:913)==========空闲阶段结束》》》》》》》》》》》》报名阶段开始==========================
[2024-09-08 18:17:11:058 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:*************,cmd:2
[2024-09-08 18:17:14:131 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/*************:39001
[2024-09-08 18:17:14:132 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-08 18:17:14:156 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-08 18:17:14:183 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
