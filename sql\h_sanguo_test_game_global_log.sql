/*
 Navicat Premium Dump SQL

 Source Server         : game
 Source Server Type    : MySQL
 Source Server Version : 50553 (5.5.53)
 Source Host           : localhost:3306
 Source Schema         : h_sanguo_test_game_global_log

 Target Server Type    : MySQL
 Target Server Version : 50553 (5.5.53)
 File Encoding         : 65001

 Date: 02/03/2025 13:10:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_recharge_log
-- ----------------------------
DROP TABLE IF EXISTS `t_recharge_log`;
CREATE TABLE `t_recharge_log`  (
  `log_date` datetime NOT NULL COMMENT '日志时间',
  `uid` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户ID',
  `pid` bigint(20) NULL DEFAULT NULL COMMENT '玩家ID',
  `cp_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品ID',
  `channel_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道ID',
  `server_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务器ID',
  `order_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `money` double NULL DEFAULT NULL COMMENT '金额',
  `currency_type` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货币类型(如：USD)',
  `currency_unit` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '货币单位(如:元)',
  `item_count` int(11) NULL DEFAULT NULL COMMENT '发放道具数量(一般为钻石)',
  PRIMARY KEY (`log_date`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '充值日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for t_role_register_log
-- ----------------------------
DROP TABLE IF EXISTS `t_role_register_log`;
CREATE TABLE `t_role_register_log`  (
  `log_date` datetime NULL DEFAULT NULL COMMENT '日志时间',
  `uid` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户ID',
  `pid` bigint(20) NULL DEFAULT NULL COMMENT '玩家ID',
  `channel_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '渠道ID',
  `server_id` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务器ID',
  `imei` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '设备号',
  `os_type` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统类型',
  `os_ver` varchar(45) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '系统版本'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色注册日志' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
