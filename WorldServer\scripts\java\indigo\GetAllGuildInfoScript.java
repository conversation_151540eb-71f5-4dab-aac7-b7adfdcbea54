package indigo;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.log4j.Logger;

import Message.Inner.GRCrossIndigo.GRGetGuildInfoReq;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Participant;
import game.route.util.GRMessageUtils;

/**
 * 请求更新所有公会信息
 * <AUTHOR>
 *
 */
public class GetAllGuildInfoScript implements IScript {
	private final Logger logger = Logger.getLogger(GetAllGuildInfoScript.class);

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {

		getallGuildInfo();
		
		return null;
	}

	/**
	 * 请求更新所有公会信息
	 * 
	 * @param final4Pars
	 */
	private void getallGuildInfo() {
		IndigoService service = IndigoService.getInstance();
		Map<Integer, Map<Long, Participant>> participantMap = service.getParticipantMap();
		for (Entry<Integer, Map<Long, Participant>> en : participantMap.entrySet()) {
			Integer groupId = en.getKey();
			Map<Long, Participant> map = en.getValue();
			Map<Long, Long> guildIds = new HashMap<>();
			for (Participant p : map.values()) {
				if (!guildIds.containsKey(p.getGuildId()) && p.getGuildId() != 0) {
					guildIds.put(p.getGuildId(), p.getGuildId());
				}
			}
			GRGetGuildInfoReq.Builder builder = GRGetGuildInfoReq.newBuilder();
			for (Long guildId : guildIds.keySet()) {
				builder.addGuildIds(guildId);
			}
			GRMessageUtils.sendMsg2GroupAllGameServer(service.genGRIndigoRsp(0, GRGetGuildInfoReq.MsgID.eMsgID_VALUE, builder.build().toByteString()), groupId);
			logger.info("请求更新群组: " + groupId + " 完毕-----");
		}
		logger.info("请求更新所有公会信息完毕-----");

	}
}
