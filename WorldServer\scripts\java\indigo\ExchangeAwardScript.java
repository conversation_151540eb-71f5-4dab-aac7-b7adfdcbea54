package indigo;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;

import com.qiniu.streaming.model.ActivityRecords.Item;

import Message.S2CIndigoMsg.ExchangeAwardRsp;
import data.bean.t_quartz_shopBean;
import game.core.pub.script.IScript;
import game.route.server.domain.GameServer;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;
import game.util.BeanTemplet;

/**
 * 
 * 兑换奖励
 * 
 * <AUTHOR>
 *
 * 2018年10月9日
 */
public class ExchangeAwardScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		long playerId = (long) args.get(Key.ARG1);
		GameServer server = (GameServer) args.get(Key.ARG2);
		int id = (int) args.get(Key.ARG3);
		int num = (int) args.get(Key.ARG4);

		exchangeAward(playerId, id, num, server);
		return null;
	}

	/**
	 * 兑换奖励
	 * 
	 * @param id
	 * @param num
	 */
	public void exchangeAward(long playerId, int id, int num, GameServer server) {
		t_quartz_shopBean quartz_shopBean = BeanTemplet.getQuartzShopBean(id);
		if (quartz_shopBean == null) {
			// MessageUtils.sendPrompt(player, PromptType.WARNING, 129);
			return;
		}
		String[] needItemStr = StringUtils.split(quartz_shopBean.getProp_id(), ";");
		List<Item> needItems = new ArrayList<>();
		// for (String str : needItemStr) {
		// String[] items = StringUtils.split(str, ",");
		// needItems.addAll(BeanFactory.createProps(Integer.parseInt(items[0]),
		// Integer.parseInt(items[1]) * num));
		// }
		// if (!player.getBackpackManager().isItemNumEnough(needItems)) {
		// MessageUtils.sendPrompt(player, PromptType.WARNING, 36);
		// return;
		// }
		// player.getBackpackManager().removeItems(needItems, true,
		// Reason.INDOGO_SHOP_EXCHAGE, "");

		String[] itemStr = StringUtils.split(quartz_shopBean.getExchange_hero(), ";");
		// List<Item> items = new ArrayList<>();
		// for (String str : itemStr) {
		// String[] rItems = StringUtils.split(str, ",");
		// items.addAll(BeanFactory.createProps(Integer.parseInt(rItems[0]),
		// Integer.parseInt(rItems[1]) * num));
		// }
		// player.getBackpackManager().addItems(items, Reason.INDOGO_SHOP_EXCHAGE, "");
		// 响应
		ExchangeAwardRsp.Builder builder = ExchangeAwardRsp.newBuilder();
		builder.setId(id);
		builder.setNum(num);
		// for (Item item : items) {
		// builder.addItems(item.genBuilder());
		// }
		// MessageUtils.send(player,
		// player.getFactory().fetchSMessage(ExchangeAwardRspID.ExchangeAwardRspMsgID_VALUE,
		// builder.build().toByteArray()));
		// TODO
	}
}
