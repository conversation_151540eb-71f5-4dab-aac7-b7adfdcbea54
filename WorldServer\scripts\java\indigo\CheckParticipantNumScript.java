package indigo;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang.math.RandomUtils;
import org.apache.log4j.Logger;

import Message.Inner.GRCrossIndigo.GRGenRobot;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Participant;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.GRMessageUtils;

/**
 * 判断参赛人数 并使用机器人填充补足
 * 
 * <AUTHOR>
 *
 *         2018年9月28日
 */
public class CheckParticipantNumScript implements IScript {
	private final Logger logger = Logger.getLogger(CheckParticipantNumScript.class);

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {

		createFinalRobot();
		
		return null;
	}

	/**
	 * 创建决赛机器人
	 * 
	 * @param final4Pars
	 */
	private void createFinalRobot() {
		Map<Integer, Map<Long, Participant>> gmap = IndigoService.getInstance().getParticipantMap();
		for (Entry<Integer, Map<Long, Participant>> en : gmap.entrySet()) {
			Integer groupId = en.getKey();
			Map<Long, Participant> pMap = en.getValue();
			int needRobotNum = IndigoService.NUM_LIMIT - pMap.size();
			if (needRobotNum <= 0) {
				logger.info("分组: " + groupId + " 参赛玩家数量达到   " + pMap.size() + "  ,无需生成石英决赛机器人完毕");
				return;
			}
			//参赛者最小等级
			int lowLvL = 80;
			for (Participant participant : pMap.values()) {
				if(lowLvL > participant.getLevel()) {
					lowLvL = participant.getLevel();
				}
			}
			
			// 分配各服务器生成三国霸主决赛机器人
			Map<Integer, GameServer> serverMap = ServerService.getInstance().getGameServerGroup().get(groupId);
			int serverNum = 0;
			for (GameServer server : serverMap.values()) {
				if (server.isConnected()) {
					serverNum++;
				}
			}
			if (needRobotNum < 50) {
				// 随机一个服生成全部机器人
				int index = RandomUtils.nextInt(serverNum);
				Integer[] serverIdArray = serverMap.keySet().stream().toArray(Integer[]::new);
				GameServer targetServer = serverMap.get(serverIdArray[index]);
				// 发送生成机器人请求
				sendGameServerGenRobot(needRobotNum, lowLvL, targetServer);
				logger.info("分组: " + groupId + " 请求  生成石英决赛机器人      服务器  >>" + targetServer.getId() + "  发送完毕------数量:" + needRobotNum);
			} else {
				int perNum = needRobotNum / serverNum;
				int leftNum = needRobotNum % serverNum;
				Map<Integer, Integer> sumMap = new HashMap<>();
				serverMap.values().stream().forEach(m -> {
					if (m.isConnected()) {
						sumMap.put(m.getId(), perNum);
					}
				});
				if (leftNum > 0) {
					// 随机一个服务器生成剩余的
					int index = RandomUtils.nextInt(serverNum);
					Integer[] serverIdArray = serverMap.keySet().stream().toArray(Integer[]::new);
					sumMap.merge(serverIdArray[index], leftNum, (m, n) -> m + n);
				}
				//遍历服务器发送生成机器人请求
				for (Entry<Integer,Integer> entry : sumMap.entrySet()) {
					GameServer targetServer = serverMap.get(entry.getKey());
					// 发送生成机器人请求
					sendGameServerGenRobot(entry.getValue(), lowLvL, targetServer);
					logger.info("分组: " + groupId + " 请求  生成石英决赛机器人      服务器  >>" + targetServer.getId() + "  发送完毕------数量:" + entry.getValue());
				}
			}
			logger.info("分组: " + groupId + " 请求生成石英决赛机器人完毕------总数量:" + needRobotNum);
		}
	}
	/**
	 * 发送服务器生成机器人
	 * @param needRobotNum
	 * @param lowLvL
	 * @param targetServer
	 */
	private void sendGameServerGenRobot(int needRobotNum, int lowLvL, GameServer targetServer) {
		GRGenRobot.Builder builder = GRGenRobot.newBuilder();
		builder.setNum(needRobotNum);
		builder.setLevel(lowLvL);

		GRMessageUtils.sendMsg2GameServer(targetServer.getId(),
				IndigoService.getInstance().genGRIndigoRsp(0, GRGenRobot.MsgID.eMsgID_VALUE, builder.build().toByteString()));
	}
}
