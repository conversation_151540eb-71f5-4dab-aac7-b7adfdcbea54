<?php 
include_once 'config.php';
$token='IwY#UGJqJWxB8xZs';
?>
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>Trang quản trị</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
  <link rel="stylesheet" href="layui/css/layui.css"  media="all">
  <link rel="stylesheet" href="layui/formSelects-v4.css" />
  <script src="layui/layui.all.js" type="text/javascript" charset="utf-8"></script>
  <script src="layui/lay/modules/layer.js" type="text/javascript" charset="utf-8"></script>
</head>
<body>
  
<?php 
switch($_POST['submit']){
	case 'Nạp tiền';
		$account=trim($_POST['account']);
		$money=trim($_POST['money']);
		$sid=trim($_POST['sid']);
		if(!$sid){
			echo "<script>alert('Vui lòng chọn khu vực game!');history.go(-1)</script>";
			exit;
		}		
		if(!$account){
			echo "<script>alert('Vui lòng nhập tên nhân vật!');history.go(-1)</script>";
			exit;
		}
		if(!$money){
			echo "<script>alert('Vui lòng chọn số tiền nạp!');history.go(-1)</script>";
			exit;
		}
		$sql="select * from t_s_server_list where zone_id='".$sid."'";
		$result=mysql_query($sql); 
		$row2=mysql_fetch_array($result);
		if(!$row2['id']){
			echo "<script>alert('Khu vực này không tồn tại!');history.go(-1)</script>";
			exit;			
		}	
		
		$sql="select * from {$serverlist[$sid]['table']}.t_player where name='".$account."'";
		$result=mysql_query($sql); 
		$row=mysql_fetch_array($result);
		if(!$row['playerId']){
			echo "<script>alert('Nhân vật không tồn tại, gửi thất bại!');history.go(-1)</script>";
			exit;	
		}
		
		$action='recharge';
		$playerid=$row['playerId'];
		$productid=$money;
		$data=array(
			"action"=>$action,
			"playerid"=>$playerid, 
			"productid"=>intval($productid),
			"token"=> $token
		);		
		$data=json_encode($data);
		$res=post_curl($serverlist[$sid]['url'], $data);
		echo "<script>alert('Nạp tiền thành công!');history.go(-1)</script>";
		exit;

		break;	

	case 'Thư cá nhân';
		$title=trim($_POST['title']);
		$context=trim($_POST['context']);
		$account=trim($_POST['account']);
		$item=trim($_POST['item']);
		$num=trim($_POST['num']);
		$item2=trim($_POST['item2']);
		$num2=trim($_POST['num2']);		
		$item3=trim($_POST['item3']);
		$num3=trim($_POST['num3']);		
		$item4=trim($_POST['item4']);
		$num4=trim($_POST['num4']);	
		$item5=trim($_POST['item5']);
		$num5=trim($_POST['num5']);		
		$sid=trim($_POST['sid']);
		if(!$sid){
			echo "<script>alert('Vui lòng chọn khu vực game!');history.go(-1)</script>";
			exit;
		}		
		if(!$account){
			echo "<script>alert('Vui lòng nhập tài khoản người chơi!');history.go(-1)</script>";
			exit;
		}
		if(!$title){
			echo "<script>alert('Vui lòng nhập tiêu đề thư!');history.go(-1)</script>";
			exit;			
		}
		if(!$context){
			echo "<script>alert('Vui lòng nhập nội dung thư!');history.go(-1)</script>";
			exit;			
		}		
		if(!$item){
			echo "<script>alert('Vui lòng chọn vật phẩm gửi!');history.go(-1)</script>";
			exit;
		}
		if(!$num){
			echo "<script>alert('Vui lòng nhập số lượng vật phẩm!');history.go(-1)</script>";
			exit;
		}
		if($item){
			if(!$num){
				echo "<script>alert('Vui lòng nhập số lượng vật phẩm!');history.go(-1)</script>";
				exit;
			}
			$items.=$item.'_'.$num;
		}
		
		if($item2){
			if(!$num2){
				echo "<script>alert('Vui lòng nhập số lượng vật phẩm!');history.go(-1)</script>";
				exit;
			}
			$items.=',';
			$items.=$item2.'_'.$num2;
		}		
		if($item3[0]){
			if(!$num3){
				echo "<script>alert('Vui lòng nhập số lượng vật phẩm!');history.go(-1)</script>";
				exit;
			}
			$items.=',';
			$items.=$item3.'_'.$num3;
		}
		if($item4[0]){
			if(!$num4){
				echo "<script>alert('Vui lòng nhập số lượng vật phẩm!');history.go(-1)</script>";
				exit;
			}
			$items.=',';
			$items.=$item4.'_'.$num4;
		}
		if($item5[0]){
			if(!$num5){
				echo "<script>alert('Vui lòng nhập số lượng vật phẩm!');history.go(-1)</script>";
				exit;
			}
			$items.=',';
			$items.=$item5.'_'.$num5;
		}			
		$sql="select * from t_s_server_list where zone_id='".$sid."'";
		$result=mysql_query($sql); 
		$row2=mysql_fetch_array($result);
		if(!$row2['id']){
			echo "<script>alert('Khu vực này không tồn tại!');history.go(-1)</script>";
			exit;			
		}	
		
		$sql="select * from {$serverlist[$sid]['table']}.t_player where name='".$account."'";
		$result=mysql_query($sql); 
		$row=mysql_fetch_array($result);
		if(!$row['playerId']){
			echo "<script>alert('Nhân vật không tồn tại, gửi thất bại!');history.go(-1)</script>";
			exit;	
		}		
		$action='mail';
		$playerid=$row['playerId'];
		$data=array(
			"action"=>$action,
			"playerid"=>$playerid, 
			"mail"=>array(
				"sender"=>"GM",
				"title"=>$title,
				"content"=>$context,
				"reward"=>$items
			),
			"token"=> $token
		);

		$data=json_encode($data);
		$res=post_curl($serverlist[$sid]['url'], $data);	
		//var_dump($data);exit;
		echo "<script>alert('Gửi thành công!');history.go(-1)</script>";
		exit;						
		break;	

		
}

?> 


  
             
<fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
  <legend>Chức năng hệ thống</legend>
</fieldset>

<form class="layui-form" method="post" action="">
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Khu vực game</label>
      <div class="layui-input-inline">
        <select name="sid">
			<option value="">Vui lòng chọn khu vực game</option>
		  <?php 
			$sql="select * from t_s_server_list";
			$result=mysql_query($sql);
			while($row=mysql_fetch_array($result)){				  
		  ?>
			<option value="<?php echo $row['zone_id'];?>"><?php echo $row['name'];?></option>
		  <?php }?>
        </select>
      </div>
    </div>
  </div>

  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Nhân vật người chơi</label>
      <div class="layui-input-inline">
        <input type="text" name="account" lay-verify="title" autocomplete="off" class="layui-input">
      </div>
    </div>
  </div>
  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Số tiền nạp</label>
      <div class="layui-input-inline">
        <select name="money" lay-verify="required" lay-search="">		
          <option value="">Vui lòng chọn số tiền nạp</option>
		  <?php 
			$sql="select * from h_sanguo_test_game_data.t_recharge";
			$result=mysql_query($sql);
			while($row=mysql_fetch_array($result)){				  
		  ?>
			<option value="<?php echo $row['id'];?>"><?php echo $row['desc'];?>-----<?php echo $row['price'];?>元</option>
		  <?php }?>			

        </select>
      </div>
    </div>
  </div>  
    
  <div class="layui-form-item">
    <div class="layui-input-block">
	  <input type="submit" class="layui-btn" name="submit" value='Nạp tiền'>
	     <button type="submit" class="btn btn-primary btn-block" onclick="window.location.href = 'https://www.baidu.com/link?url=RqTH9xazZ5-LtPAa_xs1TNHXHQ2rg0HQddwECzcdY8y&wd=&eqid=e8d31c3c000073a200000004652a9920'">Vào trang tài nguyên Aier</button>
   <button type="submit" class="btn btn-primary btn-block" onclick="window.location.href = 'https://jq.qq.com/?_wv=1027&k=jbXytgrY'">Vào nhóm phúc lợi công ích 1</button>
   <button type="submit" class="btn btn-primary btn-block" onclick="window.location.href = 'http://qm.qq.com/cgi-bin/qm/qr?_wv=1027&k=WsdMIoelu6h6fcCnMllMxXAAh7w4tl6o&authKey=brakwe308i5p9A1qVzOAH78QY1B1yCRWs4FsQgPe0itTmKWS%2FfEFhlFS8byW4egc&noverify=0&group_code=562028087'">Vào nhóm phúc lợi công ích 2</button>
    </div>
  </div>



  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Tiêu đề thư</label>
      <div class="layui-input-inline">
        <input type="text" name="title" lay-verify="title" autocomplete="off" value="Thư hệ thống" class="layui-input">
      </div>
    </div>
  </div>

  <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Nội dung thư</label>
      <div class="layui-input-inline">
        <input type="text" name="context" lay-verify="title" autocomplete="off" value="Vui lòng kiểm tra và nhận" class="layui-input">
      </div>
    </div>
  </div>


 <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Vật phẩm 1</label>
      <div class="layui-input-inline">		
        <select name="item" lay-verify="required" lay-search="">
			<option value="">Vui lòng chọn vật phẩm</option>
			<?php 
				$lines=file("item.txt");
				foreach ($lines as $value) {
					$line=explode(";",$value);
					echo '<option value="'.$line[0].'">'.$line[1].'</option>';
				}
			?>		
        </select>			
      </div>	
      <div class="layui-input-inline">
        <input type="text" name="num" lay-verify="title" autocomplete="off" placeholder="Vui lòng nhập số lượng vật phẩm" class="layui-input">
      </div>	  
    </div>
  </div>  
 
 <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Vật phẩm 2</label>
      <div class="layui-input-inline">		
        <select name="item2" lay-verify="required" lay-search="">
			<option value="">Vui lòng chọn vật phẩm</option>
			<?php 
				$lines=file("item.txt");
				foreach ($lines as $value) {
					$line=explode(";",$value);
					echo '<option value="'.$line[0].'">'.$line[1].'</option>';
				}
			?>			
        </select>			
      </div>	
      <div class="layui-input-inline">
        <input type="text" name="num2" lay-verify="title" autocomplete="off" placeholder="Vui lòng nhập số lượng vật phẩm" class="layui-input">
      </div>	  
    </div>
  </div>    
  

 <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Vật phẩm 3</label>
      <div class="layui-input-inline">		
        <select name="item3" lay-verify="required" lay-search="">
			<option value="">Vui lòng chọn vật phẩm</option>
			<?php 
				$lines=file("item.txt");
				foreach ($lines as $value) {
					$line=explode(";",$value);
					echo '<option value="'.$line[0].'">'.$line[1].'</option>';
				}
			?>		
        </select>			
      </div>	
      <div class="layui-input-inline">
        <input type="text" name="num3" lay-verify="title" autocomplete="off" placeholder="Vui lòng nhập số lượng vật phẩm" class="layui-input">
      </div>	  
    </div>
  </div>  
  
 <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Vật phẩm 4</label>
      <div class="layui-input-inline">		
        <select name="item4" lay-verify="required" lay-search="">
			<option value="">Vui lòng chọn vật phẩm</option>
			<?php 
				$lines=file("item.txt");
				foreach ($lines as $value) {
					$line=explode(";",$value);
					echo '<option value="'.$line[0].'">'.$line[1].'</option>';
				}
			?>			
        </select>			
      </div>	
      <div class="layui-input-inline">
        <input type="text" name="num4" lay-verify="title" autocomplete="off" placeholder="Vui lòng nhập số lượng vật phẩm" class="layui-input">
      </div>	  
    </div>
  </div>    
  
 <div class="layui-form-item">
    <div class="layui-inline">
      <label class="layui-form-label">Vật phẩm 5</label>
      <div class="layui-input-inline">		
        <select name="item5" lay-verify="required" lay-search="">
			<option value="">Vui lòng chọn vật phẩm</option>
			<?php 
				$lines=file("item.txt");
				foreach ($lines as $value) {
					$line=explode(";",$value);
					echo '<option value="'.$line[0].'">'.$line[1].'</option>';
				}
			?>		
        </select>			
      </div>	
      <div class="layui-input-inline">
        <input type="text" name="num5" lay-verify="title" autocomplete="off" placeholder="Vui lòng nhập số lượng vật phẩm" class="layui-input">
      </div>	  
    </div>
  </div>    
 
  <div class="layui-form-item">
    <div class="layui-input-block">
	  <input type="submit" class="layui-btn" name="submit" value='Thư cá nhân'>
    </div>
  </div>





</form>
          
<script src="layui/layui.js" charset="utf-8"></script>
<script>
layui.use(['form', 'layedit', 'laydate'], function(){
  var form = layui.form
  ,layer = layui.layer
  ,layedit = layui.layedit
  ,laydate = layui.laydate; 
});
</script>



</body>
</html>