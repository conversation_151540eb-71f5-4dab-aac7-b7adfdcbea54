package indigo;

import java.util.Map;

import Message.S2CIndigoMsg.GetPourIndigoRsp;
import Message.S2CIndigoMsg.GetPourIndigoRspID;
import Message.Inner.GRCrossIndigo.GRGetPourIndigoReq;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.BePouredInfo;
import game.route.indigo.bean.PourInfo;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;

/**
 * 获取下注信息
 * <AUTHOR>
 *
 */
public class GetPourIndigoScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		IndigoService indigoService = IndigoService.getInstance();
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(Key.ARG1);
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		GRGetPourIndigoReq req = (GRGetPourIndigoReq) args.get(Key.ARG2);

		GetPourIndigoRsp.Builder builder = GetPourIndigoRsp.newBuilder();
		PourInfo pourInfo = indigoService.getPourMap().get(req.getPlayerId());
		if (pourInfo != null) {
			builder.setPouredId(pourInfo.getPouredId());
			builder.setType(pourInfo.getType());
		}
		
		Map<Integer, BePouredInfo> map = indigoService.getBePouredMap().get(groupId);
		if (null != map) {
			for (BePouredInfo pouredInfo : map.values()) {
				builder.addPouredList(pouredInfo.genBuilder());
			}
		}
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(req.getPlayerId(), GetPourIndigoRspID.GetPourIndigoRspMsgID_VALUE, builder.build().toByteString()));
		return null;
	}

}
