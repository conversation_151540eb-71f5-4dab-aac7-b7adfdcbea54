package indigo;

import Message.S2CCrossIndigoMsg.GetFightHeroRsp;
import Message.S2CCrossIndigoMsg.GetFightHeroRspID;
import Message.Inner.GRCrossIndigo.GRGetFightHeroReq;
import game.core.pub.script.IScript;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Participant;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;

/**
 * 响应玩家详细信息
 * <AUTHOR>
 *
 */
public class ReqFightHeroScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(ScriptArgs.Key.ARG1);
		GRGetFightHeroReq req = (GRGetFightHeroReq) args.get(ScriptArgs.Key.ARG2);
		getFightHero(serverId, req.getPlayerId());
		return null;
	}
	
	private void getFightHero(int serverId, long playerId) {
		IndigoService indigoService = IndigoService.getInstance();
		Participant participant = indigoService.getParticipantByPlayerId(playerId);
		GetFightHeroRsp.Builder builder = GetFightHeroRsp.newBuilder();
		if (null != participant) {
			for (Hero hero : participant.getHeroList()) {
				builder.addHero(hero.genHeroPos());
			}
		}
		GRMessageUtils.sendMsg2GameServer(serverId,
				indigoService.genGRIndigoRsp(playerId, GetFightHeroRspID.GetFightHeroRspMsgID_VALUE, builder.build().toByteString()));
	}
}
