package indigo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import Message.S2CIndigoMsg.SingleRankMsg;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;

/**
 * 初赛排行排序汇总并生成决赛资格
 * <AUTHOR>
 *
 */
public class SortPreRankScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		IndigoService indigoService = IndigoService.getInstance();
		Map<Integer, List<SingleRankMsg>> allRank = indigoService.getPreRaceRankListAll();
		if (indigoService.getPreRaceRank().isEmpty()) {
			return null;
		}
		allRank.clear();
		synchronized (indigoService.getPreRaceRank()) {
			for (Entry<Integer, Map<Integer, List<SingleRankMsg>>> en : indigoService.getPreRaceRank().entrySet()) {
				Integer groupId = en.getKey();
				Map<Integer, List<SingleRankMsg>> value = en.getValue();
				List<SingleRankMsg> l = new ArrayList<>();
				// 所有服务器排行榜汇总
				for (List<SingleRankMsg> temp : value.values()) {
					for (SingleRankMsg singleRankMsg : temp) {
						// 排除重复
						boolean flag = false;
						for (SingleRankMsg sr : l) {
							if (sr.getPlayerId() == singleRankMsg.getPlayerId()) {
								flag = true;
							}
						}
						if (!flag) {
							l.add(singleRankMsg);
						}
					}
				}
				// 排序
				l.sort((SingleRankMsg o1, SingleRankMsg o2) -> {
					if (o1.getWinNum() > o2.getWinNum()) {
						return -1;
					} else if (o1.getWinNum() < o2.getWinNum()) {
						return 1;
					} else {
						if (o1.getIntegral() > o2.getIntegral()) {
							return -1;
						} else if (o1.getIntegral() < o2.getIntegral()) {
							return 1;
						} else {
							return 0;
						}
					}
				});
				List<SingleRankMsg> l1 = new ArrayList<>();
				for (int i = 0; i < l.size(); i++) {
					SingleRankMsg msg = l.get(i);
					SingleRankMsg.Builder b = SingleRankMsg.newBuilder();
					b.setGuildId(msg.getGuildId());
					b.setGuildName(msg.getGuildName());
					b.setHeroId(msg.getHeroId());
					b.setIntegral(msg.getIntegral());
					b.setLoseNum(msg.getLoseNum());
					b.setLvl(msg.getLvl());
					b.setName(msg.getName());
					b.setPlayerId(msg.getPlayerId());
					b.setRank(i + 1);
					b.setServerId(msg.getServerId());
					b.setServerName(msg.getServerName());
					b.setStar(msg.getStar());
					b.setWinNum(msg.getWinNum());
					b.setSex(msg.getSex());
					l1.add(b.build());
				}
				allRank.put(groupId, l1);
			}
		}
		return null;
	}

}
