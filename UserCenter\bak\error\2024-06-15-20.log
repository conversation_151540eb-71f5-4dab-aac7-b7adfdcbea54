2024-06-15 20:41:08.0066 [quartzScheduler_Worker-2] ERROR org.quartz.core.JobRunShell-[Line:211]>Job DEFAULT.channelSwitchUpdateDetail threw an unhandled Exception: 
org.springframework.scheduling.quartz.JobMethodInvocationFailedException: Invocation of method 'refresh' on target class [class com.playmore.server.list.ChannelSwitchManager] failed; nested exception is org.springframework.dao.TransientDataAccessResourceException: StatementCallback; SQL [select * from t_s_channel_switch order by channel,switch_type]; Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp; nested exception is java.sql.SQLException: Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp
	at org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean$MethodInvokingJob.executeInternal(MethodInvokingJobDetailFactoryBean.java:320) ~[spring-context-support-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.scheduling.quartz.QuartzJobBean.execute(QuartzJobBean.java:113) ~[spring-context-support-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202) ~[quartz-2.2.1.jar:na]
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573) [quartz-2.2.1.jar:na]
Caused by: org.springframework.dao.TransientDataAccessResourceException: StatementCallback; SQL [select * from t_s_channel_switch order by channel,switch_type]; Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp; nested exception is java.sql.SQLException: Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:106) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:407) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:456) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:464) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at com.playmore.server.list.ChannelSwitchDaoImpl.all(ChannelSwitchDaoImpl.java:41) ~[UserCenter.jar:na]
	at com.playmore.server.list.ChannelSwitchManager.refresh(ChannelSwitchManager.java:34) ~[UserCenter.jar:na]
	at sun.reflect.GeneratedMethodAccessor57.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_144]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_144]
	at org.springframework.util.MethodInvoker.invoke(MethodInvoker.java:273) ~[spring-core-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean$MethodInvokingJob.executeInternal(MethodInvokingJobDetailFactoryBean.java:311) ~[spring-context-support-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	... 3 common frames omitted
Caused by: java.sql.SQLException: Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:1062) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:973) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:959) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:904) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetRow.getTimestampFast(ResultSetRow.java:1086) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ByteArrayRow.getTimestampFast(ByteArrayRow.java:111) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetImpl.getTimestampInternal(ResultSetImpl.java:6588) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetImpl.getTimestamp(ResultSetImpl.java:6188) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetImpl.getTimestamp(ResultSetImpl.java:6226) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.playmore.server.list.ChannelSwitchDaoImpl$1.mapRow(ChannelSwitchDaoImpl.java:27) ~[UserCenter.jar:na]
	at com.playmore.server.list.ChannelSwitchDaoImpl$1.mapRow(ChannelSwitchDaoImpl.java:19) ~[UserCenter.jar:na]
	at org.springframework.jdbc.core.RowMapperResultSetExtractor.extractData(RowMapperResultSetExtractor.java:92) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.RowMapperResultSetExtractor.extractData(RowMapperResultSetExtractor.java:60) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:446) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:396) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	... 12 common frames omitted
2024-06-15 20:41:08.0098 [quartzScheduler_Worker-2] ERROR org.quartz.core.ErrorLogger-[Line:2425]>Job (DEFAULT.channelSwitchUpdateDetail threw an exception.
org.quartz.SchedulerException: Job threw an unhandled exception.
	at org.quartz.core.JobRunShell.run(JobRunShell.java:213) ~[quartz-2.2.1.jar:na]
	at org.quartz.simpl.SimpleThreadPool$WorkerThread.run(SimpleThreadPool.java:573) [quartz-2.2.1.jar:na]
Caused by: org.springframework.scheduling.quartz.JobMethodInvocationFailedException: Invocation of method 'refresh' on target class [class com.playmore.server.list.ChannelSwitchManager] failed; nested exception is org.springframework.dao.TransientDataAccessResourceException: StatementCallback; SQL [select * from t_s_channel_switch order by channel,switch_type]; Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp; nested exception is java.sql.SQLException: Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp
	at org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean$MethodInvokingJob.executeInternal(MethodInvokingJobDetailFactoryBean.java:320) ~[spring-context-support-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.scheduling.quartz.QuartzJobBean.execute(QuartzJobBean.java:113) ~[spring-context-support-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.quartz.core.JobRunShell.run(JobRunShell.java:202) ~[quartz-2.2.1.jar:na]
	... 1 common frames omitted
Caused by: org.springframework.dao.TransientDataAccessResourceException: StatementCallback; SQL [select * from t_s_channel_switch order by channel,switch_type]; Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp; nested exception is java.sql.SQLException: Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:106) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:72) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:80) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:407) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:456) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:464) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at com.playmore.server.list.ChannelSwitchDaoImpl.all(ChannelSwitchDaoImpl.java:41) ~[UserCenter.jar:na]
	at com.playmore.server.list.ChannelSwitchManager.refresh(ChannelSwitchManager.java:34) ~[UserCenter.jar:na]
	at sun.reflect.GeneratedMethodAccessor57.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_144]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_144]
	at org.springframework.util.MethodInvoker.invoke(MethodInvoker.java:273) ~[spring-core-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.scheduling.quartz.MethodInvokingJobDetailFactoryBean$MethodInvokingJob.executeInternal(MethodInvokingJobDetailFactoryBean.java:311) ~[spring-context-support-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	... 3 common frames omitted
Caused by: java.sql.SQLException: Value '0000-00-00 00:00:00' can not be represented as java.sql.Timestamp
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:1062) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:973) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:959) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.SQLError.createSQLException(SQLError.java:904) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetRow.getTimestampFast(ResultSetRow.java:1086) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ByteArrayRow.getTimestampFast(ByteArrayRow.java:111) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetImpl.getTimestampInternal(ResultSetImpl.java:6588) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetImpl.getTimestamp(ResultSetImpl.java:6188) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.mysql.jdbc.ResultSetImpl.getTimestamp(ResultSetImpl.java:6226) ~[mysql-connector-java-commercial-5.1.27-bin.jar:na]
	at com.playmore.server.list.ChannelSwitchDaoImpl$1.mapRow(ChannelSwitchDaoImpl.java:27) ~[UserCenter.jar:na]
	at com.playmore.server.list.ChannelSwitchDaoImpl$1.mapRow(ChannelSwitchDaoImpl.java:19) ~[UserCenter.jar:na]
	at org.springframework.jdbc.core.RowMapperResultSetExtractor.extractData(RowMapperResultSetExtractor.java:92) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.RowMapperResultSetExtractor.extractData(RowMapperResultSetExtractor.java:60) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate$1QueryStatementCallback.doInStatement(JdbcTemplate.java:446) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:396) ~[spring-jdbc-3.2.5.RELEASE.jar:3.2.5.RELEASE]
	... 12 common frames omitted
2024-06-15 20:52:32.0146 [Jetty-Server] ERROR com.playmore.http.jetty.JettyServer-[Line:197]>Jetty server start fail.
java.net.BindException: 地址已在使用
	at sun.nio.ch.Net.bind0(Native Method) ~[na:1.8.0_144]
	at sun.nio.ch.Net.bind(Net.java:433) ~[na:1.8.0_144]
	at sun.nio.ch.Net.bind(Net.java:425) ~[na:1.8.0_144]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[na:1.8.0_144]
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:74) ~[na:1.8.0_144]
	at org.eclipse.jetty.server.ServerConnector.open(ServerConnector.java:321) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.server.AbstractNetworkConnector.doStart(AbstractNetworkConnector.java:80) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.server.ServerConnector.doStart(ServerConnector.java:236) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:68) ~[jetty-util-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.server.Server.doStart(Server.java:366) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:68) ~[jetty-util-9.2.10.v20150310.jar:9.2.10.v20150310]
	at com.playmore.http.jetty.JettyServer$1.run(JettyServer.java:194) ~[UserCenter.jar:na]
2024-06-15 20:57:26.0451 [Jetty-Server] ERROR com.playmore.http.jetty.JettyServer-[Line:197]>Jetty server start fail.
java.net.BindException: 地址已在使用
	at sun.nio.ch.Net.bind0(Native Method) ~[na:1.8.0_144]
	at sun.nio.ch.Net.bind(Net.java:433) ~[na:1.8.0_144]
	at sun.nio.ch.Net.bind(Net.java:425) ~[na:1.8.0_144]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[na:1.8.0_144]
	at sun.nio.ch.ServerSocketAdaptor.bind(ServerSocketAdaptor.java:74) ~[na:1.8.0_144]
	at org.eclipse.jetty.server.ServerConnector.open(ServerConnector.java:321) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.server.AbstractNetworkConnector.doStart(AbstractNetworkConnector.java:80) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.server.ServerConnector.doStart(ServerConnector.java:236) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:68) ~[jetty-util-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.server.Server.doStart(Server.java:366) ~[jetty-server-9.2.10.v20150310.jar:9.2.10.v20150310]
	at org.eclipse.jetty.util.component.AbstractLifeCycle.start(AbstractLifeCycle.java:68) ~[jetty-util-9.2.10.v20150310.jar:9.2.10.v20150310]
	at com.playmore.http.jetty.JettyServer$1.run(JettyServer.java:194) ~[UserCenter.jar:na]
