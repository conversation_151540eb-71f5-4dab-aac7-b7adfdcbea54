package logic.crossArena;

import Message.S2CCrossArenaMsg;
import Message.S2CCrossArenaMsg.CrossArenaReportsRsp;
import Message.S2CArenaMsg.FighterInfo;
import Message.S2CArenaMsg.RewardItemMsg;

import Message.S2CPirateBossMsg;
import data.bean.t_arena_integralBean;
import data.bean.t_cross_arena_integralBean;
import data.bean.t_dropBean;
import game.core.pub.script.IScript;
import game.server.logic.constant.ItemType;
import game.server.logic.constant.Reason;
import game.server.logic.drop.DropService;
import game.server.logic.item.bean.Item;
import game.server.logic.mail.MailService;
import game.server.logic.pirateBoss.PirateBossService;
import game.server.logic.player.Player;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import Message.S2CPlayerMsg.PromptType;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRAPirateBossKillRsp;
import Message.S2CPirateBossMsg.CrossPirateBossRankInfo;
import Message.S2CPirateBossMsg.PirateBossKillRsp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SendPirateBossCrossRankScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRAPirateBossKillRsp getData = (GRAPirateBossKillRsp) args.get(ScriptArgs.Key.ARG1);
		List<S2CPirateBossMsg.CrossPirateBossRankInfo> crossRankList = getData.getCrossRankList();
		PirateBossService.getInstance().setCrossRank(crossRankList);
		return null;
	}

}
