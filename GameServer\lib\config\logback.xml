<configuration scan="true" scanPeriod="60 seconds" debug="false">
	<property name="AppName" value="Q10"/>
	<contextName>${AppName}</contextName>
	
	<appender name="FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<prudent>false</prudent>
		<file>logs/work.log</file>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>bak/work/%d{yyyy-MM-dd-HH}.log</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSSS} [%thread] %-5level %logger{36}-[Line:%L]>%m%n</pattern>
		</encoder>
	</appender>

	<appender name="ERROR_FILE"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>logs/error.log</file>
		<prudent>false</prudent>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>bak/error/%d{yyyy-MM-dd-HH}.log
			</fileNamePattern>
			<maxHistory>30</maxHistory>
		</rollingPolicy>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>ERROR</level>
		</filter>
		<encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSSS} [%thread] %-5level %logger{36}-[Line:%L]>%m%n</pattern>
		</encoder>
	</appender>

	<appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
		<!--<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>DEBUG</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		--><encoder>
			<pattern>%d{yyyy-MM-dd HH:mm:ss.SSSS} [%thread] %-5level %logger{36}-[Line:%L]>%m%n
			</pattern>
		</encoder>
	</appender>

	<root level="INFO">
		<appender-ref ref="CONSOLE" />
		<appender-ref ref="FILE" />
		<appender-ref ref="ERROR_FILE" />
	</root>
</configuration>