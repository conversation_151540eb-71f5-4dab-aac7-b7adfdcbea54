[2024-09-11 16:03:05:382 ERROR](ServerService.java:291)网络波动导致心跳终端,服务器重连:93公益
[2024-09-11 16:03:06:072 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-11 16:03:16:201 INFO](TcpClient.java:130)sessionCreate, sessionId:164,ip:/*************:39001
[2024-09-11 16:03:16:215 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 16:03:16:351 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-11 16:03:16:351 ERROR](TcpClient.java:77)repeat connect[游戏服][93公益][*************:39001]
[2024-09-11 16:05:17:392 ERROR](ServerService.java:291)网络波动导致心跳终端,服务器重连:93公益
[2024-09-11 16:05:17:467 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-11 16:05:27:486 INFO](TcpClient.java:130)sessionCreate, sessionId:165,ip:/*************:39001
[2024-09-11 16:05:27:489 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 16:05:27:699 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-11 16:05:27:699 ERROR](TcpClient.java:77)repeat connect[游戏服][93公益][*************:39001]
[2024-09-11 16:07:29:415 ERROR](ServerService.java:291)网络波动导致心跳终端,服务器重连:93公益
[2024-09-11 16:07:29:460 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-11 16:07:39:567 INFO](TcpClient.java:130)sessionCreate, sessionId:166,ip:/*************:39001
[2024-09-11 16:07:39:580 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 16:07:39:589 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-11 16:07:39:590 ERROR](TcpClient.java:77)repeat connect[游戏服][93公益][*************:39001]
[2024-09-11 16:09:41:440 ERROR](ServerService.java:291)网络波动导致心跳终端,服务器重连:93公益
[2024-09-11 16:09:41:504 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-11 16:09:51:532 INFO](TcpClient.java:130)sessionCreate, sessionId:167,ip:/*************:39001
[2024-09-11 16:09:51:538 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 16:09:51:546 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-11 16:09:51:547 ERROR](TcpClient.java:77)repeat connect[游戏服][93公益][*************:39001]
[2024-09-11 16:10:09:318 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
[2024-09-11 16:12:15:511 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 16:12:15:778 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][93公益][*************:39001]
[2024-09-11 16:13:49:673 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:*************,cmd:2
[2024-09-11 16:13:56:196 INFO](TcpClient.java:130)sessionCreate, sessionId:168,ip:/*************:39001
[2024-09-11 16:13:56:201 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 16:13:56:266 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 16:13:56:304 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
