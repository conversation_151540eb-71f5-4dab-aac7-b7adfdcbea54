package logic.research;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import Message.C2SResearchMsg.DecomRuneReq;
import Message.S2CBackpackMsg.PropInfo;
import Message.S2CPlayerMsg.PromptType;
import Message.S2CResearchMsg.DecomRuneRsp;
import Message.S2CResearchMsg.DecomRuneRspID;
import data.bean.t_recyclingBean;
import data.bean.t_runeBean;
import game.core.pub.script.IScript;
import game.core.pub.script.ScriptManager;
import game.server.logic.constant.Reason;
import game.server.logic.constant.TaskConditionType;
import game.server.logic.item.bean.Item;
import game.server.logic.log.LogService;
import game.server.logic.player.Player;
import game.server.logic.research.ResearchService;
import game.server.logic.rune.bean.Rune;
import game.server.logic.util.BeanFactory;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;

/**
 * 分解符文
 * 
 * <AUTHOR>
 * @date 2018年9月3日
 */
public class ReqDecomHeroRuneScript implements IScript {

	private final Logger logger = Logger.getLogger(ReqDecomHeroRuneScript.class);

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs script = (ScriptArgs) arg;
		DecomRuneReq req = (DecomRuneReq) script.get(ScriptArgs.Key.ARG1);
		Player player = (Player) script.get(ScriptArgs.Key.PLAYER);
		ResearchService.getInstance().decomposeRune(player, req.getRuneIdList(), req.getIsPreview());
		return null;
	}
}
