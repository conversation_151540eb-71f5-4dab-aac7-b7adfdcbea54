package indigo;

import java.util.ArrayList;
import java.util.List;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import Message.S2CIndigoMsg.SingleRankMsg;
import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.Inner.GRCrossIndigo.GRPlayerInfoReq;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.server.ServerService;
import game.route.util.ScriptArgs;

/**
 * 向gameServer获取玩家详细信息
 * <AUTHOR>
 *
 */
public class PlayerInfoScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		GRPlayerInfoReq req = (GRPlayerInfoReq) args.get(ScriptArgs.Key.ARG1);
		int serverId = (int) args.get(ScriptArgs.Key.ARG2);
		ByteString rsp = req.getRsp();
		GetPlayerInfoRsp info = null;
		try {
			info = GetPlayerInfoRsp.parseFrom(rsp);
		} catch (InvalidProtocolBufferException e) {
			e.printStackTrace();
		}
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		IndigoService indigoService = IndigoService.getInstance();
		List<SingleRankMsg> list = indigoService.getRankList().get(groupId);
		if (null != list && !list.isEmpty()) {
			SingleRankMsg rankMsg;
			for (int i = 0; i < list.size(); i++) {
				// 只有前50
				if (i >= 50) {
					break;
				}
				rankMsg = list.get(i);
				if (info.getInfo().getPlayerId() == rankMsg.getPlayerId()) {
					List<ByteString> l = indigoService.getTop3Catch().get(groupId);
					if (null == l) {
						l = new ArrayList<>();
						for (int j = 0; j < 50; j++) {
							l.add(null);
						}
						indigoService.getTop3Catch().put(groupId, l);
					}
					l.set(i, rsp);
				}
			}
		}
		return null;
	}
}
