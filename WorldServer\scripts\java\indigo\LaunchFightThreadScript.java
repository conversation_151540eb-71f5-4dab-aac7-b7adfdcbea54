package indigo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;

import com.google.protobuf.ByteString;

import Message.S2CIndigoMsg.RaceStatus;
import Message.S2CIndigoMsg.SingleRankMsg;
import game.core.pub.script.IScript;
import game.core.pub.script.ScriptManager;
import game.route.indigo.IndigoService;
import game.route.indigo.handler.InnerCreateRacesHandler;
import game.route.indigo.handler.InnerStartRacesHandler;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.util.BeanTemplet;

/**
 * 开启战斗线程
 * 
 * <AUTHOR>
 *
 *         2018年9月28日
 */
public class LaunchFightThreadScript implements IScript {
	private final Logger logger = Logger.getLogger(LaunchFightThreadScript.class);

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		startFight();
		return null;
	}

	/**
	 * 战斗过程
	 */
	public void startFight() {
		int session = 0;
		IndigoService indigoService = IndigoService.getInstance();
		for (;;) {
			session++;
			long reayDelay = BeanTemplet.getGlobalBean(409).getInt_value() * 1000;
			indigoService.setRaceStatus(RaceStatus.RACE_READY_VALUE);// 设置比赛状态
			indigoService.setFightStageEndTime(System.currentTimeMillis() + reayDelay);
			indigoService.fightStatusNotify(session);// 推送
			indigoService.addCommand(new InnerCreateRacesHandler(session));// 分组任务
			logger.info(">>>>>>=============比赛第" + session + "轮准备开始================");
			try {
				Thread.sleep(reayDelay);// 延时300 准备阶段
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			long fightDelay = BeanTemplet.getGlobalBean(212).getInt_value() * 1000;
			indigoService.setRaceStatus(RaceStatus.RACE_FIGHT_VALUE);
			indigoService.setFightStageEndTime(System.currentTimeMillis() + fightDelay);
			indigoService.fightStatusNotify(session);// 推送
			indigoService.addCommand(new InnerStartRacesHandler(session));// 比赛任务
			logger.info(">>>>>>=============比赛第" + session + "轮战斗结束================");
			try {
				Thread.sleep(fightDelay);// 延时60 战斗阶段
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			// 单轮比赛结束，设置标识
			IndigoService.getInstance().setSession(session);
			if (session >= 8) {
				break;
			}
		}
		// 生成排行榜
		ScriptManager.getInstance().call("indigo.GenerateRankScript", null);
		indigoService.setRaceStatus(RaceStatus.RACE_END_VALUE);
		indigoService.notifyTop3Info(); // 推送前三数据
		indigoService.fightStatusNotify(session);// 推送 ==== 此处推送客户端用于切换top3界面

		// 更新玩家积分信息
		indigoService.sendPlayerFinalRankMessage();
		// 50个空占位
		indigoService.getTop3Catch().clear();
		Map<Integer, GameServer> gameServerMap = ServerService.getInstance().getGameServerMap();
		for (Integer groupId : gameServerMap.keySet()) {
			List<ByteString> l = indigoService.getTop3Catch().get(groupId);
			if (l == null) {
				l = new ArrayList<>();
				indigoService.getTop3Catch().put(groupId, l);
			}
			for (int i = 0; i < 50; i++) {
				l.add(null);
			}
		}
		// 获取top50详情
		SingleRankMsg rank;
		for (List<SingleRankMsg> list : indigoService.getRankList().values()) {
			for (int i = 0; i < list.size(); i++) {
				rank = list.get(i);
				if (rank.getServerId() == 0) {
					continue;
				}
				indigoService.getTopInfo(rank.getServerId(), rank.getPlayerId());
				if (i >= 50) {
					break;
				}
			}
		}
	}
}
