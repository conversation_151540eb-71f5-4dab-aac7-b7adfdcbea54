[2024-09-23 11:52:26:915 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-23 11:52:27:708 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-23 11:52:27:742 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-23 11:52:27:743 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-23 11:52:29:405 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-23 11:52:30:901 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-23 11:52:30:902 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-23 11:52:31:129 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-23 11:52:31:131 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-23 11:52:31:152 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-23 11:52:31:493 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-23 11:52:32:007 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-23 11:53:07:231 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:**************,cmd:2
[2024-09-23 11:53:11:340 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/**************:39001
[2024-09-23 11:53:11:341 INFO](CommunicationC.java:198)connect success, ip: [**************], port = 39001
[2024-09-23 11:53:11:350 INFO](TcpClient.java:86)[游戏服][主公一区][**************:39001]connect succ
[2024-09-23 11:53:11:393 INFO](AuthenticationHandler.java:35)connect serverIp:**************---主公一区,authErrorCode:0
