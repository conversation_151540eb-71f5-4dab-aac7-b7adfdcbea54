package indigo;

import java.util.HashMap;
import java.util.Map;

import Message.S2CIndigoMsg.IndigoStage;
import Message.S2CIndigoMsg.SaveHeroesRsp;
import Message.S2CIndigoMsg.SaveHeroesRspID;
import Message.S2CPlayerMsg.PromptType;
import Message.Inner.GRCrossIndigo.GRHeroMsg;
import Message.Inner.GRCrossIndigo.GRSaveHeroesReq;
import game.core.pub.script.IScript;
import game.core.pub.script.ScriptManager;
import game.route.hero.bean.Hero;
import game.route.indigo.IndigoService;
import game.route.indigo.bean.Formation;
import game.route.indigo.bean.Participant;
import game.route.server.ServerService;
import game.route.util.GRMessageUtils;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;

/**
 * 保存参赛英雄状态属性
 * 
 * <AUTHOR>
 *
 */
public class ReqSaveHerosScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		int serverId = (int) args.get(ScriptArgs.Key.ARG1);
		GRSaveHeroesReq req = (GRSaveHeroesReq) args.get(ScriptArgs.Key.ARG2);
		IndigoService indigoService = IndigoService.getInstance();
		Integer groupId = ServerService.getInstance().getGroupIdByServer(serverId);
		if (indigoService.getIndigoStage() != IndigoStage.INDIGO_APPLAY_VALUE) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 290);// 报名已结束,不能保存
			return null;
		}
		Map<Long, Participant> map = indigoService.getParticipantMap().get(groupId);
		if (null == map) {
			map = new HashMap<>();
			indigoService.getParticipantMap().put(groupId, map);
		}
		if (!map.containsKey(req.getPlayerId())) {
			GRMessageUtils.sendPrompt(serverId, req.getPlayerId(), PromptType.WARNING, 296);// "未报名参赛"
			return null;
		}
		saveHeros(serverId, req);
		return null;
	}

	private void saveHeros(int serverId, GRSaveHeroesReq req) {
		IndigoService indigoService = IndigoService.getInstance();
		Participant participant = indigoService.getParticipantByPlayerId(req.getPlayerId());
		// 清空之前的记录
		participant.getHeroList().clear();
		long power = 0;
		GRHeroMsg grHeroMsg;
		Hero hero = null;
		Hero temp;
		for (int i = 0; i < req.getHeroListCount(); i++) {
			grHeroMsg = req.getHeroList(i);
			temp = new Hero();
			temp.fromGRHeroMsg(grHeroMsg);
			power += temp.getPower();
			participant.getHeroList().add(temp);
			if (null == hero || hero.getPower() < temp.getPower()) {
				hero = temp;
			}
		}
		participant.setOffensiveAdd(req.getOffensiveAdd());
		participant.setPower(power);
		participant.setShowHeroId(hero.getId());
		participant.setStar(hero.getStar());
		participant.setLvl(hero.getLevel());
		participant.setGuildId(req.getGuildId());
		// 自动编队
		ScriptArgs args = new ScriptArgs();
		args.put(Key.ARG1, participant);
		ScriptManager.getInstance().call("indigo.AutoFormationScript", args);
		// 推送
		SaveHeroesRsp.Builder builder = SaveHeroesRsp.newBuilder();
		for (Formation formation : participant.getFormations()) {
			builder.addFormation(formation.genBuilder());
		}
		for (Hero h : participant.getHeroList()) {
			builder.addHero(h.genHeroPos());
		}
		GRMessageUtils.sendMsg2GameServer(serverId, indigoService.genGRIndigoRsp(req.getPlayerId(),
				SaveHeroesRspID.SaveHeroesRspMsgID_VALUE, builder.build().toByteString()));
	}
}
