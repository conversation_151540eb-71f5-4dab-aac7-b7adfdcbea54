[2024-09-07 23:41:25:321 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-07 23:41:26:181 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-07 23:41:26:230 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-07 23:41:26:231 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-07 23:41:28:764 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-07 23:41:30:476 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-07 23:41:30:477 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-07 23:41:30:681 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-07 23:41:30:694 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-07 23:41:30:760 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-07 23:41:31:085 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-07 23:41:31:710 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-07 23:45:29:259 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:*************,cmd:2
[2024-09-07 23:45:30:971 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/*************:39001
[2024-09-07 23:45:30:973 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-07 23:45:30:993 INFO](TcpClient.java:86)[游戏服][93公益][*************:39001]connect succ
[2024-09-07 23:45:31:021 INFO](AuthenticationHandler.java:35)connect serverIp:*************---93公益,authErrorCode:0
