#server.host = **************
#http server ip and port
http.server.jetty.port = 8501

access.token.timeout = 1800000

#allow ip configure
allow.ips=127.0.0.1/32,***********/16,**********/16,***********/16,*********/16,************/16

# database configure
# driver class configure
db.driverClass=com.mysql.jdbc.Driver
# configure of account server
db.account.jdbcUrl=**********************************************************************************************************************************************************************
db.account.username=root
db.account.password=123456
db.account.idleConnectionTestPeriodInMinutes=60
db.account.idleMaxAgeInSeconds=3600
db.account.maxConnectionsPerPartition=60
db.account.minConnectionsPerPartition=10
db.account.partitionCount=3
db.account.acquireIncrement=5
db.account.statementsCacheSize=256


http.server.recharge.host=************:8005
