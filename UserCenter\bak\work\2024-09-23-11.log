2024-09-23 11:00:34.0792 [Jetty-QTP-24] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功2775141176
2024-09-23 11:17:33.0459 [Jetty-QTP-20] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功2775141176
2024-09-23 11:35:37.0487 [Jetty-QTP-22] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功251030
2024-09-23 11:43:53.0255 [Jetty-QTP-22] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功251030
2024-09-23 11:52:05.0620 [main] INFO  o.s.c.s.ClassPathXmlApplicationContext-[Line:510]>Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@ae45eb6: startup date [Mon Sep 23 11:52:05 CST 2024]; root of context hierarchy
2024-09-23 11:52:05.0684 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [applicationContext.xml]
2024-09-23 11:52:05.0804 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [cache_conf.xml]
2024-09-23 11:52:05.0859 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [datasource.xml]
2024-09-23 11:52:05.0903 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [annotation.xml]
2024-09-23 11:52:05.0991 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [quartz.xml]
2024-09-23 11:52:06.0033 [main] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [servlet-context.xml]
2024-09-23 11:52:06.0594 [main] INFO  o.s.b.f.c.PropertyPlaceholderConfigurer-[Line:172]>Loading properties file from class path resource [config.properties]
2024-09-23 11:52:06.0663 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c: defining beans [propertyConfigurer,jettyRunner,CIDRSetting,org.springframework.aop.config.internalAutoProxyCreator,org.springframework.cache.annotation.AnnotationCacheOperationSource#0,org.springframework.cache.interceptor.CacheInterceptor#0,org.springframework.cache.config.internalCacheAdvisor,cacheManagerFactory,cacheManager,dataSource,daoTemplate,serverInfoDao,serverStatusDao,serverInfoManager,rebateInfoDao,rebateInfoManager,activationCreateInfoDao,activationCreateManager,activationInfoDao,activationManager,dailyDao,dailyInfoUpdateQueue,serverConfigureDao,serverConfigureManager,serverZoneDao,serverZoneManager,serverLoginWhiteDao,serverLoginWhiteManager,serverSwitchDao,serverSwitchManager,channelInfoDao,channelInfoManager,channelSwitchDao,channelSwitchManager,ServerWhiteIpManager,serverWhiteIpDao,accountDao,accountProvider,roleDao,accessTokenManager,roleInfoUpdateQueue,banDao,banManager,gagDao,gagManager,activityMailDao,sendMailRecordDao,activityMailQueue,sendMailDBQueue,activityMailLoader,equipmentCodeDao,rechargeDao,roleRegisterDao,openServerDao,openServerDaily,openIPDao,ChatDao,YYPlatformRebateDao,YYPlatformRebateManager,YYPlatformGiftDao,org.springframework.context.annotation.internalConfigurationAnnotationProcessor,org.springframework.context.annotation.internalAutowiredAnnotationProcessor,org.springframework.context.annotation.internalRequiredAnnotationProcessor,org.springframework.context.annotation.internalCommonAnnotationProcessor,quartzScheduler,accountDBDetail,accountDBTrigger,serverWhiteIpDBDetail,serverWhiteIpDBTrigger,accountReleaseDetail,accountReleaseTrigger,serverUpdateDetail,serverUpdateTrigger,serverConfigureUpdateDetail,serverConfigureUpdateTrigger,serverZoneUpdateDetail,serverZoneUpdateTrigger,serverLoginWhiteUpdateDetail,serverLoginWhiteUpdateTrigger,serverSwitchUpdateDetail,serverSwitchUpdateTrigger,channelSwitchUpdateDetail,channelSwitchUpdateTrigger,accessTokenDetail,accessTokenTrigger,roleDBDetail,roleDBTrigger,banDBDetail,banDBTrigger,gagDBDetail,gagDBTrigger,dailyDBDetail,dailyDBTrigger,openServerDBDetail,openServerDBTrigger,accountCache,sendMailCache,banCache,gagCache,roleListCache,activationCreateHandler,banHandler,batchCreateRoleHandler,channelListHandler,channelSwitchHandler,createRoleHandler,debanHandler,degagHandler,gagHandler,getAllRebateListHandler,getAllServerListHandler,getAllYYPlatformRebateListHandler,getServerConfigInfoHandler,getServerConfigureHandler,getServerListHandler,getStrideServerHandler,loginYGSDKHandle,logonWithoutSDKHandler,queryBanHandler,queryRoleInfoHandler,queryX7IOSRoleInfoHandler,queryX7RoleInfoHandler,rechargeCallBackHandler,rechargeHandler,roleCreateLogHandler,sendMailHandler,serverLoginWhiteHandler,serverSwitchHandler,serverZoneHandler,YMNotifyHandler,gameCloseServlet,org.springframework.context.annotation.ConfigurationClassPostProcessor.importAwareProcessor]; root of factory hierarchy
2024-09-23 11:52:06.0708 [main] INFO  org.eclipse.jetty.util.log-[Line:186]>Logging initialized @2217ms
2024-09-23 11:52:06.0843 [main] INFO  o.s.w.c.s.XmlWebApplicationContext-[Line:510]>Refreshing Root WebApplicationContext: startup date [Mon Sep 23 11:52:06 CST 2024]; parent: org.springframework.context.support.ClassPathXmlApplicationContext@ae45eb6
2024-09-23 11:52:06.0853 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@7cbd9d24: defining beans []; parent: org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c
2024-09-23 11:52:06.0862 [main] INFO  o.s.w.c.s.XmlWebApplicationContext-[Line:510]>Refreshing Root WebApplicationContext: startup date [Mon Sep 23 11:52:06 CST 2024]; parent: org.springframework.context.support.ClassPathXmlApplicationContext@ae45eb6
2024-09-23 11:52:06.0863 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:444]>Destroying singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@7cbd9d24: defining beans []; parent: org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c
2024-09-23 11:52:06.0864 [main] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@6d07a63d: defining beans []; parent: org.springframework.beans.factory.support.DefaultListableBeanFactory@b2c9a9c
2024-09-23 11:52:06.0870 [Jetty-Server] INFO  org.eclipse.jetty.server.Server-[Line:327]>jetty-9.2.10.v20150310
2024-09-23 11:52:06.0908 [Jetty-Server] INFO  /-[Line:2052]>Initializing Spring FrameworkServlet 'baseServlet'
2024-09-23 11:52:06.0909 [Jetty-Server] INFO  o.s.web.servlet.DispatcherServlet-[Line:454]>FrameworkServlet 'baseServlet': initialization started
2024-09-23 11:52:06.0911 [Jetty-Server] INFO  o.s.w.c.s.XmlWebApplicationContext-[Line:510]>Refreshing WebApplicationContext for namespace 'baseServlet-servlet': startup date [Mon Sep 23 11:52:06 CST 2024]; root of context hierarchy
2024-09-23 11:52:06.0911 [Jetty-Server] INFO  o.s.b.f.xml.XmlBeanDefinitionReader-[Line:315]>Loading XML bean definitions from class path resource [servlet-context.xml]
2024-09-23 11:52:07.0118 [main] INFO  o.s.c.e.EhCacheManagerFactoryBean-[Line:110]>Initializing EhCache CacheManager
2024-09-23 11:52:07.0183 [Jetty-Server] INFO  o.s.b.f.s.DefaultListableBeanFactory-[Line:598]>Pre-instantiating singletons in org.springframework.beans.factory.support.DefaultListableBeanFactory@1c4df44: defining beans [org.springframework.context.annotation.internalConfigurationAnnotationProcessor,org.springframework.context.annotation.internalAutowiredAnnotationProcessor,org.springframework.context.annotation.internalRequiredAnnotationProcessor,org.springframework.context.annotation.internalCommonAnnotationProcessor,accountCache,sendMailCache,banCache,gagCache,roleListCache,activationCreateHandler,banHandler,batchCreateRoleHandler,channelListHandler,channelSwitchHandler,createRoleHandler,debanHandler,degagHandler,gagHandler,getAllRebateListHandler,getAllServerListHandler,getAllYYPlatformRebateListHandler,getServerConfigInfoHandler,getServerConfigureHandler,getServerListHandler,getStrideServerHandler,loginYGSDKHandle,logonWithoutSDKHandler,queryBanHandler,queryRoleInfoHandler,queryX7IOSRoleInfoHandler,queryX7RoleInfoHandler,rechargeCallBackHandler,rechargeHandler,roleCreateLogHandler,sendMailHandler,serverLoginWhiteHandler,serverSwitchHandler,serverZoneHandler,YMNotifyHandler,gameCloseServlet,org.springframework.context.annotation.ConfigurationClassPostProcessor.importAwareProcessor]; root of factory hierarchy
2024-09-23 11:52:07.0324 [Jetty-Server] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ChannelInfoManager into StaticDataManager
2024-09-23 11:52:07.0361 [Jetty-Server] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerInfoManagerImpl into StaticDataManager
2024-09-23 11:52:07.0363 [Jetty-Server] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerZoneManager into StaticDataManager
2024-09-23 11:52:07.0552 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addActivationCreate] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0553 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addActivationCreate.*] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0553 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addActivationCreate/] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0554 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteActivationCreate] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0554 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteActivationCreate.*] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0554 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteActivationCreate/] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0554 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getActivationCreate] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0555 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getActivationCreate.*] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0555 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getActivationCreate/] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0555 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/RefreshActivation] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0555 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/RefreshActivation.*] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0556 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/RefreshActivation/] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0556 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/useCode] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0556 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/useCode.*] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0556 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/useCode/] onto handler 'activationCreateHandler'
2024-09-23 11:52:07.0557 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/banAdd] onto handler 'banHandler'
2024-09-23 11:52:07.0557 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/banAdd.*] onto handler 'banHandler'
2024-09-23 11:52:07.0557 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/banAdd/] onto handler 'banHandler'
2024-09-23 11:52:07.0558 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/query.do] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0558 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchCreateRole] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0558 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchCreateRole.*] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0559 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchCreateRole/] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0559 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/transpond.do] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0559 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/share.do] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0559 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/batchUpdata.do] onto handler 'batchCreateRoleHandler'
2024-09-23 11:52:07.0560 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/add.do] onto handler 'channelListHandler'
2024-09-23 11:52:07.0560 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/delete.do] onto handler 'channelListHandler'
2024-09-23 11:52:07.0569 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/getserverlist] onto handler 'channelListHandler'
2024-09-23 11:52:07.0569 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/getserverlist.*] onto handler 'channelListHandler'
2024-09-23 11:52:07.0570 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channelList/getserverlist/] onto handler 'channelListHandler'
2024-09-23 11:52:07.0570 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/update.do] onto handler 'channelSwitchHandler'
2024-09-23 11:52:07.0570 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/getserverlist] onto handler 'channelSwitchHandler'
2024-09-23 11:52:07.0571 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/getserverlist.*] onto handler 'channelSwitchHandler'
2024-09-23 11:52:07.0571 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/channel/switch/getserverlist/] onto handler 'channelSwitchHandler'
2024-09-23 11:52:07.0571 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/createRole] onto handler 'createRoleHandler'
2024-09-23 11:52:07.0572 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/createRole.*] onto handler 'createRoleHandler'
2024-09-23 11:52:07.0572 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/createRole/] onto handler 'createRoleHandler'
2024-09-23 11:52:07.0572 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/deletban] onto handler 'debanHandler'
2024-09-23 11:52:07.0572 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/deletban.*] onto handler 'debanHandler'
2024-09-23 11:52:07.0573 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/deletban/] onto handler 'debanHandler'
2024-09-23 11:52:07.0573 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/DelGAG] onto handler 'degagHandler'
2024-09-23 11:52:07.0574 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/DelGAG.*] onto handler 'degagHandler'
2024-09-23 11:52:07.0574 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/DelGAG/] onto handler 'degagHandler'
2024-09-23 11:52:07.0574 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/GAG] onto handler 'gagHandler'
2024-09-23 11:52:07.0575 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/GAG.*] onto handler 'gagHandler'
2024-09-23 11:52:07.0575 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/GAG/] onto handler 'gagHandler'
2024-09-23 11:52:07.0575 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/getGAG] onto handler 'gagHandler'
2024-09-23 11:52:07.0575 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/getGAG.*] onto handler 'gagHandler'
2024-09-23 11:52:07.0576 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/account/getGAG/] onto handler 'gagHandler'
2024-09-23 11:52:07.0576 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addRebate] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0576 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addRebate.*] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0576 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addRebate/] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0577 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteRebate] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0577 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteRebate.*] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0577 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteRebate/] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0577 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getRebatelist] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0577 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getRebatelist.*] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0578 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getRebatelist/] onto handler 'getAllRebateListHandler'
2024-09-23 11:52:07.0578 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addserverlist] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0578 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addserverlist.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0579 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addserverlist/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0579 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/update.do] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0579 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteserver] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0579 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteserver.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0579 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteserver/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0579 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reNameServer] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0580 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reNameServer.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0580 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reNameServer/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0591 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getserverlist] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0592 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getserverlist.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0592 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getserverlist/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0592 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reCombineServer] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0592 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reCombineServer.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0593 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/reCombineServer/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0593 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/refres_server.do] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0593 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getopenserver] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0593 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getopenserver.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0593 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getopenserver/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0594 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addopenlist] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0594 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addopenlist.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0594 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addopenlist/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0594 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/chat/getlist] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0595 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/chat/getlist.*] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0595 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/chat/getlist/] onto handler 'getAllServerListHandler'
2024-09-23 11:52:07.0595 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addYYPlatformGiftRebate] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0595 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addYYPlatformGiftRebate.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0596 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/addYYPlatformGiftRebate/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0596 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteYYPlatformGiftRebate] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0596 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteYYPlatformGiftRebate.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0596 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/deleteYYPlatformGiftRebate/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0597 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatformGiftRebatelist] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0597 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatformGiftRebatelist.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0597 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatformGiftRebatelist/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0597 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/doYYGift] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0597 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/doYYGift.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0598 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/doYYGift/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0598 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatGift] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0598 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatGift.*] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0598 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getYYPlatGift/] onto handler 'getAllYYPlatformRebateListHandler'
2024-09-23 11:52:07.0599 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/center/ServerInfo] onto handler 'getServerConfigInfoHandler'
2024-09-23 11:52:07.0599 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/center/ServerInfo.*] onto handler 'getServerConfigInfoHandler'
2024-09-23 11:52:07.0599 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/center/ServerInfo/] onto handler 'getServerConfigInfoHandler'
2024-09-23 11:52:07.0600 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/configure/add.do] onto handler 'getServerConfigureHandler'
2024-09-23 11:52:07.0600 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/configure/update.do] onto handler 'getServerConfigureHandler'
2024-09-23 11:52:07.0600 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/configure/list.do] onto handler 'getServerConfigureHandler'
2024-09-23 11:52:07.0601 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getServerInfoById] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0601 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getServerInfoById.*] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0601 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getServerInfoById/] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0601 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/updatestatus.do] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0602 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/serverlist] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0602 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/serverlist.*] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0602 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/serverlist/] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0602 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/getstatus.do] onto handler 'getServerListHandler'
2024-09-23 11:52:07.0603 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/get_stride_server.do] onto handler 'getStrideServerHandler'
2024-09-23 11:52:07.0603 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginCheck] onto handler 'loginYGSDKHandle'
2024-09-23 11:52:07.0604 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginCheck.*] onto handler 'loginYGSDKHandle'
2024-09-23 11:52:07.0604 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginCheck/] onto handler 'loginYGSDKHandle'
2024-09-23 11:52:07.0604 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginOutCheck] onto handler 'loginYGSDKHandle'
2024-09-23 11:52:07.0604 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginOutCheck.*] onto handler 'loginYGSDKHandle'
2024-09-23 11:52:07.0604 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginOutCheck/] onto handler 'loginYGSDKHandle'
2024-09-23 11:52:07.0605 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginWithOutSDK] onto handler 'logonWithoutSDKHandler'
2024-09-23 11:52:07.0605 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginWithOutSDK.*] onto handler 'logonWithoutSDKHandler'
2024-09-23 11:52:07.0605 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/loginWithOutSDK/] onto handler 'logonWithoutSDKHandler'
2024-09-23 11:52:07.0606 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/query/account/getbanInfo] onto handler 'queryBanHandler'
2024-09-23 11:52:07.0606 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/query/account/getbanInfo.*] onto handler 'queryBanHandler'
2024-09-23 11:52:07.0606 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/query/account/getbanInfo/] onto handler 'queryBanHandler'
2024-09-23 11:52:07.0606 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/operate/query-role.do] onto handler 'queryRoleInfoHandler'
2024-09-23 11:52:07.0607 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/operate/query-role-x7ios.do] onto handler 'queryX7IOSRoleInfoHandler'
2024-09-23 11:52:07.0607 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/operate/query-role-x7.do] onto handler 'queryX7RoleInfoHandler'
2024-09-23 11:52:07.0608 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/qkPlatfromCallback] onto handler 'rechargeCallBackHandler'
2024-09-23 11:52:07.0608 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/qkPlatfromCallback.*] onto handler 'rechargeCallBackHandler'
2024-09-23 11:52:07.0608 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/qkPlatfromCallback/] onto handler 'rechargeCallBackHandler'
2024-09-23 11:52:07.0609 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeUpdate] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0609 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeUpdate.*] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0609 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeUpdate/] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0609 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeAdd] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0609 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeAdd.*] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0610 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeAdd/] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0610 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeFind] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0610 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeFind.*] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0610 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/recharge/rechargeFind/] onto handler 'rechargeHandler'
2024-09-23 11:52:07.0611 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleAdd] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0611 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleAdd.*] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0611 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleAdd/] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0611 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleLogin] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0612 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleLogin.*] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0612 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/role/roleLogin/] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0612 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/daily/getlist] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0612 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/daily/getlist.*] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0613 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/daily/getlist/] onto handler 'roleCreateLogHandler'
2024-09-23 11:52:07.0613 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/sendMail.do] onto handler 'sendMailHandler'
2024-09-23 11:52:07.0613 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteAdd] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0613 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteAdd.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0614 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteAdd/] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0614 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteDelete] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0639 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteDelete.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0648 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteDelete/] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0648 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/existsN] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0649 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/existsN.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0649 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/existsN/] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0649 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/getserverlist] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0649 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/getserverlist.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0649 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_white/getserverlist/] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0650 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteList] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0650 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteList.*] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0650 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/login_whiteList/] onto handler 'serverLoginWhiteHandler'
2024-09-23 11:52:07.0651 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/updateserverstatus] onto handler 'serverSwitchHandler'
2024-09-23 11:52:07.0651 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/updateserverstatus.*] onto handler 'serverSwitchHandler'
2024-09-23 11:52:07.0651 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/updateserverstatus/] onto handler 'serverSwitchHandler'
2024-09-23 11:52:07.0651 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/getserverlist] onto handler 'serverSwitchHandler'
2024-09-23 11:52:07.0652 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/getserverlist.*] onto handler 'serverSwitchHandler'
2024-09-23 11:52:07.0652 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/switch/getserverlist/] onto handler 'serverSwitchHandler'
2024-09-23 11:52:07.0652 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/add.do] onto handler 'serverZoneHandler'
2024-09-23 11:52:07.0652 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/update.do] onto handler 'serverZoneHandler'
2024-09-23 11:52:07.0653 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/getserverlist] onto handler 'serverZoneHandler'
2024-09-23 11:52:07.0653 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/getserverlist.*] onto handler 'serverZoneHandler'
2024-09-23 11:52:07.0653 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/server/zone/getserverlist/] onto handler 'serverZoneHandler'
2024-09-23 11:52:07.0654 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/gameserver/notify_ym_sdk.do] onto handler 'YMNotifyHandler'
2024-09-23 11:52:07.0654 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/closeGame] onto handler 'gameCloseServlet'
2024-09-23 11:52:07.0654 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/closeGame.*] onto handler 'gameCloseServlet'
2024-09-23 11:52:07.0654 [Jetty-Server] INFO  o.s.w.s.m.a.DefaultAnnotationHandlerMapping-[Line:315]>Mapped URL path [/closeGame/] onto handler 'gameCloseServlet'
2024-09-23 11:52:08.0220 [Jetty-Server] INFO  o.s.web.servlet.DispatcherServlet-[Line:473]>FrameworkServlet 'baseServlet': initialization completed in 1311 ms
2024-09-23 11:52:08.0220 [Jetty-Server] INFO  o.e.j.server.handler.ContextHandler-[Line:744]>Started o.e.j.s.ServletContextHandler@46b515db{/,null,AVAILABLE}
2024-09-23 11:52:08.0302 [Jetty-Server] INFO  o.e.jetty.server.ServerConnector-[Line:266]>Started ServerConnector@28f52f5e{HTTP/1.1}{0.0.0.0:8501}
2024-09-23 11:52:08.0303 [Jetty-Server] INFO  org.eclipse.jetty.server.Server-[Line:379]>Started @3814ms
2024-09-23 11:52:08.0809 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.RebateInfoManagerImpl into StaticDataManager
2024-09-23 11:52:08.0817 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ActivationCreateManagerImpl into StaticDataManager
2024-09-23 11:52:08.0829 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ActivationInfoManagerImpl into StaticDataManager
2024-09-23 11:52:08.0843 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.DailyInfoUpdateQueue
2024-09-23 11:52:08.0852 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerConfigureManager into StaticDataManager
2024-09-23 11:52:08.0908 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.server.list.ServerWhiteIpManager into StaticDataManager
2024-09-23 11:52:08.0919 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.AccountDBQueue
2024-09-23 11:52:08.0929 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.RoleInfoUpdateQueue
2024-09-23 11:52:08.0940 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mbean.BanDBQueue
2024-09-23 11:52:08.0948 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mbean.GagDBQueue
2024-09-23 11:52:08.0960 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mail.ActivityMailQueue
2024-09-23 11:52:08.0964 [main] INFO  c.p.database.queue.DBQueueProvider-[Line:35]>regist database queue:com.playmore.auth.mail.SendMailDBQueue
2024-09-23 11:52:09.0000 [main] INFO  com.playmore.data.StaticDataManager-[Line:36]>add com.playmore.auth.yyplatform.YYPlatformRebateManagerImpl into StaticDataManager
2024-09-23 11:52:09.0073 [main] INFO  o.s.s.quartz.SchedulerFactoryBean-[Line:552]>Loading Quartz config from [class path resource [quartz.properties]]
2024-09-23 11:52:09.0087 [main] INFO  org.quartz.impl.StdSchedulerFactory-[Line:1184]>Using default implementation for ThreadExecutor
2024-09-23 11:52:09.0100 [main] INFO  o.quartz.core.SchedulerSignalerImpl-[Line:61]>Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2024-09-23 11:52:09.0101 [main] INFO  org.quartz.core.QuartzScheduler-[Line:240]>Quartz Scheduler v.2.2.1 created.
2024-09-23 11:52:09.0102 [main] INFO  org.quartz.simpl.RAMJobStore-[Line:155]>RAMJobStore initialized.
2024-09-23 11:52:09.0103 [main] INFO  org.quartz.core.QuartzScheduler-[Line:305]>Scheduler meta-data: Quartz Scheduler (v2.2.1) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 2 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2024-09-23 11:52:09.0104 [main] INFO  org.quartz.impl.StdSchedulerFactory-[Line:1339]>Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2024-09-23 11:52:09.0104 [main] INFO  org.quartz.impl.StdSchedulerFactory-[Line:1343]>Quartz scheduler version: 2.2.1
2024-09-23 11:52:09.0104 [main] INFO  org.quartz.core.QuartzScheduler-[Line:2311]>JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6548bb7d
2024-09-23 11:52:09.0142 [main] INFO  o.s.c.s.DefaultLifecycleProcessor-[Line:334]>Starting beans in phase 2147483647
2024-09-23 11:52:09.0142 [main] INFO  o.s.s.quartz.SchedulerFactoryBean-[Line:648]>Starting Quartz Scheduler now
2024-09-23 11:52:09.0143 [main] INFO  org.quartz.core.QuartzScheduler-[Line:575]>Scheduler quartzScheduler_$_NON_CLUSTERED started.
2024-09-23 11:52:09.0143 [main] INFO  c.p.http.jetty.SpringContextLoader-[Line:70]>SERVER START COMPLETE..... 
2024-09-23 11:53:30.0133 [Jetty-QTP-15] INFO  c.p.servlet.GetServerListHandler-[Line:156]>账号验证成功251030
