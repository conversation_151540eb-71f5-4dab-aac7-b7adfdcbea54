package indigo;

import Message.S2CIndigoMsg.GetRaceInfosRsp;
import game.core.pub.script.IScript;
import game.route.indigo.IndigoService;
import game.route.server.ServerService;
import game.route.server.domain.GameServer;
import game.route.util.ScriptArgs;
import game.route.util.ScriptArgs.Key;


/**
 *  获取比赛文本信息
 * <AUTHOR>
 *
 * 2018年10月9日
 */
public class GetRaceInfosScript implements IScript {

	@Override
	public void init() {
		// TODO Auto-generated method stub

	}

	@Override
	public void destroy() {
		// TODO Auto-generated method stub

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		long playerId = (long) args.get(Key.ARG1);
		GameServer server = (GameServer) args.get(Key.ARG2);
		int startIndex = (int) args.get(Key.ARG3);
		int size = (int) args.get(Key.ARG4);
		Integer groupId = ServerService.getInstance().getGroupIdByServer(server.getId());

		GetRaceInfosRsp.Builder builder = GetRaceInfosRsp.newBuilder();
		int raceSize = IndigoService.getInstance().getRaceInfoList().size();
		startIndex = startIndex - 1;
		startIndex = startIndex < 0 ? 0 : startIndex;
		startIndex = startIndex > raceSize ? 0 : startIndex;
		for (int i = startIndex; i < size; i++) {
			if (raceSize <= i) {
				break;
			}
			builder.addRaceInfo(IndigoService.getInstance().getRaceInfoList().get(groupId).get(i));
		}
		// MessageUtils.send(player,
		// player.getFactory().fetchSMessage(GetRaceInfosRspID.GetRaceInfosRspMsgID_VALUE,
		// builder.build().toByteArray()));

		return null;
	}

}
