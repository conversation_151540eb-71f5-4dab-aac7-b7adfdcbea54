[2024-09-23 14:00:56:121 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-23 14:00:56:693 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-23 14:00:56:722 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-23 14:00:56:723 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-23 14:00:58:336 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-23 14:00:59:745 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-23 14:00:59:746 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-23 14:00:59:983 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-23 14:00:59:985 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-23 14:01:00:014 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-23 14:01:00:260 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-23 14:01:00:637 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-23 14:01:33:500 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:**************,cmd:2
[2024-09-23 14:01:40:151 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/**************:39001
[2024-09-23 14:01:40:152 INFO](CommunicationC.java:198)connect success, ip: [**************], port = 39001
[2024-09-23 14:01:40:165 INFO](TcpClient.java:86)[游戏服][主公一区][**************:39001]connect succ
[2024-09-23 14:01:40:201 INFO](AuthenticationHandler.java:35)connect serverIp:**************---主公一区,authErrorCode:0
[2024-09-23 14:52:46:462 ERROR](ScriptJavaLoader.java:251)非IScript的实现, scriptName:DelayedStopServer
[2024-09-23 14:52:47:126 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean扫描完成!> ]
[2024-09-23 14:52:47:157 ERROR](ServerConfig.java:120)unknow node [route-server-id]
[2024-09-23 14:52:47:158 ERROR](ServerConfig.java:120)unknow node [io-processor-number]
[2024-09-23 14:52:48:800 INFO](GameDataManager.java:387)Start load all game data ...
[2024-09-23 14:52:50:254 INFO](ServerConfig.java:81)loadGameData finish
[2024-09-23 14:52:50:256 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean InitBefore 方法执行完成!> ]
[2024-09-23 14:52:50:446 ERROR](PrintLogs.java:48)[日志 : game.core.pub.util.bean.SingleBeanFactory massage : <单例bean Init 方法执行完成!> ]
[2024-09-23 14:52:50:448 INFO](DatabaseProcessor.java:46)GameDBOperator starting
[2024-09-23 14:52:50:466 INFO](GameHttpServer.java:88)HttpServer开始监听: /0:0:0:0:0:0:0:0:8601/
[2024-09-23 14:52:50:743 INFO](IndigoService.java:814)今日  跨服三国霸主   决赛阶段------------------------休赛日
[2024-09-23 14:52:51:107 INFO](SuffleService.java:308)单兵大作战更表
[2024-09-23 14:53:24:742 INFO](RouteHttpServerImpl.java:82)HTTP请求，ip:**************,cmd:2
[2024-09-23 14:53:30:645 INFO](TcpClient.java:130)sessionCreate, sessionId:2,ip:/**************:39001
[2024-09-23 14:53:30:646 INFO](CommunicationC.java:198)connect success, ip: [**************], port = 39001
[2024-09-23 14:53:30:660 INFO](TcpClient.java:86)[游戏服][主公一区][**************:39001]connect succ
[2024-09-23 14:53:30:682 INFO](AuthenticationHandler.java:35)connect serverIp:**************---主公一区,authErrorCode:0
