package logic.crossIndigo;

import java.util.ArrayList;
import java.util.List;

import org.apache.log4j.Logger;

import Message.S2CPlayerMsg.PlayerType;
import data.bean.t_arena_aitempletBean;
import game.core.pub.script.IScript;
import game.server.db.game.bean.PlayerBean;
import game.server.db.game.dao.PlayerDao;
import game.server.logic.arena.RobotService;
import game.server.logic.crossIndigo.CrossIndigoService;
import game.server.logic.player.Player;
import game.server.logic.player.RoleViewService;
import game.server.logic.util.BeanTemplet;
import game.server.logic.util.ScriptArgs;

/**
 * 跨服三国争霸机器人
 * 
 * <AUTHOR>
 * @date 2018年10月8日
 */
public class CrossIndigoRobotScript implements IScript {
	private static final Logger LOGGER = Logger.getLogger(CrossIndigoRobotScript.class);

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {

		ScriptArgs args = (ScriptArgs) arg;

		int level = (int) args.get(ScriptArgs.Key.ARG1);
		int num = (int) args.get(ScriptArgs.Key.ARG2);

		List<PlayerBean> list = new ArrayList<>();
		t_arena_aitempletBean aitempletBean = BeanTemplet
				.getArenaAitempletBean(BeanTemplet.getGlobalBean(410).getInt_value());// ID mẫu robot
		RobotService.generateRobot(list, aitempletBean, PlayerType.CROSS_INDIGO_ROBOT_VALUE, num, level);
		// Lưu hàng loạt
		PlayerDao.batchInsert(list);
		for (PlayerBean playerBean : list) {
			Player player = new Player();
			player.loadInitialize(null, playerBean);
			if (player.getPlayerId() == 0) {
				LOGGER.info("Tạo =====Liên server===Robot chung kết thạch anh------player :" + player.getPlayerId());
				continue;
			}
			RoleViewService.putRoleView(player.toRoleView());
			// Đăng ký chung kết liên server

			// Đăng ký robot chung kết
			CrossIndigoService.getInstance().applyRobot(player);

		}
		LOGGER.info("Tạo =====Liên server===Robot chung kết thạch anh hoàn tất------số lượng:" + num);
		return null;
	}

}
