package logic.bug;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import game.core.pub.script.IScript;
import game.server.logic.player.Player;
import game.server.logic.player.PlayerManager;
import game.server.logic.player.RoleViewService;


/**
 * Đặt lại xếp hạng huấn luyện
 * <AUTHOR>
 *
 */
public class ResetPlayerTrain implements IScript{
	
	private Logger logger = LoggerFactory.getLogger(this.getClass());

	@Override
	public void init() {
		
	}

	@Override
	public void destroy() {
		
	}

	@Override
	public Object call(String scriptName, Object arg) {
		for (Long pid : RoleViewService.getAllPlayerId())
		{
			Player player = PlayerManager.getOffLinePlayerByPlayerId(pid);
			player.getHeroManager().setTrainLvl(0);
			player.getHeroManager().setTrainValueMax(0);
			logger.error("Người chơi:" + player.getPlayerId() + ",đã đặt lại dữ liệu xếp hạng huấn luyện");
			if (!PlayerManager.isPlayerOnline(pid)) {// Online gửi đến GameLine xử lý
				player.offLineSave();
			}
		}
		return null;
	}

}
