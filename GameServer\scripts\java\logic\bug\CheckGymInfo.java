package logic.bug;

import java.util.Date;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

import data.bean.t_roadPavilionBean;
import game.core.pub.script.IScript;
import game.server.logic.snatchTerritory.SnatchTerritoryService;
import game.server.logic.snatchTerritory.bean.SnatchTerritory;
import game.server.logic.util.BeanTemplet;

public class CheckGymInfo implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		Map<Integer, SnatchTerritory> map = SnatchTerritoryService.getInstance().getTerritoryMap();
		Iterator<Entry<Integer, SnatchTerritory>> iterator = map.entrySet().iterator();
		while (iterator.hasNext()) {
			Entry<Integer, SnatchTerritory> entry = iterator.next();
			System.err.println("   -----> id " + entry.getKey());
			System.err.println("   -----> info : ");
			SnatchTerritory tory = entry.getValue();
			t_roadPavilionBean road = BeanTemplet.getRoadPavilionBean(entry.getKey());
			System.err.println("   ----------> name : " + road.getName());
			System.err.println("   ----------> leaderName : "
					+ (null == tory.getGymLeader() ? "không có quán chủ" : tory.getGymLeader().getName()));
			System.err.println("   ----------> ID liên minh tấn công : " + tory.getOffenseGuildId());
			System.err.println("   ----------> ID người tuyên chiến : " + tory.getAttackPlayerId());
			System.err.println("   ----------> ID liên minh phòng thủ : " + tory.getDefendGuildId());
			System.err.println("   ----------> Số người bên tấn công : " + tory.getOffenseList().size());
			System.err.println("   ----------> Số người bên phòng thủ : " + tory.getDefendList().size());
			System.err.println("   ----------> Thời gian trạng thái : " + new Date(tory.getCountdown()).toString());
			System.err.println("   ----------> Trạng thái : " + tory.getStatus());
			if (tory.getCountdown() < System.currentTimeMillis()) {
				SnatchTerritoryService.getInstance().snatchTerritoryStartFight(tory);
			}

		}
		return null;
	}

}
