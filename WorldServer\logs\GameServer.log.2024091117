[2024-09-11 17:25:17:390 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:25:17:590 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:25:27:667 INFO](TcpClient.java:130)sessionCreate, sessionId:169,ip:/*************:39001
[2024-09-11 17:25:27:671 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:25:27:693 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:25:27:709 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:25:37:764 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:25:37:771 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:25:47:773 INFO](TcpClient.java:130)sessionCreate, sessionId:170,ip:/*************:39001
[2024-09-11 17:25:47:774 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:25:47:775 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:25:47:778 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:25:57:842 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:25:57:848 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:26:07:850 INFO](TcpClient.java:130)sessionCreate, sessionId:171,ip:/*************:39001
[2024-09-11 17:26:07:851 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:26:07:852 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:26:07:856 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:26:17:923 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:26:17:930 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:26:27:933 INFO](TcpClient.java:130)sessionCreate, sessionId:172,ip:/*************:39001
[2024-09-11 17:26:27:950 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:26:27:974 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:26:27:978 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:26:38:040 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:26:38:045 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:26:48:050 INFO](TcpClient.java:130)sessionCreate, sessionId:173,ip:/*************:39001
[2024-09-11 17:26:48:050 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:26:48:051 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:26:48:055 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:26:58:120 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:26:58:123 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:27:08:125 INFO](TcpClient.java:130)sessionCreate, sessionId:174,ip:/*************:39001
[2024-09-11 17:27:08:125 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:27:08:126 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:27:08:130 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:27:18:198 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:27:18:199 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:27:28:201 INFO](TcpClient.java:130)sessionCreate, sessionId:175,ip:/*************:39001
[2024-09-11 17:27:28:202 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:27:28:203 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:27:28:207 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:27:38:273 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:27:38:275 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:27:48:278 INFO](TcpClient.java:130)sessionCreate, sessionId:176,ip:/*************:39001
[2024-09-11 17:27:48:280 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:27:48:281 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:27:48:288 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:27:57:807 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:27:57:810 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:28:07:812 INFO](TcpClient.java:130)sessionCreate, sessionId:177,ip:/*************:39001
[2024-09-11 17:28:07:815 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:28:07:816 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:28:07:819 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:28:31:625 ERROR](ServerService.java:291)网络波动导致心跳终端,服务器重连:稀有一区
[2024-09-11 17:28:31:643 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:28:41:644 INFO](TcpClient.java:130)sessionCreate, sessionId:178,ip:/*************:39001
[2024-09-11 17:28:41:647 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:28:41:647 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:28:41:647 ERROR](TcpClient.java:77)repeat connect[游戏服][稀有一区][*************:39001]
[2024-09-11 17:28:41:652 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:29:02:326 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:29:02:332 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:29:12:334 INFO](TcpClient.java:130)sessionCreate, sessionId:179,ip:/*************:39001
[2024-09-11 17:29:12:336 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:29:12:338 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:29:12:341 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:29:22:409 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:29:22:411 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:29:32:414 INFO](TcpClient.java:130)sessionCreate, sessionId:180,ip:/*************:39001
[2024-09-11 17:29:32:414 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:29:32:416 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:29:32:421 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:29:42:483 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:29:42:489 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:29:52:491 INFO](TcpClient.java:130)sessionCreate, sessionId:181,ip:/*************:39001
[2024-09-11 17:29:52:491 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:29:52:508 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:29:52:516 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:30:02:585 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:30:02:590 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:30:12:595 INFO](TcpClient.java:130)sessionCreate, sessionId:182,ip:/*************:39001
[2024-09-11 17:30:12:597 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:30:12:599 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:30:12:606 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:30:22:668 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:30:22:671 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:30:32:676 INFO](TcpClient.java:130)sessionCreate, sessionId:183,ip:/*************:39001
[2024-09-11 17:30:32:677 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:30:32:681 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:30:32:687 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:30:42:750 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:30:42:752 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:30:52:757 INFO](TcpClient.java:130)sessionCreate, sessionId:184,ip:/*************:39001
[2024-09-11 17:30:52:757 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:30:52:758 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:30:52:762 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
[2024-09-11 17:31:02:824 ERROR](TcpClient.java:169)session exceptionCaught:java.io.IOException: Connection reset by peer
[2024-09-11 17:31:02:826 ERROR](TcpClient.java:151)session closed:session closed:[游戏服][稀有一区][*************:39001]
[2024-09-11 17:31:12:830 INFO](TcpClient.java:130)sessionCreate, sessionId:185,ip:/*************:39001
[2024-09-11 17:31:12:830 INFO](CommunicationC.java:198)connect success, ip: [*************], port = 39001
[2024-09-11 17:31:12:842 INFO](TcpClient.java:86)[游戏服][稀有一区][*************:39001]connect succ
[2024-09-11 17:31:12:846 INFO](AuthenticationHandler.java:35)connect serverIp:*************---稀有一区,authErrorCode:0
