package logic.crossArena;

import Message.S2CArenaMsg;
import Message.S2CCrossArenaMsg.CrossEnemyMsg;
import Message.S2CCrossArenaMsg;
import Message.S2CArenaMsg.EnemyMsg;
import Message.S2CPlayerMsg;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;

import game.core.pub.script.IScript;
import game.server.logic.player.Player;
import game.server.logic.util.ScriptArgs;
import game.server.util.MessageUtils;
import Message.Inner.GRCrossArena.GRAGetDataRsp;
import Message.Inner.GRCrossArena.GRAPlayerInfoReq;
import Message.S2CPlayerMsg.GetPlayerInfoRsp;
import Message.S2CCrossArenaMsg.GetCrossArenaRsp;
import Message.S2CCrossArenaMsg.CrossArenaMsg;

public class GetCrossArenaDataScript implements IScript {

	@Override
	public void init() {

	}

	@Override
	public void destroy() {

	}

	@Override
	public Object call(String scriptName, Object arg) {
		ScriptArgs args = (ScriptArgs) arg;
		Player player = (Player) args.get(ScriptArgs.Key.PLAYER);
		GRAGetDataRsp getData = (GRAGetDataRsp) args.get(ScriptArgs.Key.ARG1);
		// 封装协议准备下发数据
		GetCrossArenaRsp.Builder builder = GetCrossArenaRsp.newBuilder();
		CrossArenaMsg.Builder cbuilder = CrossArenaMsg.newBuilder();
		cbuilder.setArenaScore(getData.getArenaScore());
		cbuilder.setDayChallengeNum(getData.getDayChallengeNum());
		cbuilder.setBuyDayChallengeNum(getData.getBuyDayChallengeNum());
		cbuilder.setDayrefreshEnemyNum(getData.getDayrefreshEnemyNum());
		cbuilder.setChallengeCD(getData.getChallengeCD());
		cbuilder.setLastGetReportTime(0);
		cbuilder.setCurRank(getData.getCurRank());
		cbuilder.setRankHighest(getData.getRankHighest());
		cbuilder.addAllScoreReward(getData.getScoreRewardList());
		cbuilder.addAllRankHighestReward(getData.getRankHighestRewardList());
		for(ByteString byteString:getData.getEnemyListList()) {
			try {
				CrossEnemyMsg cenemyMsg = CrossEnemyMsg.parseFrom(byteString);
				EnemyMsg.Builder enemyMsg = EnemyMsg.newBuilder();
				enemyMsg.setPlayerId(cenemyMsg.getPlayerId());
				enemyMsg.setRank(cenemyMsg.getRank());
				enemyMsg.setPower(cenemyMsg.getPower());
				enemyMsg.setName(cenemyMsg.getName());
				enemyMsg.setHeroId(cenemyMsg.getHeroId());
				enemyMsg.setRobot(cenemyMsg.getRobot());
				enemyMsg.setViewCard(cenemyMsg.getViewCard());
				enemyMsg.setManifesto(cenemyMsg.getManifesto());
               cbuilder.addEnemyList(enemyMsg);
			} catch (InvalidProtocolBufferException e) {
				e.printStackTrace();
			}
		}
		cbuilder.addAllReports(getData.getReportsList());
		cbuilder.setManifesto(getData.getManifesto());
		builder.setArana(cbuilder);
		// 推送
		MessageUtils.send(player, player.getFactory().fetchSMessage(S2CCrossArenaMsg.GetCrossArenaRspID.GetCrossArenaRspMsgID_VALUE,
				builder.build().toByteArray()));
		return null;
	}

}
